# AI数字营销应用系统

基于AI技术的智能数字营销平台，提供内容生成、用户画像分析、营销自动化等功能。

## 🚀 项目概述

本项目是一个现代化的AI驱动数字营销平台，采用微服务架构，支持高并发、高可用性，具备良好的可维护性和扩展性。

### 核心功能

- **AI内容生成** - 智能生成营销文案、图片和视频内容
- **用户画像分析** - 深度分析用户行为数据，构建精准用户画像
- **营销自动化** - 设计智能营销流程，自动执行营销活动
- **数据分析报告** - 实时监控营销效果，生成详细分析报告
- **多渠道营销** - 统一管理邮件、短信、社交媒体等营销渠道
- **支付订阅** - 完整的订阅管理和支付处理系统

### 技术栈

#### 前端
- **框架**: Next.js 15 (React 19)
- **样式**: Tailwind CSS + Shadcn/ui
- **状态管理**: Zustand
- **数据获取**: TanStack Query
- **表单处理**: React Hook Form + Zod

#### 后端
- **运行时**: Node.js 20+
- **框架**: Fastify
- **语言**: TypeScript
- **数据库**: PostgreSQL + Redis + MongoDB
- **搜索**: Elasticsearch
- **队列**: Bull Queue

#### DevOps
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes
- **CI/CD**: GitHub Actions
- **监控**: Prometheus + Grafana

## 📋 系统要求

- Node.js 20.0.0+
- Docker 20.0.0+
- Docker Compose 2.0.0+
- PostgreSQL 15+
- Redis 7+
- 至少 8GB RAM
- 至少 20GB 可用磁盘空间

## 🛠️ 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-org/ai-digital-marketing.git
cd ai-digital-marketing
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件，填入实际配置
vim .env
```

### 3. 安装依赖

```bash
# 安装项目依赖
npm install

# 生成Prisma客户端
npm run db:generate
```

### 4. 启动开发环境

```bash
# 启动所有服务（数据库、缓存、API、前端）
docker-compose up -d

# 或者分别启动
npm run dev:api    # 启动API服务
npm run dev:web    # 启动前端应用
```

### 5. 数据库初始化

```bash
# 运行数据库迁移
npm run db:migrate

# 填充初始数据
npm run db:seed
```

### 6. 访问应用

- **前端应用**: http://localhost:3000
- **API文档**: http://localhost:3001/docs
- **数据库管理**: http://localhost:5555 (Prisma Studio)
- **监控面板**: http://localhost:3003 (Grafana)

## 📁 项目结构

```
ai-digital-marketing/
├── apps/                          # 应用目录
│   ├── web/                       # Next.js 前端应用
│   │   ├── app/                   # App Router 页面
│   │   ├── components/            # React 组件
│   │   ├── lib/                   # 工具函数
│   │   └── hooks/                 # 自定义 Hooks
│   └── api/                       # Fastify 后端API
│       ├── src/                   # 源代码
│       ├── prisma/                # 数据库模式
│       └── tests/                 # 测试文件
├── packages/                      # 共享包
│   ├── ui/                        # UI组件库
│   ├── config/                    # 配置包
│   └── utils/                     # 工具函数包
├── docs/                          # 项目文档
│   ├── requirements.md            # 需求文档
│   ├── system-design.md           # 系统设计文档
│   ├── feature-list.md            # 功能清单
│   └── todo-list.md               # TODO列表
├── scripts/                       # 脚本文件
├── nginx/                         # Nginx配置
├── monitoring/                    # 监控配置
├── docker-compose.yml             # Docker编排文件
├── turbo.json                     # Turborepo配置
└── package.json                   # 项目配置
```

## 📞 联系我们

- **项目主页**: https://github.com/your-org/ai-digital-marketing
- **问题反馈**: https://github.com/your-org/ai-digital-marketing/issues
- **邮箱**: <EMAIL>

---

**AI数字营销平台** - 让营销更智能，让增长更简单 🚀

