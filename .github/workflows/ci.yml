# AI数字营销平台 CI/CD 流水线配置
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  lint-and-test:
    name: 代码检查和测试
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 代码格式检查
        run: npm run format:check

      - name: 代码质量检查
        run: npm run lint

      - name: 类型检查
        run: npm run type-check

      - name: 运行测试
        run: npm run test:coverage
        env:
          NODE_ENV: test

      - name: 上传测试覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # 构建检查
  build:
    name: 构建检查
    runs-on: ubuntu-latest
    needs: lint-and-test
    
    strategy:
      matrix:
        app: [web, api]
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 构建应用
        run: npm run build --workspace=apps/${{ matrix.app }}

      - name: 缓存构建产物
        uses: actions/cache@v3
        with:
          path: |
            apps/${{ matrix.app }}/dist
            apps/${{ matrix.app }}/.next
          key: ${{ runner.os }}-build-${{ matrix.app }}-${{ github.sha }}

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: lint-and-test
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 运行 Trivy 漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传 Trivy 扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: 依赖安全审计
        run: npm audit --audit-level=high

  # Docker 镜像构建
  docker-build:
    name: Docker 镜像构建
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.event_name == 'push'
    
    strategy:
      matrix:
        app: [web, api]
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.app }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 构建并推送 Docker 镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/${{ matrix.app }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 部署到开发环境
  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: 配置 kubeconfig
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBE_CONFIG_DEV }}" | base64 -d > $HOME/.kube/config

      - name: 部署到 Kubernetes
        run: |
          kubectl set image deployment/ai-marketing-web web=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-web:develop-${{ github.sha }} -n ai-marketing-dev
          kubectl set image deployment/ai-marketing-api api=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:develop-${{ github.sha }} -n ai-marketing-dev
          kubectl rollout status deployment/ai-marketing-web -n ai-marketing-dev
          kubectl rollout status deployment/ai-marketing-api -n ai-marketing-dev

      - name: 运行健康检查
        run: |
          kubectl wait --for=condition=ready pod -l app=ai-marketing-web -n ai-marketing-dev --timeout=300s
          kubectl wait --for=condition=ready pod -l app=ai-marketing-api -n ai-marketing-dev --timeout=300s

  # 部署到生产环境
  deploy-prod:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: 配置 kubeconfig
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBE_CONFIG_PROD }}" | base64 -d > $HOME/.kube/config

      - name: 部署到 Kubernetes
        run: |
          kubectl set image deployment/ai-marketing-web web=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-web:main-${{ github.sha }} -n ai-marketing-prod
          kubectl set image deployment/ai-marketing-api api=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:main-${{ github.sha }} -n ai-marketing-prod
          kubectl rollout status deployment/ai-marketing-web -n ai-marketing-prod
          kubectl rollout status deployment/ai-marketing-api -n ai-marketing-prod

      - name: 运行健康检查
        run: |
          kubectl wait --for=condition=ready pod -l app=ai-marketing-web -n ai-marketing-prod --timeout=300s
          kubectl wait --for=condition=ready pod -l app=ai-marketing-api -n ai-marketing-prod --timeout=300s

      - name: 发送部署通知
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

  # 性能测试
  performance-test:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: deploy-dev
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 运行 Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './lighthouse.config.js'
          uploadArtifacts: true
          temporaryPublicStorage: true

      - name: 运行负载测试
        run: |
          npm install -g artillery
          artillery run tests/load/api-load-test.yml

  # 端到端测试
  e2e-test:
    name: 端到端测试
    runs-on: ubuntu-latest
    needs: deploy-dev
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 安装 Playwright
        run: npx playwright install --with-deps

      - name: 运行 E2E 测试
        run: npm run test:e2e
        env:
          BASE_URL: https://dev.ai-marketing.com

      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
