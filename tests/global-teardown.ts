// Jest 全局清理文件
// 在所有测试结束后运行

/**
 * 全局测试清理
 * 清理测试环境、关闭数据库连接等
 */
export default async function globalTeardown() {
  console.log('🧹 开始全局测试清理...')

  try {
    // 清理测试数据库
    await cleanupTestDatabase()

    // 关闭数据库连接
    await closeDatabaseConnections()

    // 清理临时文件
    await cleanupTempFiles()

    // 清理缓存
    await cleanupCache()

    console.log('✅ 全局测试清理完成')
  } catch (error) {
    console.error('❌ 全局测试清理失败:', error)
    // 不抛出错误，避免影响测试结果
  }
}

/**
 * 清理测试数据库
 */
async function cleanupTestDatabase() {
  console.log('🗄️ 清理测试数据库...')
  
  try {
    // 这里可以添加清理测试数据库的逻辑
    // 例如删除所有测试数据，重置数据库状态
    console.log('✅ 测试数据库清理完成')
  } catch (error) {
    console.error('❌ 测试数据库清理失败:', error)
  }
}

/**
 * 关闭数据库连接
 */
async function closeDatabaseConnections() {
  console.log('🔌 关闭数据库连接...')
  
  try {
    // 这里可以添加关闭数据库连接的逻辑
    // 例如关闭 Prisma 客户端连接
    console.log('✅ 数据库连接已关闭')
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error)
  }
}

/**
 * 清理临时文件
 */
async function cleanupTempFiles() {
  console.log('📁 清理临时文件...')
  
  try {
    const fs = require('fs').promises
    const path = require('path')
    
    // 清理测试上传文件
    const testUploadsDir = path.join(process.cwd(), 'test-uploads')
    try {
      await fs.rmdir(testUploadsDir, { recursive: true })
    } catch (error) {
      // 目录不存在时忽略错误
    }
    
    // 清理测试日志文件
    const testLogsDir = path.join(process.cwd(), 'test-logs')
    try {
      await fs.rmdir(testLogsDir, { recursive: true })
    } catch (error) {
      // 目录不存在时忽略错误
    }
    
    console.log('✅ 临时文件清理完成')
  } catch (error) {
    console.error('❌ 临时文件清理失败:', error)
  }
}

/**
 * 清理缓存
 */
async function cleanupCache() {
  console.log('🗂️ 清理缓存...')
  
  try {
    // 这里可以添加清理缓存的逻辑
    // 例如清理 Redis 测试缓存
    console.log('✅ 缓存清理完成')
  } catch (error) {
    console.error('❌ 缓存清理失败:', error)
  }
}
