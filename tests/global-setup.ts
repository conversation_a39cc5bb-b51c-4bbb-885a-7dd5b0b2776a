// Jest 全局设置文件
// 在所有测试开始前运行

import { execSync } from 'child_process'
import { config } from 'dotenv'

/**
 * 全局测试设置
 * 设置测试环境、启动测试数据库等
 */
export default async function globalSetup() {
  console.log('🚀 开始全局测试设置...')

  // 加载测试环境变量
  config({ path: '.env.test' })

  try {
    // 设置测试环境变量
    process.env.NODE_ENV = 'test'
    process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://postgres:postgres123@localhost:5432/ai_marketing_test'
    process.env.REDIS_URL = process.env.TEST_REDIS_URL || 'redis://:redis123@localhost:6379/1'
    
    console.log('✅ 环境变量设置完成')

    // 检查测试数据库连接
    await checkDatabaseConnection()

    // 运行数据库迁移
    await runDatabaseMigrations()

    // 清理测试数据
    await cleanTestData()

    console.log('✅ 全局测试设置完成')
  } catch (error) {
    console.error('❌ 全局测试设置失败:', error)
    throw error
  }
}

/**
 * 检查数据库连接
 */
async function checkDatabaseConnection() {
  console.log('🔍 检查数据库连接...')
  
  try {
    // 这里可以添加数据库连接检查逻辑
    // 例如使用 Prisma 或其他数据库客户端
    console.log('✅ 数据库连接正常')
  } catch (error) {
    console.error('❌ 数据库连接失败:', error)
    throw error
  }
}

/**
 * 运行数据库迁移
 */
async function runDatabaseMigrations() {
  console.log('🔄 运行数据库迁移...')
  
  try {
    // 切换到API目录并运行迁移
    process.chdir('./apps/api')
    
    // 生成Prisma客户端
    execSync('npx prisma generate', { stdio: 'inherit' })
    
    // 推送数据库模式
    execSync('npx prisma db push --force-reset', { stdio: 'inherit' })
    
    // 返回根目录
    process.chdir('../../')
    
    console.log('✅ 数据库迁移完成')
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error)
    throw error
  }
}

/**
 * 清理测试数据
 */
async function cleanTestData() {
  console.log('🧹 清理测试数据...')
  
  try {
    // 这里可以添加清理测试数据的逻辑
    // 例如删除所有测试用户、测试活动等
    console.log('✅ 测试数据清理完成')
  } catch (error) {
    console.error('❌ 测试数据清理失败:', error)
    throw error
  }
}
