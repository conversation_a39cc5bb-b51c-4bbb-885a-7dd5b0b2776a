# Artillery 负载测试配置文件
# 用于API性能和负载测试

config:
  # 测试目标
  target: 'http://localhost:3001'
  
  # 测试阶段配置
  phases:
    # 预热阶段
    - duration: 60
      arrivalRate: 5
      name: "预热阶段"
    
    # 负载递增阶段
    - duration: 120
      arrivalRate: 5
      rampTo: 50
      name: "负载递增阶段"
    
    # 高负载维持阶段
    - duration: 300
      arrivalRate: 50
      name: "高负载维持阶段"
    
    # 峰值负载测试
    - duration: 60
      arrivalRate: 100
      name: "峰值负载测试"
    
    # 负载递减阶段
    - duration: 120
      arrivalRate: 100
      rampTo: 5
      name: "负载递减阶段"

  # 默认配置
  defaults:
    headers:
      'Content-Type': 'application/json'
      'User-Agent': 'Artillery Load Test'
  
  # 插件配置
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true
    
  # 处理器
  processor: './tests/load/processors.js'

# 测试场景
scenarios:
  # 健康检查测试
  - name: "健康检查"
    weight: 10
    flow:
      - get:
          url: "/health"
          name: "健康检查"
          expect:
            - statusCode: 200

  # 用户认证流程测试
  - name: "用户认证流程"
    weight: 30
    flow:
      # 用户注册
      - post:
          url: "/api/v1/auth/register"
          name: "用户注册"
          json:
            email: "{{ $randomEmail() }}"
            password: "Test123456"
            firstName: "测试"
            lastName: "用户"
          capture:
            - json: "$.data.user.id"
              as: "userId"
          expect:
            - statusCode: 201

      # 用户登录
      - post:
          url: "/api/v1/auth/login"
          name: "用户登录"
          json:
            email: "{{ email }}"
            password: "Test123456"
          capture:
            - json: "$.data.accessToken"
              as: "accessToken"
          expect:
            - statusCode: 200

      # 获取用户信息
      - get:
          url: "/api/v1/users/profile"
          name: "获取用户信息"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          expect:
            - statusCode: 200

  # AI内容生成测试
  - name: "AI内容生成"
    weight: 25
    flow:
      # 先登录获取token
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "<EMAIL>"
            password: "test123"
          capture:
            - json: "$.data.accessToken"
              as: "accessToken"

      # 生成营销文案
      - post:
          url: "/api/v1/ai/generate-text"
          name: "生成营销文案"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          json:
            prompt: "为一款智能手机写一段营销文案"
            type: "marketing_copy"
            tone: "professional"
            length: "medium"
          expect:
            - statusCode: 200

      # 获取生成历史
      - get:
          url: "/api/v1/ai/history"
          name: "获取生成历史"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          expect:
            - statusCode: 200

  # 营销活动管理测试
  - name: "营销活动管理"
    weight: 20
    flow:
      # 先登录
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "<EMAIL>"
            password: "test123"
          capture:
            - json: "$.data.accessToken"
              as: "accessToken"

      # 创建营销活动
      - post:
          url: "/api/v1/campaigns"
          name: "创建营销活动"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          json:
            name: "测试营销活动 {{ $randomString() }}"
            description: "这是一个负载测试营销活动"
            type: "EMAIL"
            budget: 1000
          capture:
            - json: "$.data.id"
              as: "campaignId"
          expect:
            - statusCode: 201

      # 获取营销活动列表
      - get:
          url: "/api/v1/campaigns"
          name: "获取营销活动列表"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          expect:
            - statusCode: 200

      # 获取营销活动详情
      - get:
          url: "/api/v1/campaigns/{{ campaignId }}"
          name: "获取营销活动详情"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          expect:
            - statusCode: 200

  # 数据分析测试
  - name: "数据分析"
    weight: 15
    flow:
      # 先登录
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "<EMAIL>"
            password: "test123"
          capture:
            - json: "$.data.accessToken"
              as: "accessToken"

      # 获取仪表板数据
      - get:
          url: "/api/v1/analytics/dashboard"
          name: "获取仪表板数据"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          expect:
            - statusCode: 200

      # 获取用户画像
      - get:
          url: "/api/v1/analytics/user-profile"
          name: "获取用户画像"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          expect:
            - statusCode: 200

# 期望的性能指标
expect:
  # 响应时间要求
  p95: 500  # 95%的请求响应时间小于500ms
  p99: 1000 # 99%的请求响应时间小于1000ms
  
  # 错误率要求
  maxErrorRate: 1  # 错误率小于1%
  
  # 吞吐量要求
  minThroughput: 100  # 最小吞吐量100 RPS
