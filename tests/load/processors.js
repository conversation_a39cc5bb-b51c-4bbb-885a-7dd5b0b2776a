// Artillery 负载测试处理器
// 提供自定义函数和数据生成

const faker = require('faker');

// 设置中文语言
faker.locale = 'zh_CN';

module.exports = {
  // 生成随机邮箱
  randomEmail,
  
  // 生成随机字符串
  randomString,
  
  // 生成随机中文名字
  randomChineseName,
  
  // 生成随机手机号
  randomPhoneNumber,
  
  // 生成随机公司名
  randomCompanyName,
  
  // 生成随机营销文案提示词
  randomMarketingPrompt,
  
  // 设置用户上下文
  setUserContext,
  
  // 验证响应数据
  validateResponse,
  
  // 记录性能指标
  recordMetrics,
  
  // 清理测试数据
  cleanupTestData,
};

/**
 * 生成随机邮箱地址
 */
function randomEmail(context, events, done) {
  const timestamp = Date.now();
  const randomNum = Math.floor(Math.random() * 1000);
  context.vars.email = `test_${timestamp}_${randomNum}@loadtest.com`;
  return done();
}

/**
 * 生成随机字符串
 */
function randomString(context, events, done) {
  const length = 8;
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  context.vars.randomString = result;
  return done();
}

/**
 * 生成随机中文名字
 */
function randomChineseName(context, events, done) {
  const surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴'];
  const names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋'];
  
  const surname = surnames[Math.floor(Math.random() * surnames.length)];
  const name = names[Math.floor(Math.random() * names.length)];
  
  context.vars.firstName = surname;
  context.vars.lastName = name;
  return done();
}

/**
 * 生成随机手机号
 */
function randomPhoneNumber(context, events, done) {
  const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = Math.floor(Math.random() * *********).toString().padStart(8, '0');
  
  context.vars.phoneNumber = prefix + suffix;
  return done();
}

/**
 * 生成随机公司名
 */
function randomCompanyName(context, events, done) {
  const prefixes = ['北京', '上海', '深圳', '广州', '杭州', '成都', '武汉', '西安'];
  const types = ['科技', '网络', '信息', '数据', '智能', '创新'];
  const suffixes = ['有限公司', '股份有限公司', '科技有限公司'];
  
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const type = types[Math.floor(Math.random() * types.length)];
  const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
  
  context.vars.companyName = `${prefix}${type}${suffix}`;
  return done();
}

/**
 * 生成随机营销文案提示词
 */
function randomMarketingPrompt(context, events, done) {
  const products = ['智能手机', '笔记本电脑', '智能手表', '无线耳机', '平板电脑', '智能音箱'];
  const features = ['高性能', '长续航', '轻薄便携', '智能AI', '快速充电', '高清显示'];
  const scenarios = ['商务办公', '学习娱乐', '运动健身', '旅行出差', '居家生活', '创意设计'];
  
  const product = products[Math.floor(Math.random() * products.length)];
  const feature = features[Math.floor(Math.random() * features.length)];
  const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
  
  context.vars.marketingPrompt = `为${product}写一段营销文案，突出${feature}特点，适用于${scenario}场景`;
  return done();
}

/**
 * 设置用户上下文
 */
function setUserContext(context, events, done) {
  // 设置用户类型
  const userTypes = ['individual', 'business', 'enterprise'];
  context.vars.userType = userTypes[Math.floor(Math.random() * userTypes.length)];
  
  // 设置用户偏好
  context.vars.preferences = {
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    theme: Math.random() > 0.5 ? 'light' : 'dark',
  };
  
  return done();
}

/**
 * 验证响应数据
 */
function validateResponse(context, events, done) {
  // 验证响应时间
  if (context.response && context.response.timings) {
    const responseTime = context.response.timings.response;
    if (responseTime > 2000) {
      console.warn(`响应时间过长: ${responseTime}ms`);
    }
  }
  
  // 验证响应状态
  if (context.response && context.response.statusCode >= 400) {
    console.error(`请求失败: ${context.response.statusCode} - ${context.response.statusMessage}`);
  }
  
  return done();
}

/**
 * 记录性能指标
 */
function recordMetrics(context, events, done) {
  // 记录自定义指标
  if (context.response) {
    const responseTime = context.response.timings ? context.response.timings.response : 0;
    const statusCode = context.response.statusCode;
    
    // 发送自定义指标
    events.emit('counter', 'custom.requests.total', 1);
    events.emit('histogram', 'custom.response_time', responseTime);
    
    if (statusCode >= 200 && statusCode < 300) {
      events.emit('counter', 'custom.requests.success', 1);
    } else {
      events.emit('counter', 'custom.requests.error', 1);
    }
  }
  
  return done();
}

/**
 * 清理测试数据
 */
function cleanupTestData(context, events, done) {
  // 在测试结束后清理创建的测试数据
  // 这里可以添加清理逻辑，比如删除测试用户、测试活动等
  
  if (context.vars.userId) {
    console.log(`清理测试用户: ${context.vars.userId}`);
  }
  
  if (context.vars.campaignId) {
    console.log(`清理测试活动: ${context.vars.campaignId}`);
  }
  
  return done();
}

/**
 * 生成随机数据的辅助函数
 */
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * 生成随机整数
 */
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 生成随机布尔值
 */
function getRandomBoolean() {
  return Math.random() > 0.5;
}
