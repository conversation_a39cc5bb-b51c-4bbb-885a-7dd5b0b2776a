{"name": "ai-digital-marketing", "version": "1.0.0", "description": "AI驱动的数字营销应用系统", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "dev:setup": "./scripts/dev-setup.sh", "dev:stop": "./scripts/dev-stop.sh", "dev:restart": "./scripts/dev-restart.sh", "dev:api": "turbo run dev --filter=api", "dev:web": "turbo run dev --filter=web", "build": "turbo run build", "build:api": "turbo run build --filter=api", "build:web": "turbo run build --filter=web", "test": "turbo run test", "test:coverage": "turbo run test:coverage", "test:e2e": "turbo run test:e2e", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "db:generate": "cd apps/api && npx prisma generate", "db:migrate": "cd apps/api && npx prisma db push", "db:seed": "cd apps/api && npx prisma db seed", "db:studio": "cd apps/api && npx prisma studio", "db:reset": "cd apps/api && npx prisma migrate reset", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:build": "docker-compose build", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/node": "^20.10.5", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "packageManager": "npm@10.2.4", "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}