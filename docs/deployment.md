# AI数字营销平台部署指南

本指南详细介绍了如何在不同环境中部署AI数字营销平台。

## 📋 目录

1. [系统要求](#系统要求)
2. [环境准备](#环境准备)
3. [本地开发部署](#本地开发部署)
4. [Docker部署](#docker部署)
5. [生产环境部署](#生产环境部署)
6. [云平台部署](#云平台部署)
7. [监控和维护](#监控和维护)
8. [故障排除](#故障排除)

## 🖥️ 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB SSD
- **网络**: 高速互联网连接

### 软件依赖
- **Node.js**: 18.0+
- **PostgreSQL**: 13.0+
- **Redis**: 6.0+
- **Docker**: 20.0+ (可选)
- **Docker Compose**: 2.0+ (可选)

## 🔧 环境准备

### 1. 安装Node.js

#### Ubuntu/Debian
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### CentOS/RHEL
```bash
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

#### macOS
```bash
brew install node@18
```

### 2. 安装PostgreSQL

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### CentOS/RHEL
```bash
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### macOS
```bash
brew install postgresql
brew services start postgresql
```

### 3. 安装Redis

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### CentOS/RHEL
```bash
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

#### macOS
```bash
brew install redis
brew services start redis
```

## 💻 本地开发部署

### 1. 克隆项目
```bash
git clone https://github.com/your-org/ai-digital-marketing.git
cd ai-digital-marketing
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键变量：
```bash
# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/ai_marketing

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# AI服务配置
OPENAI_API_KEY=your-openai-api-key

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 4. 初始化数据库
```bash
# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 初始化数据
npm run db:seed
```

### 5. 启动开发服务器
```bash
# 启动API服务
npm run dev:api

# 启动Web服务（新终端）
npm run dev:web
```

访问地址：
- 前端: http://localhost:3000
- API: http://localhost:3001
- API文档: http://localhost:3001/docs

## 🐳 Docker部署

### 1. 安装Docker和Docker Compose

#### Ubuntu/Debian
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑环境变量，确保数据库和Redis配置正确
```

### 3. 启动服务
```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

### 4. 初始化数据库
```bash
# 等待服务启动完成
sleep 30

# 运行数据库迁移
docker-compose exec api npm run migrate

# 初始化数据
docker-compose exec api npm run db:seed
```

### 5. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🚀 生产环境部署

### 1. 服务器准备

#### 创建部署用户
```bash
sudo adduser deploy
sudo usermod -aG sudo deploy
sudo su - deploy
```

#### 配置SSH密钥
```bash
mkdir -p ~/.ssh
chmod 700 ~/.ssh
# 将公钥添加到 ~/.ssh/authorized_keys
```

### 2. 安装依赖软件
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y git curl wget unzip nginx certbot python3-certbot-nginx

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2
sudo npm install -g pm2
```

### 3. 配置数据库

#### PostgreSQL配置
```bash
# 创建数据库用户
sudo -u postgres createuser --interactive deploy
sudo -u postgres createdb ai_marketing -O deploy

# 设置密码
sudo -u postgres psql -c "ALTER USER deploy PASSWORD 'secure_password';"
```

#### Redis配置
```bash
# 编辑Redis配置
sudo nano /etc/redis/redis.conf

# 设置密码
requirepass your_redis_password

# 重启Redis
sudo systemctl restart redis-server
```

### 4. 部署应用

#### 克隆代码
```bash
cd /home/<USER>
git clone https://github.com/your-org/ai-digital-marketing.git
cd ai-digital-marketing
```

#### 安装依赖和构建
```bash
npm ci --only=production
npm run build
```

#### 配置环境变量
```bash
cp .env.example .env.production
nano .env.production
```

生产环境配置示例：
```bash
NODE_ENV=production
APP_URL=https://yourdomain.com
API_URL=https://api.yourdomain.com

DATABASE_URL=postgresql://deploy:secure_password@localhost:5432/ai_marketing
REDIS_HOST=localhost
REDIS_PASSWORD=your_redis_password

# SSL证书路径
SSL_CERT_PATH=/etc/letsencrypt/live/yourdomain.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/yourdomain.com/privkey.pem
```

#### 初始化数据库
```bash
NODE_ENV=production npx prisma migrate deploy
NODE_ENV=production npm run db:seed
```

### 5. 配置进程管理

#### 创建PM2配置文件
```bash
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'ai-marketing-api',
      script: './apps/api/dist/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: './logs/api-error.log',
      out_file: './logs/api-out.log',
      log_file: './logs/api-combined.log',
      time: true
    },
    {
      name: 'ai-marketing-web',
      script: './apps/web/server.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: './logs/web-error.log',
      out_file: './logs/web-out.log',
      log_file: './logs/web-combined.log',
      time: true
    }
  ]
}
EOF
```

#### 启动应用
```bash
# 创建日志目录
mkdir -p logs

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 6. 配置Nginx反向代理

#### 创建Nginx配置
```bash
sudo nano /etc/nginx/sites-available/ai-marketing
```

配置内容：
```nginx
# API服务器配置
server {
    listen 80;
    server_name api.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Web服务器配置
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/ai-marketing /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 7. 配置SSL证书

#### 使用Let's Encrypt
```bash
# 为主域名申请证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 为API域名申请证书
sudo certbot --nginx -d api.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## ☁️ 云平台部署

### AWS部署

#### 1. 创建EC2实例
- 选择Ubuntu 20.04 LTS
- 实例类型：t3.medium或更高
- 配置安全组：开放80、443、22端口

#### 2. 创建RDS数据库
- 引擎：PostgreSQL 13
- 实例类型：db.t3.micro或更高
- 配置安全组：允许EC2访问

#### 3. 创建ElastiCache Redis
- 引擎：Redis 6.x
- 节点类型：cache.t3.micro或更高

#### 4. 部署应用
按照生产环境部署步骤，使用RDS和ElastiCache的连接信息。

### 阿里云部署

#### 1. 创建ECS实例
- 镜像：Ubuntu 20.04
- 实例规格：ecs.t6-c2m4.large或更高
- 配置安全组

#### 2. 创建RDS实例
- 数据库类型：PostgreSQL
- 版本：13.0

#### 3. 创建Redis实例
- 版本：5.0或更高

### 腾讯云部署

类似于阿里云，使用CVM、TencentDB、Redis等服务。

## 📊 监控和维护

### 1. 应用监控

#### PM2监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 重启应用
pm2 restart all

# 查看资源使用
pm2 monit
```

#### 系统监控
```bash
# 安装htop
sudo apt install htop

# 监控系统资源
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

### 2. 数据库维护

#### 备份数据库
```bash
# 创建备份脚本
cat > backup.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U deploy ai_marketing > backup_\$DATE.sql
# 上传到云存储或远程服务器
EOF

chmod +x backup.sh

# 设置定时备份
crontab -e
# 添加：0 2 * * * /home/<USER>/ai-digital-marketing/backup.sh
```

#### 数据库优化
```sql
-- 分析表统计信息
ANALYZE;

-- 重建索引
REINDEX DATABASE ai_marketing;

-- 清理无用数据
VACUUM FULL;
```

### 3. 日志管理

#### 配置日志轮转
```bash
sudo nano /etc/logrotate.d/ai-marketing
```

配置内容：
```
/home/<USER>/ai-digital-marketing/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 deploy deploy
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🔧 故障排除

### 常见问题

#### 1. 应用无法启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3001

# 检查环境变量
cat .env.production

# 查看详细错误日志
pm2 logs --lines 100
```

#### 2. 数据库连接失败
```bash
# 测试数据库连接
psql -h localhost -U deploy -d ai_marketing

# 检查PostgreSQL状态
sudo systemctl status postgresql

# 查看PostgreSQL日志
sudo tail -f /var/log/postgresql/postgresql-13-main.log
```

#### 3. Redis连接失败
```bash
# 测试Redis连接
redis-cli ping

# 检查Redis状态
sudo systemctl status redis-server

# 查看Redis日志
sudo tail -f /var/log/redis/redis-server.log
```

#### 4. Nginx配置问题
```bash
# 测试Nginx配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 性能优化

#### 1. 数据库优化
- 添加适当的索引
- 优化查询语句
- 配置连接池
- 定期维护统计信息

#### 2. 缓存优化
- 合理设置Redis过期时间
- 使用适当的缓存策略
- 监控缓存命中率

#### 3. 应用优化
- 启用gzip压缩
- 使用CDN加速静态资源
- 优化图片和资源大小
- 实施代码分割

### 安全加固

#### 1. 系统安全
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 配置防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'

# 禁用root登录
sudo nano /etc/ssh/sshd_config
# 设置 PermitRootLogin no
```

#### 2. 应用安全
- 定期更新依赖包
- 使用强密码
- 启用HTTPS
- 配置安全头
- 实施访问控制

---

如果在部署过程中遇到问题，请参考故障排除部分或联系技术支持团队。
