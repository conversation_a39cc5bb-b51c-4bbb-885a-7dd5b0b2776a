# AI数字营销平台测试环境变量配置
# 用于Jest测试和CI/CD流水线

# ===========================================
# 基础环境配置
# ===========================================
NODE_ENV=test
PORT=3001
HOST=localhost

# ===========================================
# 测试数据库配置
# ===========================================
# PostgreSQL 测试数据库
TEST_DATABASE_URL="postgresql://postgres:postgres123@localhost:5432/ai_marketing_test"
DATABASE_URL="postgresql://postgres:postgres123@localhost:5432/ai_marketing_test"

# Redis 测试缓存数据库
TEST_REDIS_URL="redis://:redis123@localhost:6379/1"
REDIS_URL="redis://:redis123@localhost:6379/1"
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# MongoDB 测试文档数据库
TEST_MONGODB_URL="***************************************************************************"
MONGODB_URL="***************************************************************************"

# Elasticsearch 测试搜索引擎
TEST_ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_URL="http://localhost:9200"

# ===========================================
# JWT认证配置（测试用）
# ===========================================
JWT_SECRET="test-jwt-secret-key-for-testing-only-not-for-production"
JWT_EXPIRES_IN="1h"
JWT_REFRESH_EXPIRES_IN="7d"

# ===========================================
# OpenAI API配置（测试用）
# ===========================================
# 使用模拟的API密钥进行测试
OPENAI_API_KEY="sk-test-mock-openai-api-key-for-testing"
OPENAI_ORGANIZATION=""
OPENAI_MODEL="gpt-3.5-turbo"
OPENAI_MAX_TOKENS=1000

# ===========================================
# Stripe支付配置（测试用）
# ===========================================
# 使用Stripe测试密钥
STRIPE_SECRET_KEY="sk_test_mock_stripe_secret_key_for_testing"
STRIPE_PUBLISHABLE_KEY="pk_test_mock_stripe_publishable_key_for_testing"
STRIPE_WEBHOOK_SECRET="whsec_mock_webhook_secret_for_testing"

# ===========================================
# 邮件服务配置（测试用）
# ===========================================
# 使用模拟SMTP配置
SMTP_HOST="localhost"
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="test_password"
SMTP_FROM="测试邮件 <<EMAIL>>"

# SendGrid配置（测试用）
SENDGRID_API_KEY="SG.mock_sendgrid_api_key_for_testing"
SENDGRID_FROM_EMAIL="<EMAIL>"

# ===========================================
# 文件存储配置（测试用）
# ===========================================
# 测试文件存储路径
UPLOAD_PATH="./test-uploads"
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,application/pdf"

# MinIO 测试对象存储配置
MINIO_ENDPOINT="localhost"
MINIO_PORT=9000
MINIO_ACCESS_KEY="testuser"
MINIO_SECRET_KEY="testpassword"
MINIO_BUCKET="ai-marketing-test"

# ===========================================
# 安全配置（测试用）
# ===========================================
BCRYPT_ROUNDS=4
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW=60000

# ===========================================
# 日志配置（测试用）
# ===========================================
LOG_LEVEL="error"
LOG_FILE="./test-logs/app.log"

# ===========================================
# 前端应用配置（测试用）
# ===========================================
FRONTEND_URL="http://localhost:3000"
API_PREFIX="/api/v1"
API_DOCS_PATH="/docs"

# ===========================================
# 监控配置（测试用）
# ===========================================
ENABLE_METRICS=false
METRICS_PATH="/metrics"

# ===========================================
# 测试特定配置
# ===========================================
# 启用测试模式
TEST_MODE=true
# 跳过外部API调用
SKIP_EXTERNAL_APIS=true
# 使用模拟数据
USE_MOCK_DATA=true
# 测试超时时间
TEST_TIMEOUT=30000
# 并发测试数量
TEST_CONCURRENCY=4

# ===========================================
# 缓存配置（测试用）
# ===========================================
CACHE_TTL=60
CACHE_PREFIX="ai-marketing-test:"

# ===========================================
# CI/CD特定配置
# ===========================================
# CI环境标识
CI=true
# 禁用交互式提示
CI_NO_INTERACTIVE=true
# 测试覆盖率阈值
COVERAGE_THRESHOLD=70
