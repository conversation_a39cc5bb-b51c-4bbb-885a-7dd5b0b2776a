-- AI数字营销平台数据库初始化脚本
-- 创建数据库和基础配置

-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE ai_marketing'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'ai_marketing')\gexec

-- 连接到ai_marketing数据库
\c ai_marketing;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 设置时区
SET timezone = 'UTC';

-- 创建自定义函数：更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建自定义函数：生成短ID
CREATE OR REPLACE FUNCTION generate_short_id(length INTEGER DEFAULT 8)
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    result TEXT := '';
    i INTEGER := 0;
BEGIN
    FOR i IN 1..length LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::INTEGER, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 创建用户角色枚举类型
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('USER', 'ADMIN', 'SUPER_ADMIN');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建用户状态枚举类型
DO $$ BEGIN
    CREATE TYPE user_status AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建营销活动状态枚举类型
DO $$ BEGIN
    CREATE TYPE campaign_status AS ENUM ('DRAFT', 'SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建营销活动类型枚举类型
DO $$ BEGIN
    CREATE TYPE campaign_type AS ENUM ('EMAIL', 'SMS', 'SOCIAL', 'PUSH', 'DISPLAY', 'SEARCH');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建内容状态枚举类型
DO $$ BEGIN
    CREATE TYPE content_status AS ENUM ('DRAFT', 'PUBLISHED', 'ARCHIVED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建内容类型枚举类型
DO $$ BEGIN
    CREATE TYPE content_type AS ENUM ('TEXT', 'IMAGE', 'VIDEO', 'AUDIO', 'DOCUMENT');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建AI生成状态枚举类型
DO $$ BEGIN
    CREATE TYPE ai_generation_status AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建AI生成类型枚举类型
DO $$ BEGIN
    CREATE TYPE ai_generation_type AS ENUM ('TEXT_GENERATION', 'IMAGE_GENERATION', 'CONTENT_OPTIMIZATION');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建订阅状态枚举类型
DO $$ BEGIN
    CREATE TYPE subscription_status AS ENUM ('ACTIVE', 'PAST_DUE', 'CANCELLED', 'UNPAID');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建支付状态枚举类型
DO $$ BEGIN
    CREATE TYPE payment_status AS ENUM ('PENDING', 'SUCCEEDED', 'FAILED', 'CANCELLED', 'REFUNDED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建报告类型枚举类型
DO $$ BEGIN
    CREATE TYPE report_type AS ENUM ('CAMPAIGN_PERFORMANCE', 'USER_ANALYTICS', 'REVENUE_REPORT', 'AI_USAGE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建基础配置数据
INSERT INTO system_configs (key, value, category, description) VALUES
('app_name', 'AI数字营销平台', 'general', '应用程序名称'),
('app_version', '1.0.0', 'general', '应用程序版本'),
('maintenance_mode', 'false', 'general', '维护模式开关'),
('max_file_upload_size', '10485760', 'upload', '最大文件上传大小（字节）'),
('allowed_file_types', 'image/jpeg,image/png,image/gif,image/webp,application/pdf', 'upload', '允许的文件类型'),
('default_timezone', 'UTC', 'general', '默认时区'),
('default_language', 'zh-CN', 'general', '默认语言'),
('email_verification_required', 'true', 'auth', '是否需要邮箱验证'),
('password_min_length', '8', 'auth', '密码最小长度'),
('session_timeout', '86400', 'auth', '会话超时时间（秒）'),
('api_rate_limit', '100', 'api', 'API速率限制（每15分钟）'),
('openai_default_model', 'gpt-3.5-turbo', 'ai', 'OpenAI默认模型'),
('openai_max_tokens', '2000', 'ai', 'OpenAI最大令牌数'),
('stripe_webhook_tolerance', '300', 'payment', 'Stripe Webhook容忍时间（秒）')
ON CONFLICT (key) DO NOTHING;

-- 创建默认管理员用户（密码：admin123）
INSERT INTO users (
    id,
    email,
    password,
    first_name,
    last_name,
    role,
    status,
    email_verified,
    email_verified_at,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid()::text,
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwEHxPr5oUHNKOCYrjg5/TRJ5oyNdmAGjU4imy92BBdh6', -- admin123
    '系统',
    '管理员',
    'SUPER_ADMIN',
    'ACTIVE',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;

-- 创建测试用户（密码：test123）
INSERT INTO users (
    id,
    email,
    password,
    first_name,
    last_name,
    role,
    status,
    email_verified,
    email_verified_at,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid()::text,
    '<EMAIL>',
    '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- test123
    '测试',
    '用户',
    'USER',
    'ACTIVE',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;

-- 输出初始化完成信息
\echo '数据库初始化完成！'
\echo '默认管理员账户：<EMAIL> / admin123'
\echo '测试用户账户：<EMAIL> / test123'
