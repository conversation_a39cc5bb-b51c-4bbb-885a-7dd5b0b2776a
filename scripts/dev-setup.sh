#!/bin/bash

# AI数字营销平台开发环境设置脚本
# 用于快速设置和启动开发环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查开发依赖..."
    
    local deps=("node" "npm" "docker" "docker-compose" "git")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少以下依赖工具:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        log_error "请安装这些工具后再运行脚本"
        exit 1
    fi
    
    # 检查Node.js版本
    local node_version=$(node -v | cut -d'v' -f2)
    local required_version="20.0.0"
    
    if [[ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" != "$required_version" ]]; then
        log_error "Node.js版本过低，需要 >= $required_version，当前版本: $node_version"
        exit 1
    fi
    
    log_success "所有依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=("logs" "uploads" "backups" "temp")
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录创建完成"
}

# 检查环境变量文件
check_env_file() {
    log_info "检查环境变量文件..."
    
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            log_warning ".env文件不存在，从.env.example复制..."
            cp .env.example .env
            log_warning "请编辑.env文件，填入正确的配置值"
        else
            log_error ".env.example文件不存在，无法创建.env文件"
            exit 1
        fi
    else
        log_success ".env文件已存在"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 检查package-lock.json是否存在
    if [[ -f "package-lock.json" ]]; then
        npm ci
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 启动数据库服务
start_databases() {
    log_info "启动数据库服务..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker未运行，请启动Docker后重试"
        exit 1
    fi
    
    # 启动数据库容器
    docker-compose up -d postgres redis mongodb elasticsearch minio
    
    # 等待数据库启动
    log_info "等待数据库服务启动..."
    sleep 10
    
    # 检查数据库连接
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "检查数据库连接... ($attempt/$max_attempts)"
        
        if docker-compose exec -T postgres pg_isready -U postgres &> /dev/null; then
            log_success "PostgreSQL连接成功"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "数据库连接失败"
            exit 1
        fi
        
        sleep 2
        ((attempt++))
    done
    
    log_success "数据库服务启动完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 生成Prisma客户端
    cd apps/api
    npx prisma generate
    
    # 运行数据库迁移
    npx prisma db push
    
    # 填充初始数据（如果存在seed脚本）
    if [[ -f "prisma/seed.ts" ]]; then
        npx prisma db seed
    fi
    
    cd ../..
    
    log_success "数据库迁移完成"
}

# 启动开发服务器
start_dev_servers() {
    log_info "启动开发服务器..."
    
    # 在后台启动API服务器
    log_info "启动API服务器..."
    cd apps/api
    npm run dev &
    API_PID=$!
    cd ../..
    
    # 等待API服务器启动
    sleep 5
    
    # 在后台启动Web服务器
    log_info "启动Web服务器..."
    cd apps/web
    npm run dev &
    WEB_PID=$!
    cd ../..
    
    # 保存进程ID到文件
    echo $API_PID > .api.pid
    echo $WEB_PID > .web.pid
    
    log_success "开发服务器启动完成"
    log_info "API服务器: http://localhost:3001"
    log_info "Web应用: http://localhost:3000"
    log_info "API文档: http://localhost:3001/docs"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查尝试 $attempt/$max_attempts"
        
        # 检查API服务器
        if curl -f http://localhost:3001/health &> /dev/null; then
            log_success "API服务器健康检查通过"
            
            # 检查Web服务器
            if curl -f http://localhost:3000 &> /dev/null; then
                log_success "Web服务器健康检查通过"
                break
            fi
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "健康检查失败"
            return 1
        fi
        
        sleep 5
        ((attempt++))
    done
    
    log_success "所有服务健康检查通过"
}

# 显示开发信息
show_dev_info() {
    log_success "开发环境设置完成！"
    echo
    echo "🚀 服务地址:"
    echo "  - Web应用:     http://localhost:3000"
    echo "  - API服务:     http://localhost:3001"
    echo "  - API文档:     http://localhost:3001/docs"
    echo "  - Prisma Studio: npx prisma studio (在apps/api目录下)"
    echo "  - Grafana监控: http://localhost:3003 (admin/admin123)"
    echo
    echo "📊 数据库管理:"
    echo "  - PostgreSQL:  localhost:5432 (postgres/postgres123)"
    echo "  - Redis:       localhost:6379 (密码: redis123)"
    echo "  - MongoDB:     localhost:27017 (admin/mongo123)"
    echo "  - MinIO:       http://localhost:9001 (minioadmin/minioadmin123)"
    echo
    echo "🛠️ 开发命令:"
    echo "  - 停止服务:    ./scripts/dev-stop.sh"
    echo "  - 重启服务:    ./scripts/dev-restart.sh"
    echo "  - 查看日志:    docker-compose logs -f"
    echo "  - 运行测试:    npm run test"
    echo
    echo "📝 默认账户:"
    echo "  - 管理员:      <EMAIL> / admin123"
    echo "  - 测试用户:    <EMAIL> / test123"
}

# 主函数
main() {
    log_info "开始设置AI数字营销平台开发环境..."
    
    # 检查依赖
    check_dependencies
    
    # 创建目录
    create_directories
    
    # 检查环境变量
    check_env_file
    
    # 安装依赖
    install_dependencies
    
    # 启动数据库
    start_databases
    
    # 运行迁移
    run_migrations
    
    # 启动开发服务器
    start_dev_servers
    
    # 健康检查
    if health_check; then
        show_dev_info
    else
        log_error "开发环境设置失败"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
