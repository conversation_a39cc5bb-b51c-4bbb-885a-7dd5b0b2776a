// AI数字营销平台 MongoDB 初始化脚本
// 创建数据库、集合和索引

// 切换到ai_marketing数据库
db = db.getSiblingDB('ai_marketing');

// 创建用户行为分析集合
db.createCollection('user_behaviors', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'action', 'timestamp'],
      properties: {
        userId: {
          bsonType: 'string',
          description: '用户ID，必填'
        },
        action: {
          bsonType: 'string',
          description: '用户行为动作，必填'
        },
        timestamp: {
          bsonType: 'date',
          description: '行为发生时间，必填'
        },
        sessionId: {
          bsonType: 'string',
          description: '会话ID'
        },
        page: {
          bsonType: 'string',
          description: '页面路径'
        },
        metadata: {
          bsonType: 'object',
          description: '额外的元数据'
        },
        ipAddress: {
          bsonType: 'string',
          description: 'IP地址'
        },
        userAgent: {
          bsonType: 'string',
          description: '用户代理字符串'
        }
      }
    }
  }
});

// 创建营销活动数据集合
db.createCollection('campaign_data', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['campaignId', 'timestamp'],
      properties: {
        campaignId: {
          bsonType: 'string',
          description: '营销活动ID，必填'
        },
        timestamp: {
          bsonType: 'date',
          description: '数据记录时间，必填'
        },
        metrics: {
          bsonType: 'object',
          description: '营销指标数据'
        },
        audience: {
          bsonType: 'object',
          description: '受众数据'
        },
        performance: {
          bsonType: 'object',
          description: '性能数据'
        }
      }
    }
  }
});

// 创建AI生成内容集合
db.createCollection('ai_generated_content', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'type', 'prompt', 'createdAt'],
      properties: {
        userId: {
          bsonType: 'string',
          description: '用户ID，必填'
        },
        type: {
          bsonType: 'string',
          enum: ['text', 'image', 'video'],
          description: '内容类型，必填'
        },
        prompt: {
          bsonType: 'string',
          description: '生成提示词，必填'
        },
        result: {
          bsonType: 'string',
          description: '生成结果'
        },
        metadata: {
          bsonType: 'object',
          description: '生成元数据'
        },
        createdAt: {
          bsonType: 'date',
          description: '创建时间，必填'
        },
        status: {
          bsonType: 'string',
          enum: ['pending', 'processing', 'completed', 'failed'],
          description: '生成状态'
        }
      }
    }
  }
});

// 创建用户画像集合
db.createCollection('user_profiles', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'updatedAt'],
      properties: {
        userId: {
          bsonType: 'string',
          description: '用户ID，必填'
        },
        demographics: {
          bsonType: 'object',
          description: '人口统计学数据'
        },
        interests: {
          bsonType: 'array',
          description: '兴趣标签'
        },
        behaviors: {
          bsonType: 'object',
          description: '行为模式'
        },
        preferences: {
          bsonType: 'object',
          description: '偏好设置'
        },
        segments: {
          bsonType: 'array',
          description: '用户分群'
        },
        score: {
          bsonType: 'object',
          description: '用户评分'
        },
        updatedAt: {
          bsonType: 'date',
          description: '更新时间，必填'
        }
      }
    }
  }
});

// 创建系统日志集合
db.createCollection('system_logs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['level', 'message', 'timestamp'],
      properties: {
        level: {
          bsonType: 'string',
          enum: ['error', 'warn', 'info', 'debug'],
          description: '日志级别，必填'
        },
        message: {
          bsonType: 'string',
          description: '日志消息，必填'
        },
        timestamp: {
          bsonType: 'date',
          description: '日志时间，必填'
        },
        service: {
          bsonType: 'string',
          description: '服务名称'
        },
        userId: {
          bsonType: 'string',
          description: '用户ID'
        },
        requestId: {
          bsonType: 'string',
          description: '请求ID'
        },
        metadata: {
          bsonType: 'object',
          description: '额外的日志数据'
        }
      }
    }
  }
});

// 创建索引
print('创建索引...');

// 用户行为分析索引
db.user_behaviors.createIndex({ userId: 1, timestamp: -1 });
db.user_behaviors.createIndex({ action: 1, timestamp: -1 });
db.user_behaviors.createIndex({ sessionId: 1 });
db.user_behaviors.createIndex({ timestamp: -1 });

// 营销活动数据索引
db.campaign_data.createIndex({ campaignId: 1, timestamp: -1 });
db.campaign_data.createIndex({ timestamp: -1 });

// AI生成内容索引
db.ai_generated_content.createIndex({ userId: 1, createdAt: -1 });
db.ai_generated_content.createIndex({ type: 1, createdAt: -1 });
db.ai_generated_content.createIndex({ status: 1 });
db.ai_generated_content.createIndex({ createdAt: -1 });

// 用户画像索引
db.user_profiles.createIndex({ userId: 1 }, { unique: true });
db.user_profiles.createIndex({ 'segments': 1 });
db.user_profiles.createIndex({ updatedAt: -1 });

// 系统日志索引
db.system_logs.createIndex({ timestamp: -1 });
db.system_logs.createIndex({ level: 1, timestamp: -1 });
db.system_logs.createIndex({ service: 1, timestamp: -1 });
db.system_logs.createIndex({ userId: 1, timestamp: -1 });

// 创建TTL索引（自动删除旧数据）
// 用户行为数据保留90天
db.user_behaviors.createIndex({ timestamp: 1 }, { expireAfterSeconds: 7776000 });

// 系统日志保留30天
db.system_logs.createIndex({ timestamp: 1 }, { expireAfterSeconds: 2592000 });

// 插入示例数据
print('插入示例数据...');

// 插入示例用户行为数据
db.user_behaviors.insertMany([
  {
    userId: 'user_001',
    action: 'page_view',
    page: '/dashboard',
    timestamp: new Date(),
    sessionId: 'session_001',
    metadata: { duration: 30000 }
  },
  {
    userId: 'user_001',
    action: 'button_click',
    page: '/dashboard/ai',
    timestamp: new Date(),
    sessionId: 'session_001',
    metadata: { button: 'generate_content' }
  }
]);

// 插入示例用户画像数据
db.user_profiles.insertMany([
  {
    userId: 'user_001',
    demographics: {
      age: 30,
      gender: 'male',
      location: 'Beijing'
    },
    interests: ['technology', 'marketing', 'ai'],
    behaviors: {
      loginFrequency: 'daily',
      preferredTime: 'morning',
      deviceType: 'desktop'
    },
    segments: ['tech_enthusiast', 'early_adopter'],
    score: {
      engagement: 85,
      loyalty: 70,
      value: 90
    },
    updatedAt: new Date()
  }
]);

// 创建数据库用户（如果需要）
// db.createUser({
//   user: 'ai_marketing_user',
//   pwd: 'secure_password',
//   roles: [
//     { role: 'readWrite', db: 'ai_marketing' }
//   ]
// });

print('MongoDB初始化完成！');
print('已创建以下集合：');
print('- user_behaviors: 用户行为分析');
print('- campaign_data: 营销活动数据');
print('- ai_generated_content: AI生成内容');
print('- user_profiles: 用户画像');
print('- system_logs: 系统日志');
print('所有索引已创建完成。');
