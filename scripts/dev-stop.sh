#!/bin/bash

# AI数字营销平台开发环境停止脚本
# 用于停止所有开发服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止开发服务器
stop_dev_servers() {
    log_info "停止开发服务器..."
    
    # 停止API服务器
    if [[ -f ".api.pid" ]]; then
        local api_pid=$(cat .api.pid)
        if kill -0 $api_pid 2>/dev/null; then
            log_info "停止API服务器 (PID: $api_pid)"
            kill $api_pid
            rm .api.pid
        else
            log_warning "API服务器进程不存在"
            rm -f .api.pid
        fi
    else
        log_warning "未找到API服务器PID文件"
    fi
    
    # 停止Web服务器
    if [[ -f ".web.pid" ]]; then
        local web_pid=$(cat .web.pid)
        if kill -0 $web_pid 2>/dev/null; then
            log_info "停止Web服务器 (PID: $web_pid)"
            kill $web_pid
            rm .web.pid
        else
            log_warning "Web服务器进程不存在"
            rm -f .web.pid
        fi
    else
        log_warning "未找到Web服务器PID文件"
    fi
    
    # 强制停止所有Node.js进程（如果需要）
    local node_processes=$(pgrep -f "node.*dev" || true)
    if [[ -n "$node_processes" ]]; then
        log_warning "发现残留的Node.js开发进程，正在强制停止..."
        pkill -f "node.*dev" || true
    fi
    
    log_success "开发服务器已停止"
}

# 停止Docker容器
stop_docker_containers() {
    log_info "停止Docker容器..."
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_warning "Docker未运行，跳过容器停止"
        return
    fi
    
    # 停止所有容器
    docker-compose down
    
    log_success "Docker容器已停止"
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理PID文件
    rm -f .api.pid .web.pid
    
    # 清理临时目录
    if [[ -d "temp" ]]; then
        rm -rf temp/*
    fi
    
    # 清理日志文件（可选）
    if [[ "$1" == "--clean-logs" ]]; then
        log_info "清理日志文件..."
        if [[ -d "logs" ]]; then
            rm -f logs/*.log
        fi
    fi
    
    log_success "临时文件清理完成"
}

# 显示停止信息
show_stop_info() {
    log_success "开发环境已停止！"
    echo
    echo "🛑 已停止的服务:"
    echo "  - Web应用服务器"
    echo "  - API服务器"
    echo "  - 所有Docker容器"
    echo
    echo "🔄 重新启动开发环境:"
    echo "  ./scripts/dev-setup.sh"
    echo
    echo "🧹 完全清理环境:"
    echo "  ./scripts/dev-stop.sh --clean-logs"
    echo "  docker system prune -f"
}

# 主函数
main() {
    log_info "停止AI数字营销平台开发环境..."
    
    # 停止开发服务器
    stop_dev_servers
    
    # 停止Docker容器
    stop_docker_containers
    
    # 清理临时文件
    cleanup_temp_files "$1"
    
    # 显示停止信息
    show_stop_info
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
