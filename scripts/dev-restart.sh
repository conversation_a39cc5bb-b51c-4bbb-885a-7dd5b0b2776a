#!/bin/bash

# AI数字营销平台开发环境重启脚本
# 用于重启开发环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 主函数
main() {
    log_info "重启AI数字营销平台开发环境..."
    
    # 停止当前服务
    log_info "停止当前服务..."
    ./scripts/dev-stop.sh
    
    # 等待服务完全停止
    sleep 3
    
    # 重新启动服务
    log_info "重新启动服务..."
    ./scripts/dev-setup.sh
    
    log_success "开发环境重启完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
