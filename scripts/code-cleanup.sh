#!/bin/bash

# 代码清理和优化脚本
# 自动清理无用代码、优化性能、格式化代码、添加注释

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
代码清理和优化脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -v, --verbose           详细输出
    --lint-only             仅执行代码检查
    --format-only           仅执行代码格式化
    --optimize-only         仅执行性能优化
    --clean-only            仅执行清理操作
    --skip-tests            跳过测试运行
    --fix                   自动修复可修复的问题

示例:
    $0                      执行完整的清理和优化
    $0 --lint-only          仅检查代码质量
    $0 --format-only        仅格式化代码
    $0 --fix                自动修复问题

EOF
}

# 默认参数
VERBOSE=false
LINT_ONLY=false
FORMAT_ONLY=false
OPTIMIZE_ONLY=false
CLEAN_ONLY=false
SKIP_TESTS=false
AUTO_FIX=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --lint-only)
            LINT_ONLY=true
            shift
            ;;
        --format-only)
            FORMAT_ONLY=true
            shift
            ;;
        --optimize-only)
            OPTIMIZE_ONLY=true
            shift
            ;;
        --clean-only)
            CLEAN_ONLY=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --fix)
            AUTO_FIX=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必要工具
check_tools() {
    log_info "检查必要工具..."
    
    local tools=("node" "npm" "eslint" "prettier" "tsc")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "请先安装缺少的工具"
        exit 1
    fi
    
    log_success "工具检查通过"
}

# 清理无用文件
clean_unused_files() {
    if [[ "$CLEAN_ONLY" == true ]] || [[ "$LINT_ONLY" == false && "$FORMAT_ONLY" == false && "$OPTIMIZE_ONLY" == false ]]; then
        log_info "清理无用文件..."
        
        # 清理构建产物
        find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
        find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
        find . -name ".next" -type d -exec rm -rf {} + 2>/dev/null || true
        
        # 清理日志文件
        find . -name "*.log" -type f -delete 2>/dev/null || true
        find . -name "logs" -type d -exec rm -rf {} + 2>/dev/null || true
        
        # 清理临时文件
        find . -name "*.tmp" -type f -delete 2>/dev/null || true
        find . -name "*.temp" -type f -delete 2>/dev/null || true
        find . -name ".DS_Store" -type f -delete 2>/dev/null || true
        
        # 清理测试覆盖率报告
        find . -name "coverage" -type d -exec rm -rf {} + 2>/dev/null || true
        
        # 清理缓存文件
        find . -name ".eslintcache" -type f -delete 2>/dev/null || true
        find . -name ".jest-cache" -type d -exec rm -rf {} + 2>/dev/null || true
        
        log_success "无用文件清理完成"
    fi
}

# 代码质量检查
lint_code() {
    if [[ "$LINT_ONLY" == true ]] || [[ "$FORMAT_ONLY" == false && "$OPTIMIZE_ONLY" == false && "$CLEAN_ONLY" == false ]]; then
        log_info "执行代码质量检查..."
        
        # TypeScript 类型检查
        log_info "执行 TypeScript 类型检查..."
        if [[ "$AUTO_FIX" == true ]]; then
            npx tsc --noEmit --skipLibCheck || log_warning "TypeScript 类型检查发现问题"
        else
            npx tsc --noEmit --skipLibCheck
        fi
        
        # ESLint 检查
        log_info "执行 ESLint 检查..."
        if [[ "$AUTO_FIX" == true ]]; then
            npx eslint . --ext .ts,.tsx,.js,.jsx --fix || log_warning "ESLint 发现问题"
        else
            npx eslint . --ext .ts,.tsx,.js,.jsx
        fi
        
        # API 项目检查
        if [ -d "apps/api" ]; then
            log_info "检查 API 项目..."
            cd apps/api
            if [[ "$AUTO_FIX" == true ]]; then
                npx eslint src --ext .ts --fix || log_warning "API ESLint 发现问题"
            else
                npx eslint src --ext .ts
            fi
            cd ../..
        fi
        
        # Web 项目检查
        if [ -d "apps/web" ]; then
            log_info "检查 Web 项目..."
            cd apps/web
            if [[ "$AUTO_FIX" == true ]]; then
                npx eslint src --ext .ts,.tsx --fix || log_warning "Web ESLint 发现问题"
            else
                npx eslint src --ext .ts,.tsx
            fi
            cd ../..
        fi
        
        log_success "代码质量检查完成"
    fi
}

# 代码格式化
format_code() {
    if [[ "$FORMAT_ONLY" == true ]] || [[ "$LINT_ONLY" == false && "$OPTIMIZE_ONLY" == false && "$CLEAN_ONLY" == false ]]; then
        log_info "执行代码格式化..."
        
        # Prettier 格式化
        log_info "使用 Prettier 格式化代码..."
        npx prettier --write "**/*.{ts,tsx,js,jsx,json,md,yml,yaml}" \
            --ignore-path .gitignore \
            --ignore-path .prettierignore
        
        # 格式化 package.json 文件
        log_info "格式化 package.json 文件..."
        find . -name "package.json" -not -path "./node_modules/*" -exec npx prettier --write {} \;
        
        # 格式化配置文件
        log_info "格式化配置文件..."
        find . -name "*.config.{js,ts}" -not -path "./node_modules/*" -exec npx prettier --write {} \;
        
        log_success "代码格式化完成"
    fi
}

# 性能优化
optimize_performance() {
    if [[ "$OPTIMIZE_ONLY" == true ]] || [[ "$LINT_ONLY" == false && "$FORMAT_ONLY" == false && "$CLEAN_ONLY" == false ]]; then
        log_info "执行性能优化..."
        
        # 分析包大小
        log_info "分析包大小..."
        if [ -d "apps/web" ]; then
            cd apps/web
            if [ -f "package.json" ] && grep -q "analyze" package.json; then
                npm run analyze > /dev/null 2>&1 || log_warning "包分析失败"
            fi
            cd ../..
        fi
        
        # 检查重复依赖
        log_info "检查重复依赖..."
        npx npm-check-updates --doctor || log_warning "依赖检查发现问题"
        
        # 优化图片资源
        log_info "优化图片资源..."
        find . -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | \
            grep -v node_modules | \
            head -10 | \
            while read -r img; do
                if command -v imagemin &> /dev/null; then
                    imagemin "$img" --out-dir="$(dirname "$img")" > /dev/null 2>&1 || true
                fi
            done
        
        # 检查未使用的依赖
        log_info "检查未使用的依赖..."
        if command -v depcheck &> /dev/null; then
            depcheck --ignores="@types/*,eslint-*,prettier,jest,@testing-library/*" || log_warning "发现未使用的依赖"
        fi
        
        log_success "性能优化完成"
    fi
}

# 更新依赖
update_dependencies() {
    log_info "检查依赖更新..."
    
    # 检查过期依赖
    npm outdated || log_info "所有依赖都是最新的"
    
    # 安全审计
    log_info "执行安全审计..."
    npm audit || log_warning "发现安全漏洞"
    
    if [[ "$AUTO_FIX" == true ]]; then
        log_info "自动修复安全漏洞..."
        npm audit fix || log_warning "部分安全漏洞无法自动修复"
    fi
}

# 运行测试
run_tests() {
    if [[ "$SKIP_TESTS" == false ]]; then
        log_info "运行测试..."
        
        # 运行单元测试
        if [ -f "jest.config.js" ] || [ -f "jest.config.ts" ]; then
            log_info "运行单元测试..."
            npm test -- --passWithNoTests || log_warning "部分测试失败"
        fi
        
        # 运行类型检查
        log_info "运行类型检查..."
        npx tsc --noEmit || log_warning "类型检查发现问题"
        
        log_success "测试运行完成"
    fi
}

# 生成报告
generate_report() {
    log_info "生成清理报告..."
    
    local report_file="cleanup-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << EOF
# 代码清理报告

生成时间: $(date)

## 清理统计

### 文件清理
- 清理构建产物: dist, build, .next 目录
- 清理日志文件: *.log 文件
- 清理临时文件: *.tmp, *.temp 文件
- 清理缓存文件: .eslintcache, .jest-cache 目录

### 代码质量
- TypeScript 类型检查: $(npx tsc --noEmit --skipLibCheck > /dev/null 2>&1 && echo "通过" || echo "有问题")
- ESLint 检查: $(npx eslint . --ext .ts,.tsx,.js,.jsx > /dev/null 2>&1 && echo "通过" || echo "有问题")

### 代码格式化
- Prettier 格式化: 已执行
- 配置文件格式化: 已执行

### 性能优化
- 依赖检查: 已执行
- 图片优化: 已执行
- 包大小分析: 已执行

### 测试结果
- 单元测试: $(npm test -- --passWithNoTests > /dev/null 2>&1 && echo "通过" || echo "有问题")
- 类型检查: $(npx tsc --noEmit > /dev/null 2>&1 && echo "通过" || echo "有问题")

## 建议

1. 定期运行此脚本保持代码质量
2. 在提交代码前执行格式化
3. 关注安全审计结果
4. 及时更新过期依赖

EOF

    log_success "清理报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始代码清理和优化..."
    
    # 检查工具
    check_tools
    
    # 清理无用文件
    clean_unused_files
    
    # 代码质量检查
    lint_code
    
    # 代码格式化
    format_code
    
    # 性能优化
    optimize_performance
    
    # 更新依赖
    update_dependencies
    
    # 运行测试
    run_tests
    
    # 生成报告
    generate_report
    
    log_success "代码清理和优化完成！"
    echo
    echo "总结:"
    echo "✅ 无用文件已清理"
    echo "✅ 代码质量已检查"
    echo "✅ 代码格式已优化"
    echo "✅ 性能已优化"
    echo "✅ 依赖已检查"
    echo "✅ 测试已运行"
    echo
    echo "建议定期运行此脚本以保持代码质量。"
}

# 捕获中断信号
trap 'log_error "清理过程被中断"; exit 1' INT TERM

# 执行主函数
main
