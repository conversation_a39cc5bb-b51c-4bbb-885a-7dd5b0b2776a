#!/bin/bash

# AI数字营销平台部署脚本
# 用于自动化部署到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查部署依赖..."
    
    local deps=("docker" "docker-compose" "git" "curl")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            log_error "$dep 未安装，请先安装后再运行部署脚本"
            exit 1
        fi
    done
    
    log_success "所有依赖检查通过"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    local required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "JWT_SECRET"
        "OPENAI_API_KEY"
        "STRIPE_SECRET_KEY"
        "STRIPE_WEBHOOK_SECRET"
    )
    
    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必要的环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_error "请设置这些环境变量后再运行部署脚本"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 备份当前部署
backup_current_deployment() {
    log_info "备份当前部署..."
    
    local backup_dir="./backups/deployment-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    if docker ps | grep -q "ai-marketing-postgres"; then
        log_info "备份数据库..."
        docker exec ai-marketing-postgres pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > "$backup_dir/database.sql"
        log_success "数据库备份完成"
    fi
    
    # 备份配置文件
    if [[ -f ".env" ]]; then
        cp .env "$backup_dir/"
    fi
    
    log_success "备份完成: $backup_dir"
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."
    
    # 检查是否有未提交的更改
    if [[ -n $(git status --porcelain) ]]; then
        log_warning "检测到未提交的更改，将暂存这些更改"
        git stash push -m "Auto-stash before deployment $(date)"
    fi
    
    # 拉取最新代码
    git fetch origin
    git checkout main
    git pull origin main
    
    log_success "代码更新完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -f apps/web/Dockerfile -t ai-marketing-web:latest .
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -f apps/api/Dockerfile -t ai-marketing-api:latest .
    
    log_success "镜像构建完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 确保数据库容器正在运行
    docker-compose -f docker-compose.prod.yml up -d postgres
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 运行迁移
    docker-compose -f docker-compose.prod.yml run --rm api npm run db:migrate
    
    log_success "数据库迁移完成"
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    # 停止旧容器
    log_info "停止旧容器..."
    docker-compose -f docker-compose.prod.yml down
    
    # 启动新容器
    log_info "启动新容器..."
    docker-compose -f docker-compose.prod.yml up -d
    
    log_success "应用部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查尝试 $attempt/$max_attempts"
        
        # 检查前端
        if curl -f http://localhost/api/health &> /dev/null; then
            log_success "前端健康检查通过"
            break
        fi
        
        # 检查后端
        if curl -f http://localhost:3001/health &> /dev/null; then
            log_success "后端健康检查通过"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "健康检查失败，部署可能有问题"
            return 1
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log_success "所有服务健康检查通过"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧镜像..."
    
    # 删除悬空镜像
    docker image prune -f
    
    # 删除旧版本镜像（保留最新的3个版本）
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}" | \
    grep "ai-marketing" | \
    tail -n +4 | \
    awk '{print $3}' | \
    xargs -r docker rmi
    
    log_success "镜像清理完成"
}

# 发送部署通知
send_notification() {
    log_info "发送部署通知..."
    
    local status=$1
    local message="AI数字营销平台部署${status} - $(date)"
    
    # 这里可以集成 Slack、钉钉、邮件等通知方式
    # 示例：发送到 Slack
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
    
    log_success "通知发送完成"
}

# 主部署流程
main() {
    log_info "开始部署AI数字营销平台..."
    
    # 检查依赖
    check_dependencies
    
    # 检查环境变量
    check_env_vars
    
    # 备份当前部署
    backup_current_deployment
    
    # 拉取最新代码
    pull_latest_code
    
    # 构建镜像
    build_images
    
    # 运行数据库迁移
    run_migrations
    
    # 部署应用
    deploy_application
    
    # 健康检查
    if health_check; then
        log_success "部署成功完成！"
        send_notification "成功"
        
        # 清理旧镜像
        cleanup_old_images
        
        log_info "部署信息:"
        log_info "  - 前端地址: https://ai-marketing.com"
        log_info "  - API地址: https://api.ai-marketing.com"
        log_info "  - 监控面板: http://localhost:3003"
        log_info "  - 部署时间: $(date)"
        
    else
        log_error "部署失败，正在回滚..."
        
        # 回滚到之前的版本
        docker-compose -f docker-compose.prod.yml down
        
        # 这里可以添加更复杂的回滚逻辑
        
        send_notification "失败"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
