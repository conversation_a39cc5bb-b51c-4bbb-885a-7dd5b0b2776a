// Lighthouse CI 配置文件
// 用于前端性能测试和监控

module.exports = {
  ci: {
    // 收集配置
    collect: {
      // 测试的URL列表
      url: [
        'http://localhost:3000',
        'http://localhost:3000/dashboard',
        'http://localhost:3000/dashboard/ai',
        'http://localhost:3000/dashboard/campaigns',
        'http://localhost:3000/dashboard/analytics',
      ],
      
      // 每个URL运行的次数
      numberOfRuns: 3,
      
      // Chrome启动选项
      settings: {
        chromeFlags: [
          '--headless',
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-web-security',
          '--allow-running-insecure-content',
        ],
      },
      
      // 启动服务器配置
      startServerCommand: 'npm run dev:web',
      startServerReadyPattern: 'ready on',
      startServerReadyTimeout: 60000,
    },

    // 断言配置 - 性能阈值
    assert: {
      assertions: {
        // 性能指标
        'categories:performance': ['error', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.8 }],
        'categories:seo': ['error', { minScore: 0.8 }],
        
        // 核心Web指标
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 4000 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
        
        // 其他重要指标
        'speed-index': ['error', { maxNumericValue: 3000 }],
        'interactive': ['error', { maxNumericValue: 5000 }],
        
        // 资源优化
        'unused-css-rules': ['warn', { maxLength: 0 }],
        'unused-javascript': ['warn', { maxLength: 0 }],
        'modern-image-formats': ['warn', { maxLength: 0 }],
        'uses-webp-images': ['warn', { maxLength: 0 }],
        'uses-optimized-images': ['warn', { maxLength: 0 }],
        'uses-text-compression': ['warn', { maxLength: 0 }],
        
        // 网络优化
        'uses-http2': ['warn', { maxLength: 0 }],
        'uses-long-cache-ttl': ['warn', { maxLength: 0 }],
        'efficient-animated-content': ['warn', { maxLength: 0 }],
        
        // 安全性
        'is-on-https': ['error', { maxLength: 0 }],
        'uses-https': ['error', { maxLength: 0 }],
      },
      
      // 预设配置
      preset: 'lighthouse:recommended',
      
      // 包含的审计
      includePassedAssertions: true,
    },

    // 上传配置
    upload: {
      // 目标类型
      target: 'temporary-public-storage',
      
      // 令牌（如果使用LHCI服务器）
      // token: process.env.LHCI_TOKEN,
      
      // 服务器URL（如果使用LHCI服务器）
      // serverBaseUrl: process.env.LHCI_SERVER_URL,
      
      // 基本认证
      // basicAuth: {
      //   username: process.env.LHCI_BASIC_AUTH_USERNAME,
      //   password: process.env.LHCI_BASIC_AUTH_PASSWORD,
      // },
    },

    // 服务器配置（如果运行LHCI服务器）
    server: {
      port: 9001,
      storage: {
        storageMethod: 'sql',
        sqlDialect: 'sqlite',
        sqlDatabasePath: './lhci.db',
      },
    },

    // Wizard配置
    wizard: {
      // 跳过向导
      skip: true,
    },
  },

  // 扩展的Lighthouse配置
  extends: 'lighthouse:default',
  
  // 自定义审计
  audits: [
    // 可以添加自定义审计
  ],
  
  // 自定义类别
  categories: {
    // 可以添加自定义类别
    'custom-performance': {
      title: '自定义性能',
      description: '针对AI数字营销平台的自定义性能指标',
      auditRefs: [
        { id: 'first-contentful-paint', weight: 3 },
        { id: 'largest-contentful-paint', weight: 3 },
        { id: 'cumulative-layout-shift', weight: 2 },
        { id: 'total-blocking-time', weight: 2 },
      ],
    },
  },
  
  // 设置
  settings: {
    // 仅桌面端测试
    formFactor: 'desktop',
    
    // 网络节流
    throttling: {
      rttMs: 40,
      throughputKbps: 10240,
      cpuSlowdownMultiplier: 1,
      requestLatencyMs: 0,
      downloadThroughputKbps: 0,
      uploadThroughputKbps: 0,
    },
    
    // 屏幕模拟
    screenEmulation: {
      mobile: false,
      width: 1350,
      height: 940,
      deviceScaleFactor: 1,
      disabled: false,
    },
    
    // 用户代理
    emulatedUserAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36 Chrome-Lighthouse',
    
    // 跳过的审计
    skipAudits: [
      // 'uses-http2', // 如果开发环境不支持HTTP/2
    ],
    
    // 仅运行的审计
    onlyAudits: [
      // 如果只想运行特定审计，可以在这里指定
    ],
    
    // 输出格式
    output: ['html', 'json'],
    
    // 输出路径
    outputPath: './lighthouse-reports/',
    
    // 最大等待时间
    maxWaitForLoad: 45000,
    
    // 清除存储
    clearStorageTypes: ['cookies', 'websql', 'indexeddb', 'localstorage'],
  },
};
