{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"next": "15.1.8", "react": "19.1.1", "react-dom": "19.1.1", "@tanstack/react-query": "^5.17.9", "@hookform/resolvers": "^3.3.2", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "zustand": "^4.4.7", "axios": "^1.6.2", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.303.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-select": "^2.0.0", "class-variance-authority": "^0.7.0", "recharts": "^2.8.0", "date-fns": "^3.0.6"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "15.1.8", "eslint-config-custom": "workspace:*", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}