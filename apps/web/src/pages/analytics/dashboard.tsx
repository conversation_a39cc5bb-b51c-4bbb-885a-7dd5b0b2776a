// 数据分析仪表板页面
// 提供数据可视化、图表展示、交互式分析界面

import React, { useState, useEffect } from 'react'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Eye, 
  MousePointer, 
  DollarSign,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Settings
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YA<PERSON>s, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ResponsiveContainer 
} from 'recharts'
import { useToast } from '@/hooks/useToast'
import { useAnalytics } from '@/hooks/useAnalytics'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

// 图表颜色配置
const CHART_COLORS = [
  '#2563eb', '#059669', '#dc2626', '#7c3aed', '#ea580c', '#0891b2', '#be185d', '#4338ca'
]

/**
 * 数据分析仪表板页面组件
 */
export default function AnalyticsDashboardPage() {
  const { toast } = useToast()
  const {
    analytics,
    realTimeMetrics,
    isLoading,
    getAnalytics,
    getRealTimeMetrics
  } = useAnalytics()

  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
    to: new Date()
  })
  const [selectedMetrics, setSelectedMetrics] = useState(['pageViews', 'users', 'sessions'])
  const [groupBy, setGroupBy] = useState('day')
  const [autoRefresh, setAutoRefresh] = useState(true)

  // 加载分析数据
  useEffect(() => {
    loadAnalytics()
  }, [dateRange, selectedMetrics, groupBy])

  // 自动刷新实时数据
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        getRealTimeMetrics()
      }, 30000) // 30秒刷新一次

      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  /**
   * 加载分析数据
   */
  const loadAnalytics = async () => {
    try {
      await getAnalytics({
        startDate: dateRange.from,
        endDate: dateRange.to,
        metrics: selectedMetrics,
        groupBy
      })
    } catch (error: any) {
      toast({
        title: '加载失败',
        description: error.message || '无法加载分析数据',
        variant: 'destructive'
      })
    }
  }

  /**
   * 手动刷新数据
   */
  const handleRefresh = () => {
    loadAnalytics()
    getRealTimeMetrics()
  }

  /**
   * 导出数据
   */
  const handleExport = async (format: 'excel' | 'pdf' | 'csv') => {
    try {
      // 调用导出API
      const response = await fetch('/api/analytics/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          format,
          dateRange,
          metrics: selectedMetrics
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `analytics_${format}_${Date.now()}.${format}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        toast({
          title: '导出成功',
          description: '数据已导出到本地',
          variant: 'success'
        })
      }
    } catch (error) {
      toast({
        title: '导出失败',
        description: '无法导出数据',
        variant: 'destructive'
      })
    }
  }

  /**
   * 格式化数字
   */
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  /**
   * 格式化百分比
   */
  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`
  }

  /**
   * 获取趋势图标
   */
  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'down':
        return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />
      default:
        return <div className="w-4 h-4" />
    }
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面标题和控制栏 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <BarChart3 className="w-8 h-8 mr-3 text-blue-600" />
              数据分析仪表板
            </h1>
            <p className="text-gray-600 mt-2">
              实时监控和分析您的营销数据
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>

            <Select value={groupBy} onValueChange={setGroupBy}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hour">按小时</SelectItem>
                <SelectItem value="day">按天</SelectItem>
                <SelectItem value="week">按周</SelectItem>
                <SelectItem value="month">按月</SelectItem>
              </SelectContent>
            </Select>

            <DatePickerWithRange
              date={dateRange}
              onDateChange={setDateRange}
            />

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('excel')}
            >
              <Download className="w-4 h-4 mr-2" />
              导出
            </Button>
          </div>
        </div>

        {/* 实时指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">页面浏览量</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(analytics?.summary?.totalEvents || 0)}
                  </p>
                  <div className="flex items-center mt-2">
                    {getTrendIcon(analytics?.trends?.find(t => t.metric === 'totalEvents')?.trend || 'stable')}
                    <span className="text-sm text-gray-600 ml-1">
                      {analytics?.trends?.find(t => t.metric === 'totalEvents')?.changePercent || 0}%
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Eye className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">独立访客</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(analytics?.summary?.uniqueUsers || 0)}
                  </p>
                  <div className="flex items-center mt-2">
                    {getTrendIcon(analytics?.trends?.find(t => t.metric === 'uniqueUsers')?.trend || 'stable')}
                    <span className="text-sm text-gray-600 ml-1">
                      {analytics?.trends?.find(t => t.metric === 'uniqueUsers')?.changePercent || 0}%
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-lg">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">会话数</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(analytics?.summary?.totalSessions || 0)}
                  </p>
                  <div className="flex items-center mt-2">
                    <span className="text-sm text-gray-600">
                      平均时长: {Math.round(analytics?.summary?.averageSessionDuration || 0)}分钟
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-purple-100 rounded-lg">
                  <MousePointer className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">转化率</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(analytics?.summary?.conversionRate || 0)}
                  </p>
                  <div className="flex items-center mt-2">
                    {getTrendIcon(analytics?.trends?.find(t => t.metric === 'conversionRate')?.trend || 'stable')}
                    <span className="text-sm text-gray-600 ml-1">
                      跳出率: {formatPercentage(analytics?.summary?.bounceRate || 0)}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* 趋势图 */}
          <Card>
            <CardHeader>
              <CardTitle>访问趋势</CardTitle>
              <CardDescription>
                页面浏览量和独立访客趋势
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analytics?.data || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="metrics.count" 
                    stroke={CHART_COLORS[0]} 
                    strokeWidth={2}
                    name="页面浏览量"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="metrics.unique_users" 
                    stroke={CHART_COLORS[1]} 
                    strokeWidth={2}
                    name="独立访客"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* 设备分布 */}
          <Card>
            <CardHeader>
              <CardTitle>设备分布</CardTitle>
              <CardDescription>
                用户使用的设备类型分布
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={[
                      { name: '桌面端', value: 45, color: CHART_COLORS[0] },
                      { name: '移动端', value: 40, color: CHART_COLORS[1] },
                      { name: '平板端', value: 15, color: CHART_COLORS[2] }
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {[
                      { name: '桌面端', value: 45, color: CHART_COLORS[0] },
                      { name: '移动端', value: 40, color: CHART_COLORS[1] },
                      { name: '平板端', value: 15, color: CHART_COLORS[2] }
                    ].map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* 详细分析标签页 */}
        <Tabs defaultValue="traffic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="traffic">流量分析</TabsTrigger>
            <TabsTrigger value="behavior">用户行为</TabsTrigger>
            <TabsTrigger value="conversion">转化分析</TabsTrigger>
            <TabsTrigger value="realtime">实时数据</TabsTrigger>
          </TabsList>

          <TabsContent value="traffic" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>流量来源</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={[
                      { source: '直接访问', visitors: 1200 },
                      { source: '搜索引擎', visitors: 800 },
                      { source: '社交媒体', visitors: 600 },
                      { source: '邮件营销', visitors: 400 },
                      { source: '付费广告', visitors: 300 }
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="source" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="visitors" fill={CHART_COLORS[0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>热门页面</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { page: '/dashboard', views: 2500, percentage: 35 },
                      { page: '/ai/text-generator', views: 1800, percentage: 25 },
                      { page: '/marketing/campaigns', views: 1200, percentage: 17 },
                      { page: '/analytics', views: 900, percentage: 13 },
                      { page: '/settings', views: 700, percentage: 10 }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="font-medium text-sm">{item.page}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Progress value={item.percentage} className="flex-1 h-2" />
                            <span className="text-xs text-gray-500">{item.percentage}%</span>
                          </div>
                        </div>
                        <div className="ml-4 text-right">
                          <p className="font-medium text-sm">{item.views.toLocaleString()}</p>
                          <p className="text-xs text-gray-500">浏览量</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="behavior" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>用户留存</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <AreaChart data={[
                      { day: '第1天', retention: 100 },
                      { day: '第3天', retention: 65 },
                      { day: '第7天', retention: 45 },
                      { day: '第14天', retention: 32 },
                      { day: '第30天', retention: 25 }
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value}%`, '留存率']} />
                      <Area 
                        type="monotone" 
                        dataKey="retention" 
                        stroke={CHART_COLORS[2]} 
                        fill={CHART_COLORS[2]}
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>用户活跃度</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">日活跃用户</span>
                      <Badge variant="secondary">1,234</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">周活跃用户</span>
                      <Badge variant="secondary">5,678</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">月活跃用户</span>
                      <Badge variant="secondary">12,345</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">平均会话时长</span>
                      <Badge variant="secondary">8分32秒</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">页面/会话</span>
                      <Badge variant="secondary">3.2</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="conversion" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>转化漏斗</CardTitle>
                <CardDescription>
                  用户从访问到转化的完整路径
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { step: '访问网站', users: 10000, rate: 100 },
                    { step: '注册账户', users: 3000, rate: 30 },
                    { step: '完善资料', users: 2400, rate: 80 },
                    { step: '首次使用', users: 1800, rate: 75 },
                    { step: '付费订阅', users: 540, rate: 30 }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <div className="w-24 text-sm font-medium">{item.step}</div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">{item.users.toLocaleString()} 用户</span>
                          <span className="text-sm text-gray-500">{item.rate}%</span>
                        </div>
                        <Progress value={item.rate} className="h-3" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="realtime" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                    实时访客
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{realTimeMetrics?.activeUsers || 0}</p>
                  <p className="text-sm text-gray-600 mt-2">当前在线用户</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>今日浏览量</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{realTimeMetrics?.todayPageViews || 0}</p>
                  <p className="text-sm text-gray-600 mt-2">
                    比昨天 {realTimeMetrics?.pageViewsChange > 0 ? '+' : ''}{realTimeMetrics?.pageViewsChange || 0}%
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>实时转化</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{realTimeMetrics?.todayConversions || 0}</p>
                  <p className="text-sm text-gray-600 mt-2">今日转化次数</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>实时活动</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { time: '刚刚', event: '用户从北京访问了首页', type: 'visit' },
                    { time: '1分钟前', event: '用户完成了注册', type: 'signup' },
                    { time: '2分钟前', event: '用户生成了AI文案', type: 'generation' },
                    { time: '3分钟前', event: '用户创建了营销活动', type: 'campaign' },
                    { time: '5分钟前', event: '用户订阅了付费计划', type: 'conversion' }
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm">{activity.event}</p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {activity.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: '数据分析仪表板 - AI数字营销平台',
      description: '实时监控和分析您的营销数据'
    }
  }
}
