// AI文本生成页面
// 提供AI文案生成功能，包括参数配置、实时预览和结果展示界面

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  Wand2, 
  Copy, 
  Download, 
  Heart, 
  RefreshCw, 
  Settings, 
  Sparkles,
  FileText,
  MessageSquare,
  Mail,
  Globe,
  Megaphone,
  Tag
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/useToast'
import { useAIGeneration } from '@/hooks/useAIGeneration'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

// 内容类型选项
const contentTypes = [
  { value: 'MARKETING_COPY', label: '营销文案', icon: Megaphone, description: '产品推广、品牌宣传文案' },
  { value: 'SOCIAL_MEDIA', label: '社交媒体', icon: MessageSquare, description: '微博、朋友圈、短视频文案' },
  { value: 'EMAIL_SUBJECT', label: '邮件主题', icon: Mail, description: '营销邮件、通知邮件主题' },
  { value: 'EMAIL_CONTENT', label: '邮件内容', icon: Mail, description: '完整的邮件营销内容' },
  { value: 'BLOG_POST', label: '博客文章', icon: FileText, description: '行业洞察、产品介绍文章' },
  { value: 'PRODUCT_DESCRIPTION', label: '产品描述', icon: Tag, description: '电商产品详情描述' },
  { value: 'AD_COPY', label: '广告文案', icon: Megaphone, description: '搜索广告、展示广告文案' },
  { value: 'SLOGAN', label: '品牌口号', icon: Sparkles, description: '朗朗上口的品牌标语' },
  { value: 'LANDING_PAGE', label: '着陆页', icon: Globe, description: '转化导向的页面内容' }
]

// 语调选项
const toneOptions = [
  { value: 'professional', label: '专业正式' },
  { value: 'casual', label: '轻松随意' },
  { value: 'friendly', label: '友好亲切' },
  { value: 'urgent', label: '紧迫有力' },
  { value: 'persuasive', label: '说服性强' },
  { value: 'informative', label: '信息丰富' }
]

// 语言选项
const languageOptions = [
  { value: 'zh-CN', label: '中文' },
  { value: 'en', label: 'English' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' }
]

// 表单验证模式
const textGenerationSchema = z.object({
  type: z.string().min(1, '请选择内容类型'),
  prompt: z.string().min(10, '提示词至少需要10个字符').max(2000, '提示词不能超过2000个字符'),
  targetAudience: z.string().optional(),
  tone: z.string().optional(),
  language: z.string().optional(),
  keywords: z.string().optional(),
  maxLength: z.number().min(50).max(5000).optional(),
  includeEmoji: z.boolean().optional(),
  includeHashtags: z.boolean().optional(),
  brandVoice: z.string().optional()
})

type TextGenerationFormData = z.infer<typeof textGenerationSchema>

/**
 * AI文本生成页面组件
 */
export default function TextGeneratorPage() {
  const { toast } = useToast()
  const { generateText, isLoading, usage } = useAIGeneration()
  
  const [selectedType, setSelectedType] = useState<string>('')
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [alternatives, setAlternatives] = useState<string[]>([])
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [isFavorite, setIsFavorite] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)

  // 表单处理
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset
  } = useForm<TextGenerationFormData>({
    resolver: zodResolver(textGenerationSchema),
    defaultValues: {
      language: 'zh-CN',
      tone: 'professional',
      maxLength: 500,
      includeEmoji: false,
      includeHashtags: false
    }
  })

  const watchedValues = watch()

  /**
   * 处理内容生成
   */
  const onSubmit = async (data: TextGenerationFormData) => {
    try {
      setGenerationProgress(0)
      
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      // 处理关键词
      const keywords = data.keywords 
        ? data.keywords.split(/[,，\s]+/).filter(Boolean)
        : undefined

      const result = await generateText({
        type: data.type as any,
        prompt: data.prompt,
        targetAudience: data.targetAudience,
        tone: data.tone as any,
        language: data.language,
        keywords,
        maxLength: data.maxLength,
        includeEmoji: data.includeEmoji,
        includeHashtags: data.includeHashtags,
        brandVoice: data.brandVoice
      })

      clearInterval(progressInterval)
      setGenerationProgress(100)

      setGeneratedContent(result.content)
      setAlternatives(result.alternatives || [])
      setIsFavorite(false)

      toast({
        title: '生成成功',
        description: 'AI文案已生成完成！',
        variant: 'success'
      })

      // 重置进度
      setTimeout(() => setGenerationProgress(0), 1000)
    } catch (error: any) {
      setGenerationProgress(0)
      toast({
        title: '生成失败',
        description: error.message || 'AI文案生成失败，请重试',
        variant: 'destructive'
      })
    }
  }

  /**
   * 复制内容到剪贴板
   */
  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast({
        title: '复制成功',
        description: '内容已复制到剪贴板',
        variant: 'success'
      })
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制到剪贴板',
        variant: 'destructive'
      })
    }
  }

  /**
   * 使用替代方案
   */
  const useAlternative = (content: string) => {
    setGeneratedContent(content)
    setAlternatives(prev => prev.filter(alt => alt !== content))
  }

  /**
   * 重新生成
   */
  const regenerate = () => {
    handleSubmit(onSubmit)()
  }

  /**
   * 获取内容类型图标
   */
  const getTypeIcon = (type: string) => {
    const typeConfig = contentTypes.find(t => t.value === type)
    return typeConfig?.icon || FileText
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Wand2 className="w-8 h-8 mr-3 text-blue-600" />
            AI文案生成器
          </h1>
          <p className="text-gray-600 mt-2">
            使用AI技术快速生成高质量的营销文案和内容
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：配置面板 */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="w-5 h-5 mr-2" />
                  生成配置
                </CardTitle>
                <CardDescription>
                  配置AI生成参数以获得最佳效果
                </CardDescription>
              </CardHeader>

              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* 内容类型选择 */}
                  <div className="space-y-3">
                    <Label>内容类型</Label>
                    <div className="grid grid-cols-1 gap-2">
                      {contentTypes.map((type) => {
                        const Icon = type.icon
                        return (
                          <div
                            key={type.value}
                            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                              selectedType === type.value
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => {
                              setSelectedType(type.value)
                              setValue('type', type.value)
                            }}
                          >
                            <div className="flex items-start space-x-3">
                              <Icon className="w-5 h-5 text-blue-600 mt-0.5" />
                              <div>
                                <div className="font-medium text-sm">{type.label}</div>
                                <div className="text-xs text-gray-500">{type.description}</div>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                    {errors.type && (
                      <p className="text-sm text-red-600">{errors.type.message}</p>
                    )}
                  </div>

                  {/* 提示词输入 */}
                  <div className="space-y-2">
                    <Label htmlFor="prompt">提示词</Label>
                    <Textarea
                      id="prompt"
                      placeholder="请详细描述您想要生成的内容，包括产品信息、目标受众、关键信息等..."
                      rows={4}
                      {...register('prompt')}
                      disabled={isSubmitting || isLoading}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{watchedValues.prompt?.length || 0} / 2000</span>
                      <span>详细的描述有助于生成更好的内容</span>
                    </div>
                    {errors.prompt && (
                      <p className="text-sm text-red-600">{errors.prompt.message}</p>
                    )}
                  </div>

                  {/* 基础配置 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="language">语言</Label>
                      <Select
                        value={watchedValues.language}
                        onValueChange={(value) => setValue('language', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {languageOptions.map((lang) => (
                            <SelectItem key={lang.value} value={lang.value}>
                              {lang.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tone">语调风格</Label>
                      <Select
                        value={watchedValues.tone}
                        onValueChange={(value) => setValue('tone', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {toneOptions.map((tone) => (
                            <SelectItem key={tone.value} value={tone.value}>
                              {tone.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* 高级配置 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>高级配置</Label>
                      <Switch
                        checked={showAdvanced}
                        onCheckedChange={setShowAdvanced}
                      />
                    </div>

                    {showAdvanced && (
                      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                        <div className="space-y-2">
                          <Label htmlFor="targetAudience">目标受众</Label>
                          <Input
                            id="targetAudience"
                            placeholder="例如：25-35岁职场女性"
                            {...register('targetAudience')}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="keywords">关键词</Label>
                          <Input
                            id="keywords"
                            placeholder="用逗号分隔多个关键词"
                            {...register('keywords')}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="brandVoice">品牌声音</Label>
                          <Input
                            id="brandVoice"
                            placeholder="例如：年轻活力、专业可信"
                            {...register('brandVoice')}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>内容长度：{watchedValues.maxLength} 字符</Label>
                          <Slider
                            value={[watchedValues.maxLength || 500]}
                            onValueChange={([value]) => setValue('maxLength', value)}
                            min={50}
                            max={5000}
                            step={50}
                            className="w-full"
                          />
                        </div>

                        {selectedType === 'SOCIAL_MEDIA' && (
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <Switch
                                id="includeEmoji"
                                checked={watchedValues.includeEmoji}
                                onCheckedChange={(checked) => setValue('includeEmoji', checked)}
                              />
                              <Label htmlFor="includeEmoji">包含表情符号</Label>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Switch
                                id="includeHashtags"
                                checked={watchedValues.includeHashtags}
                                onCheckedChange={(checked) => setValue('includeHashtags', checked)}
                              />
                              <Label htmlFor="includeHashtags">包含话题标签</Label>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 生成按钮 */}
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting || isLoading || !selectedType}
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        生成内容
                      </>
                    )}
                  </Button>

                  {/* 生成进度 */}
                  {generationProgress > 0 && generationProgress < 100 && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>生成进度</span>
                        <span>{generationProgress}%</span>
                      </div>
                      <Progress value={generationProgress} className="w-full" />
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>

            {/* 使用统计 */}
            {usage && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">今日使用情况</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>文本生成</span>
                    <span>{usage.textRequests}/100</span>
                  </div>
                  <Progress value={(usage.textRequests / 100) * 100} className="h-2" />
                  <div className="text-xs text-gray-500">
                    剩余 {100 - usage.textRequests} 次
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* 右侧：结果展示 */}
          <div className="lg:col-span-2 space-y-6">
            {generatedContent ? (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      {React.createElement(getTypeIcon(selectedType), { className: "w-5 h-5 mr-2" })}
                      生成结果
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsFavorite(!isFavorite)}
                      >
                        <Heart className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(generatedContent)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={regenerate}
                        disabled={isLoading}
                      >
                        <RefreshCw className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="whitespace-pre-wrap text-gray-900">
                        {generatedContent}
                      </div>
                    </div>

                    {/* 内容分析 */}
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="font-medium text-gray-900">
                          {generatedContent.length}
                        </div>
                        <div className="text-gray-500">字符数</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-gray-900">
                          {generatedContent.split(/\s+/).length}
                        </div>
                        <div className="text-gray-500">词数</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-gray-900">
                          {Math.ceil(generatedContent.split(/\s+/).length / 200)}
                        </div>
                        <div className="text-gray-500">预计阅读时间(分钟)</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Wand2 className="w-12 h-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    开始创作您的内容
                  </h3>
                  <p className="text-gray-500 text-center max-w-md">
                    选择内容类型，输入详细的提示词，让AI为您生成高质量的营销文案
                  </p>
                </CardContent>
              </Card>
            )}

            {/* 替代方案 */}
            {alternatives.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>替代方案</CardTitle>
                  <CardDescription>
                    AI为您提供了其他创意版本
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {alternatives.map((alt, index) => (
                      <div
                        key={index}
                        className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                        onClick={() => useAlternative(alt)}
                      >
                        <div className="whitespace-pre-wrap text-gray-900 mb-2">
                          {alt}
                        </div>
                        <div className="flex justify-between items-center">
                          <Badge variant="outline">方案 {index + 1}</Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              copyToClipboard(alt)
                            }}
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: 'AI文案生成器 - AI数字营销平台',
      description: '使用AI技术快速生成高质量的营销文案和内容'
    }
  }
}
