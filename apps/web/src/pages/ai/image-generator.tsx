// AI图像生成页面
// 提供AI图像生成功能，包括参数配置、实时预览和结果展示界面

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  Image as ImageIcon, 
  Download, 
  Heart, 
  RefreshCw, 
  Settings, 
  Sparkles,
  Camera,
  Palette,
  Zap,
  Grid,
  Maximize,
  Copy,
  Share2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/useToast'
import { useAIGeneration } from '@/hooks/useAIGeneration'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

// 图像风格选项
const styleOptions = [
  { value: 'photographic', label: '摄影风格', description: '真实照片效果' },
  { value: 'digital_art', label: '数字艺术', description: '现代数字插画' },
  { value: 'comic_book', label: '漫画风格', description: '卡通漫画效果' },
  { value: 'fantasy_art', label: '奇幻艺术', description: '魔幻风格插画' },
  { value: 'line_art', label: '线条艺术', description: '简洁线条风格' },
  { value: 'anime', label: '动漫风格', description: '日式动漫风格' },
  { value: 'cinematic', label: '电影风格', description: '电影镜头效果' }
]

// 尺寸选项
const sizeOptions = [
  { value: '1024x1024', label: '正方形 (1:1)', description: '1024×1024' },
  { value: '1792x1024', label: '横向 (16:9)', description: '1792×1024' },
  { value: '1024x1792', label: '纵向 (9:16)', description: '1024×1792' },
  { value: '512x512', label: '小正方形', description: '512×512' }
]

// 质量选项
const qualityOptions = [
  { value: 'standard', label: '标准质量', description: '平衡速度和质量' },
  { value: 'hd', label: '高清质量', description: '更高质量，耗时更长' }
]

// 表单验证模式
const imageGenerationSchema = z.object({
  prompt: z.string().min(10, '提示词至少需要10个字符').max(1000, '提示词不能超过1000个字符'),
  style: z.string().optional(),
  size: z.string().optional(),
  quality: z.string().optional(),
  count: z.number().min(1).max(4).optional(),
  negativePrompt: z.string().optional()
})

type ImageGenerationFormData = z.infer<typeof imageGenerationSchema>

/**
 * AI图像生成页面组件
 */
export default function ImageGeneratorPage() {
  const { toast } = useToast()
  const { generateImage, isLoading, usage } = useAIGeneration()
  
  const [generatedImages, setGeneratedImages] = useState<any[]>([])
  const [selectedImage, setSelectedImage] = useState<any>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)

  // 表单处理
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset
  } = useForm<ImageGenerationFormData>({
    resolver: zodResolver(imageGenerationSchema),
    defaultValues: {
      style: 'photographic',
      size: '1024x1024',
      quality: 'standard',
      count: 1
    }
  })

  const watchedValues = watch()

  /**
   * 处理图像生成
   */
  const onSubmit = async (data: ImageGenerationFormData) => {
    try {
      setGenerationProgress(0)
      
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 5, 90))
      }, 500)

      const result = await generateImage({
        prompt: data.prompt,
        style: data.style as any,
        size: data.size as any,
        quality: data.quality as any,
        count: data.count || 1,
        negativePrompt: data.negativePrompt
      })

      clearInterval(progressInterval)
      setGenerationProgress(100)

      setGeneratedImages(result.images)

      toast({
        title: '生成成功',
        description: `成功生成 ${result.images.length} 张图像！`,
        variant: 'success'
      })

      // 重置进度
      setTimeout(() => setGenerationProgress(0), 1000)
    } catch (error: any) {
      setGenerationProgress(0)
      toast({
        title: '生成失败',
        description: error.message || 'AI图像生成失败，请重试',
        variant: 'destructive'
      })
    }
  }

  /**
   * 下载图像
   */
  const downloadImage = async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(url)
      
      toast({
        title: '下载成功',
        description: '图像已保存到本地',
        variant: 'success'
      })
    } catch (error) {
      toast({
        title: '下载失败',
        description: '无法下载图像',
        variant: 'destructive'
      })
    }
  }

  /**
   * 复制图像URL
   */
  const copyImageUrl = async (imageUrl: string) => {
    try {
      await navigator.clipboard.writeText(imageUrl)
      toast({
        title: '复制成功',
        description: '图像链接已复制到剪贴板',
        variant: 'success'
      })
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制图像链接',
        variant: 'destructive'
      })
    }
  }

  /**
   * 重新生成
   */
  const regenerate = () => {
    handleSubmit(onSubmit)()
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <ImageIcon className="w-8 h-8 mr-3 text-purple-600" />
            AI图像生成器
          </h1>
          <p className="text-gray-600 mt-2">
            使用AI技术创造独特的图像和艺术作品
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：配置面板 */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="w-5 h-5 mr-2" />
                  生成配置
                </CardTitle>
                <CardDescription>
                  配置AI图像生成参数
                </CardDescription>
              </CardHeader>

              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* 提示词输入 */}
                  <div className="space-y-2">
                    <Label htmlFor="prompt">图像描述</Label>
                    <Textarea
                      id="prompt"
                      placeholder="详细描述您想要生成的图像，例如：一只可爱的橙色小猫坐在阳光明媚的花园里..."
                      rows={4}
                      {...register('prompt')}
                      disabled={isSubmitting || isLoading}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{watchedValues.prompt?.length || 0} / 1000</span>
                      <span>详细描述有助于生成更好的图像</span>
                    </div>
                    {errors.prompt && (
                      <p className="text-sm text-red-600">{errors.prompt.message}</p>
                    )}
                  </div>

                  {/* 基础配置 */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>图像风格</Label>
                      <Select
                        value={watchedValues.style}
                        onValueChange={(value) => setValue('style', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {styleOptions.map((style) => (
                            <SelectItem key={style.value} value={style.value}>
                              <div>
                                <div className="font-medium">{style.label}</div>
                                <div className="text-xs text-gray-500">{style.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>图像尺寸</Label>
                        <Select
                          value={watchedValues.size}
                          onValueChange={(value) => setValue('size', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {sizeOptions.map((size) => (
                              <SelectItem key={size.value} value={size.value}>
                                <div>
                                  <div className="font-medium">{size.label}</div>
                                  <div className="text-xs text-gray-500">{size.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>图像质量</Label>
                        <Select
                          value={watchedValues.quality}
                          onValueChange={(value) => setValue('quality', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {qualityOptions.map((quality) => (
                              <SelectItem key={quality.value} value={quality.value}>
                                <div>
                                  <div className="font-medium">{quality.label}</div>
                                  <div className="text-xs text-gray-500">{quality.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>生成数量：{watchedValues.count} 张</Label>
                      <Slider
                        value={[watchedValues.count || 1]}
                        onValueChange={([value]) => setValue('count', value)}
                        min={1}
                        max={4}
                        step={1}
                        className="w-full"
                      />
                      <div className="text-xs text-gray-500">
                        一次最多生成4张图像
                      </div>
                    </div>
                  </div>

                  {/* 高级配置 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>高级配置</Label>
                      <Switch
                        checked={showAdvanced}
                        onCheckedChange={setShowAdvanced}
                      />
                    </div>

                    {showAdvanced && (
                      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                        <div className="space-y-2">
                          <Label htmlFor="negativePrompt">负面提示词</Label>
                          <Textarea
                            id="negativePrompt"
                            placeholder="描述您不希望在图像中出现的元素，例如：模糊、低质量、变形..."
                            rows={3}
                            {...register('negativePrompt')}
                          />
                          <div className="text-xs text-gray-500">
                            指定要避免的元素可以提高图像质量
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 生成按钮 */}
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting || isLoading}
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        生成图像
                      </>
                    )}
                  </Button>

                  {/* 生成进度 */}
                  {generationProgress > 0 && generationProgress < 100 && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>生成进度</span>
                        <span>{generationProgress}%</span>
                      </div>
                      <Progress value={generationProgress} className="w-full" />
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>

            {/* 使用统计 */}
            {usage && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">今日使用情况</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>图像生成</span>
                    <span>{usage.imageRequests}/20</span>
                  </div>
                  <Progress value={(usage.imageRequests / 20) * 100} className="h-2" />
                  <div className="text-xs text-gray-500">
                    剩余 {20 - usage.imageRequests} 次
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* 右侧：结果展示 */}
          <div className="lg:col-span-2 space-y-6">
            {generatedImages.length > 0 ? (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      <ImageIcon className="w-5 h-5 mr-2" />
                      生成结果
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={regenerate}
                        disabled={isLoading}
                      >
                        <RefreshCw className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {generatedImages.map((image, index) => (
                      <div key={image.id} className="space-y-4">
                        <div className="relative group">
                          <img
                            src={image.thumbnailUrl || image.url}
                            alt={`Generated image ${index + 1}`}
                            className="w-full h-64 object-cover rounded-lg cursor-pointer"
                            onClick={() => {
                              setSelectedImage(image)
                              setPreviewDialogOpen(true)
                            }}
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <Button
                              variant="secondary"
                              size="sm"
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => {
                                setSelectedImage(image)
                                setPreviewDialogOpen(true)
                              }}
                            >
                              <Maximize className="w-4 h-4 mr-2" />
                              查看大图
                            </Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="text-sm text-gray-600">
                            {image.metadata.width} × {image.metadata.height}
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyImageUrl(image.url)}
                            >
                              <Copy className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => downloadImage(image.url, `generated_image_${index + 1}.png`)}
                            >
                              <Download className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Camera className="w-12 h-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    开始创作您的图像
                  </h3>
                  <p className="text-gray-500 text-center max-w-md">
                    输入详细的图像描述，选择合适的风格和参数，让AI为您创造独特的艺术作品
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* 图像预览对话框 */}
        <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>图像预览</DialogTitle>
            </DialogHeader>
            {selectedImage && (
              <div className="space-y-4">
                <img
                  src={selectedImage.url}
                  alt="Generated image preview"
                  className="w-full h-auto rounded-lg"
                />
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="text-sm font-medium">
                      尺寸: {selectedImage.metadata.width} × {selectedImage.metadata.height}
                    </div>
                    <div className="text-sm text-gray-600">
                      格式: {selectedImage.metadata.format.toUpperCase()}
                    </div>
                    <div className="text-sm text-gray-600">
                      大小: {(selectedImage.metadata.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => copyImageUrl(selectedImage.url)}
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      复制链接
                    </Button>
                    <Button
                      onClick={() => downloadImage(selectedImage.url, `generated_image_${selectedImage.id}.png`)}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      下载图像
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: 'AI图像生成器 - AI数字营销平台',
      description: '使用AI技术创造独特的图像和艺术作品'
    }
  }
}
