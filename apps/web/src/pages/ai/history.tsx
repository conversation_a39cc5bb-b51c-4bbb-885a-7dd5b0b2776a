// AI生成历史页面
// 显示用户的AI生成历史记录，支持搜索、筛选、收藏和导出功能

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { 
  History, 
  Search, 
  Filter, 
  Download, 
  Heart, 
  Trash2, 
  Eye,
  FileText,
  Image as ImageIcon,
  Calendar,
  Tag,
  MoreHorizontal,
  Star,
  Copy,
  Edit
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Pagination } from '@/components/ui/pagination'
import { useToast } from '@/hooks/useToast'
import { useAIHistory } from '@/hooks/useAIHistory'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

// 筛选选项
const typeOptions = [
  { value: 'ALL', label: '全部类型' },
  { value: 'TEXT_GENERATION', label: '文本生成' },
  { value: 'IMAGE_GENERATION', label: '图像生成' }
]

const sortOptions = [
  { value: 'createdAt', label: '创建时间' },
  { value: 'updatedAt', label: '更新时间' },
  { value: 'prompt', label: '提示词' }
]

const sortOrderOptions = [
  { value: 'desc', label: '降序' },
  { value: 'asc', label: '升序' }
]

/**
 * AI生成历史页面组件
 */
export default function AIHistoryPage() {
  const router = useRouter()
  const { toast } = useToast()
  const {
    history,
    stats,
    isLoading,
    pagination,
    getHistory,
    updateHistoryItem,
    deleteHistoryItem,
    batchDeleteItems,
    exportHistory
  } = useAIHistory()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState('ALL')
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [favoriteOnly, setFavoriteOnly] = useState(false)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)

  // 加载历史记录
  useEffect(() => {
    loadHistory()
  }, [currentPage, selectedType, sortBy, sortOrder, favoriteOnly])

  /**
   * 加载历史记录
   */
  const loadHistory = async () => {
    try {
      await getHistory({
        page: currentPage,
        limit: 20,
        type: selectedType as any,
        search: searchQuery,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
        favoriteOnly
      })
    } catch (error: any) {
      toast({
        title: '加载失败',
        description: error.message || '无法加载历史记录',
        variant: 'destructive'
      })
    }
  }

  /**
   * 搜索处理
   */
  const handleSearch = () => {
    setCurrentPage(1)
    loadHistory()
  }

  /**
   * 切换收藏状态
   */
  const toggleFavorite = async (itemId: string, currentFavorite: boolean) => {
    try {
      await updateHistoryItem(itemId, { isFavorite: !currentFavorite })
      toast({
        title: currentFavorite ? '取消收藏' : '添加收藏',
        description: '操作成功',
        variant: 'success'
      })
      loadHistory()
    } catch (error: any) {
      toast({
        title: '操作失败',
        description: error.message || '无法更新收藏状态',
        variant: 'destructive'
      })
    }
  }

  /**
   * 删除单个项目
   */
  const handleDeleteItem = async (itemId: string) => {
    try {
      await deleteHistoryItem(itemId)
      toast({
        title: '删除成功',
        description: '历史记录已删除',
        variant: 'success'
      })
      loadHistory()
    } catch (error: any) {
      toast({
        title: '删除失败',
        description: error.message || '无法删除历史记录',
        variant: 'destructive'
      })
    }
  }

  /**
   * 批量删除
   */
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) return

    try {
      await batchDeleteItems(selectedItems)
      toast({
        title: '批量删除成功',
        description: `已删除 ${selectedItems.length} 条记录`,
        variant: 'success'
      })
      setSelectedItems([])
      loadHistory()
    } catch (error: any) {
      toast({
        title: '批量删除失败',
        description: error.message || '无法删除选中的记录',
        variant: 'destructive'
      })
    }
  }

  /**
   * 导出历史记录
   */
  const handleExport = async (format: string) => {
    try {
      const filePath = await exportHistory({
        format: format as any,
        items: selectedItems.length > 0 ? selectedItems : history.map(item => item.id),
        includeMetadata: true
      })
      
      toast({
        title: '导出成功',
        description: '文件已生成，正在下载...',
        variant: 'success'
      })
      
      // 触发下载
      window.open(`/api/download?file=${encodeURIComponent(filePath)}`, '_blank')
      setExportDialogOpen(false)
    } catch (error: any) {
      toast({
        title: '导出失败',
        description: error.message || '无法导出历史记录',
        variant: 'destructive'
      })
    }
  }

  /**
   * 复制内容
   */
  const copyContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast({
        title: '复制成功',
        description: '内容已复制到剪贴板',
        variant: 'success'
      })
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制内容',
        variant: 'destructive'
      })
    }
  }

  /**
   * 选择/取消选择项目
   */
  const toggleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = () => {
    if (selectedItems.length === history.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(history.map(item => item.id))
    }
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: string) => {
    return new Date(date).toLocaleString('zh-CN')
  }

  /**
   * 获取类型图标
   */
  const getTypeIcon = (type: string) => {
    return type === 'TEXT_GENERATION' ? FileText : ImageIcon
  }

  /**
   * 获取类型标签
   */
  const getTypeLabel = (type: string) => {
    return type === 'TEXT_GENERATION' ? '文本生成' : '图像生成'
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <History className="w-8 h-8 mr-3 text-green-600" />
            AI生成历史
          </h1>
          <p className="text-gray-600 mt-2">
            查看和管理您的AI生成历史记录
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总记录数</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.totalItems || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FileText className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">文本生成</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.textGenerations || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <ImageIcon className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">图像生成</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.imageGenerations || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Heart className="w-6 h-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">收藏数量</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.favoriteCount || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 筛选和搜索 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              筛选和搜索
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="search">搜索</Label>
                <div className="flex space-x-2">
                  <Input
                    id="search"
                    placeholder="搜索提示词或结果..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <Button onClick={handleSearch}>
                    <Search className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label>类型</Label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {typeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>排序字段</Label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>排序方式</Label>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOrderOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="favoriteOnly"
                    checked={favoriteOnly}
                    onCheckedChange={setFavoriteOnly}
                  />
                  <Label htmlFor="favoriteOnly">仅显示收藏</Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 批量操作 */}
        {selectedItems.length > 0 && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  已选择 {selectedItems.length} 项
                </span>
                <div className="flex items-center space-x-2">
                  <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        导出
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>导出历史记录</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <p className="text-sm text-gray-600">
                          选择导出格式：
                        </p>
                        <div className="grid grid-cols-2 gap-4">
                          <Button onClick={() => handleExport('excel')}>
                            Excel 格式
                          </Button>
                          <Button onClick={() => handleExport('pdf')}>
                            PDF 格式
                          </Button>
                          <Button onClick={() => handleExport('json')}>
                            JSON 格式
                          </Button>
                          <Button onClick={() => handleExport('csv')}>
                            CSV 格式
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBatchDelete}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    删除
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 历史记录列表 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>历史记录</CardTitle>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedItems.length === history.length && history.length > 0}
                  onCheckedChange={toggleSelectAll}
                />
                <Label>全选</Label>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : history.length === 0 ? (
              <div className="text-center py-8">
                <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  暂无历史记录
                </h3>
                <p className="text-gray-500">
                  开始使用AI生成功能来创建您的第一条记录
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {history.map((item) => {
                  const TypeIcon = getTypeIcon(item.type)
                  return (
                    <div
                      key={item.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-start space-x-4">
                        <Checkbox
                          checked={selectedItems.includes(item.id)}
                          onCheckedChange={() => toggleSelectItem(item.id)}
                        />
                        
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <TypeIcon className="w-5 h-5 text-gray-600" />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant="outline">
                              {getTypeLabel(item.type)}
                            </Badge>
                            {item.isFavorite && (
                              <Heart className="w-4 h-4 text-red-500 fill-current" />
                            )}
                            <span className="text-sm text-gray-500">
                              {formatDate(item.createdAt)}
                            </span>
                          </div>

                          <h3 className="font-medium text-gray-900 mb-2 truncate">
                            {item.prompt}
                          </h3>

                          <p className="text-sm text-gray-600 line-clamp-2">
                            {item.type === 'TEXT_GENERATION' 
                              ? item.result 
                              : '图像生成结果'
                            }
                          </p>

                          {item.tags && item.tags.length > 0 && (
                            <div className="flex items-center space-x-1 mt-2">
                              <Tag className="w-3 h-3 text-gray-400" />
                              {item.tags.map((tag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleFavorite(item.id, item.isFavorite)}
                          >
                            <Heart className={`w-4 h-4 ${item.isFavorite ? 'text-red-500 fill-current' : ''}`} />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedItem(item)
                              setPreviewDialogOpen(true)
                            }}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem
                                onClick={() => copyContent(item.type === 'TEXT_GENERATION' ? item.result : item.prompt)}
                              >
                                <Copy className="w-4 h-4 mr-2" />
                                复制内容
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteItem(item.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}

            {/* 分页 */}
            {pagination && pagination.totalPages > 1 && (
              <div className="mt-6">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.totalPages}
                  onPageChange={setCurrentPage}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* 预览对话框 */}
        <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>详细预览</DialogTitle>
            </DialogHeader>
            {selectedItem && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">
                    {getTypeLabel(selectedItem.type)}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    {formatDate(selectedItem.createdAt)}
                  </span>
                </div>

                <div>
                  <h3 className="font-medium mb-2">提示词：</h3>
                  <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                    {selectedItem.prompt}
                  </p>
                </div>

                <div>
                  <h3 className="font-medium mb-2">生成结果：</h3>
                  {selectedItem.type === 'TEXT_GENERATION' ? (
                    <p className="text-gray-700 bg-gray-50 p-3 rounded-lg whitespace-pre-wrap">
                      {selectedItem.result}
                    </p>
                  ) : (
                    <div className="grid grid-cols-2 gap-4">
                      {JSON.parse(selectedItem.result).map((image: any, index: number) => (
                        <img
                          key={index}
                          src={image.url}
                          alt={`Generated image ${index + 1}`}
                          className="w-full h-auto rounded-lg"
                        />
                      ))}
                    </div>
                  )}
                </div>

                {selectedItem.metadata && (
                  <div>
                    <h3 className="font-medium mb-2">元数据：</h3>
                    <pre className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg overflow-auto">
                      {JSON.stringify(selectedItem.metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: 'AI生成历史 - AI数字营销平台',
      description: '查看和管理您的AI生成历史记录'
    }
  }
}
