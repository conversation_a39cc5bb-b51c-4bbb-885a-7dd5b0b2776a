// 订阅管理页面
// 显示当前订阅状态、计划对比、升级降级等功能

import React, { useState, useEffect } from 'react'
import { 
  CreditCard, 
  Check, 
  X, 
  Star, 
  Calendar, 
  DollarSign,
  Users,
  Zap,
  Shield,
  Crown,
  AlertCircle,
  RefreshCw
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/useToast'
import { useSubscription } from '@/hooks/useSubscription'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

/**
 * 订阅计划配置
 */
const PLANS = [
  {
    id: 'free',
    name: '免费版',
    price: 0,
    interval: 'month',
    description: '适合个人用户和小团队',
    features: [
      '基础AI文案生成',
      '简单邮件营销',
      '基础数据分析',
      '社区支持'
    ],
    limits: {
      aiGenerations: 10,
      emailSends: 100,
      campaigns: 1,
      automations: 1,
      reports: 3,
      storage: 1
    },
    color: 'gray',
    icon: Users
  },
  {
    id: 'basic',
    name: '基础版',
    price: 99,
    interval: 'month',
    description: '适合小型企业',
    features: [
      '无限AI文案生成',
      '高级邮件营销',
      '营销自动化',
      '详细数据分析',
      '邮件支持'
    ],
    limits: {
      aiGenerations: 1000,
      emailSends: 5000,
      campaigns: 10,
      automations: 5,
      reports: 20,
      storage: 10
    },
    color: 'blue',
    icon: Zap
  },
  {
    id: 'pro',
    name: '专业版',
    price: 299,
    interval: 'month',
    description: '适合成长型企业',
    features: [
      '无限AI内容生成',
      '高级营销自动化',
      '深度数据分析',
      '自定义报告',
      '优先支持',
      'API访问'
    ],
    limits: {
      aiGenerations: -1,
      emailSends: 50000,
      campaigns: 100,
      automations: 50,
      reports: -1,
      storage: 100
    },
    color: 'purple',
    icon: Crown,
    popular: true
  },
  {
    id: 'enterprise',
    name: '企业版',
    price: 999,
    interval: 'month',
    description: '适合大型企业',
    features: [
      '所有专业版功能',
      '白标解决方案',
      '专属客户经理',
      '定制开发',
      '私有部署',
      'SLA保障'
    ],
    limits: {
      aiGenerations: -1,
      emailSends: -1,
      campaigns: -1,
      automations: -1,
      reports: -1,
      storage: -1
    },
    color: 'gold',
    icon: Shield
  }
]

/**
 * 订阅管理页面组件
 */
export default function SubscriptionPage() {
  const { toast } = useToast()
  const {
    subscription,
    usage,
    isLoading,
    getSubscription,
    changeSubscription,
    cancelSubscription,
    resumeSubscription
  } = useSubscription()

  const [selectedPlan, setSelectedPlan] = useState<string>('')
  const [isChanging, setIsChanging] = useState(false)

  // 加载订阅信息
  useEffect(() => {
    getSubscription()
  }, [])

  /**
   * 处理计划变更
   */
  const handlePlanChange = async (planId: string) => {
    if (planId === subscription?.plan?.id) {
      return
    }

    setIsChanging(true)
    try {
      await changeSubscription(planId)
      toast({
        title: '订阅更新成功',
        description: '您的订阅计划已更新',
        variant: 'success'
      })
      await getSubscription()
    } catch (error: any) {
      toast({
        title: '订阅更新失败',
        description: error.message || '无法更新订阅计划',
        variant: 'destructive'
      })
    } finally {
      setIsChanging(false)
    }
  }

  /**
   * 处理取消订阅
   */
  const handleCancelSubscription = async () => {
    if (!confirm('确定要取消订阅吗？取消后将在当前计费周期结束时生效。')) {
      return
    }

    try {
      await cancelSubscription()
      toast({
        title: '订阅已取消',
        description: '您的订阅将在当前计费周期结束时取消',
        variant: 'success'
      })
      await getSubscription()
    } catch (error: any) {
      toast({
        title: '取消失败',
        description: error.message || '无法取消订阅',
        variant: 'destructive'
      })
    }
  }

  /**
   * 处理恢复订阅
   */
  const handleResumeSubscription = async () => {
    try {
      await resumeSubscription()
      toast({
        title: '订阅已恢复',
        description: '您的订阅已恢复正常',
        variant: 'success'
      })
      await getSubscription()
    } catch (error: any) {
      toast({
        title: '恢复失败',
        description: error.message || '无法恢复订阅',
        variant: 'destructive'
      })
    }
  }

  /**
   * 获取使用率百分比
   */
  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0 // 无限制
    return Math.min((used / limit) * 100, 100)
  }

  /**
   * 格式化限制显示
   */
  const formatLimit = (limit: number) => {
    if (limit === -1) return '无限制'
    if (limit >= 1000) return `${(limit / 1000).toFixed(0)}K`
    return limit.toString()
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const currentPlan = subscription?.plan || PLANS[0]

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <CreditCard className="w-8 h-8 mr-3 text-blue-600" />
            订阅管理
          </h1>
          <p className="text-gray-600 mt-2">
            管理您的订阅计划和账单信息
          </p>
        </div>

        <Tabs defaultValue="current" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="current">当前订阅</TabsTrigger>
            <TabsTrigger value="plans">订阅计划</TabsTrigger>
            <TabsTrigger value="usage">使用情况</TabsTrigger>
          </TabsList>

          {/* 当前订阅标签页 */}
          <TabsContent value="current" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>当前订阅</span>
                  <Badge 
                    variant={subscription?.subscription?.status === 'ACTIVE' ? 'default' : 'secondary'}
                  >
                    {subscription?.subscription?.status || 'FREE'}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  您当前的订阅计划和状态
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-lg bg-${currentPlan.color}-100`}>
                    <currentPlan.icon className={`w-6 h-6 text-${currentPlan.color}-600`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold">{currentPlan.name}</h3>
                    <p className="text-gray-600">{currentPlan.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold">
                      ¥{currentPlan.price}
                      <span className="text-sm font-normal text-gray-500">/月</span>
                    </div>
                  </div>
                </div>

                {subscription?.subscription && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                    <div>
                      <p className="text-sm text-gray-600">当前周期</p>
                      <p className="font-medium">
                        {new Date(subscription.subscription.currentPeriodStart).toLocaleDateString('zh-CN')} - 
                        {new Date(subscription.subscription.currentPeriodEnd).toLocaleDateString('zh-CN')}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">下次续费</p>
                      <p className="font-medium">
                        {subscription.subscription.cancelAtPeriodEnd ? 
                          '将在周期结束时取消' : 
                          new Date(subscription.subscription.currentPeriodEnd).toLocaleDateString('zh-CN')
                        }
                      </p>
                    </div>
                  </div>
                )}

                {subscription?.subscription?.cancelAtPeriodEnd && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      您的订阅将在当前计费周期结束时取消。
                      <Button 
                        variant="link" 
                        className="p-0 h-auto ml-2"
                        onClick={handleResumeSubscription}
                      >
                        恢复订阅
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex space-x-4">
                  {currentPlan.id !== 'free' && !subscription?.subscription?.cancelAtPeriodEnd && (
                    <Button 
                      variant="outline" 
                      onClick={handleCancelSubscription}
                    >
                      取消订阅
                    </Button>
                  )}
                  <Button onClick={() => setSelectedPlan('plans')}>
                    {currentPlan.id === 'free' ? '升级计划' : '更改计划'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 订阅计划标签页 */}
          <TabsContent value="plans" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {PLANS.map((plan) => (
                <Card 
                  key={plan.id} 
                  className={`relative ${plan.popular ? 'ring-2 ring-purple-500' : ''} ${
                    plan.id === currentPlan.id ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-purple-500">
                        <Star className="w-3 h-3 mr-1" />
                        推荐
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader className="text-center">
                    <div className={`mx-auto p-3 rounded-lg bg-${plan.color}-100 w-fit`}>
                      <plan.icon className={`w-6 h-6 text-${plan.color}-600`} />
                    </div>
                    <CardTitle>{plan.name}</CardTitle>
                    <CardDescription>{plan.description}</CardDescription>
                    <div className="text-3xl font-bold">
                      ¥{plan.price}
                      <span className="text-sm font-normal text-gray-500">/月</span>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    <ul className="space-y-2">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm">
                          <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    
                    <div className="pt-4 border-t">
                      <p className="text-xs text-gray-600 mb-2">使用限制:</p>
                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between">
                          <span>AI生成</span>
                          <span>{formatLimit(plan.limits.aiGenerations)}/月</span>
                        </div>
                        <div className="flex justify-between">
                          <span>邮件发送</span>
                          <span>{formatLimit(plan.limits.emailSends)}/月</span>
                        </div>
                        <div className="flex justify-between">
                          <span>营销活动</span>
                          <span>{formatLimit(plan.limits.campaigns)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <Button 
                      className="w-full"
                      variant={plan.id === currentPlan.id ? 'secondary' : 'default'}
                      disabled={plan.id === currentPlan.id || isChanging}
                      onClick={() => handlePlanChange(plan.id)}
                    >
                      {plan.id === currentPlan.id ? '当前计划' : 
                       plan.price > currentPlan.price ? '升级' : 
                       plan.price < currentPlan.price ? '降级' : '选择'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* 使用情况标签页 */}
          <TabsContent value="usage" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { key: 'aiGenerations', name: 'AI生成', icon: Zap },
                { key: 'emailSends', name: '邮件发送', icon: Calendar },
                { key: 'campaigns', name: '营销活动', icon: DollarSign },
                { key: 'automations', name: '自动化', icon: RefreshCw },
                { key: 'reports', name: '报告', icon: AlertCircle },
                { key: 'storage', name: '存储空间', icon: Shield }
              ].map((item) => {
                const used = usage?.[item.key] || 0
                const limit = currentPlan.limits[item.key as keyof typeof currentPlan.limits]
                const percentage = getUsagePercentage(used, limit)
                
                return (
                  <Card key={item.key}>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center text-sm">
                        <item.icon className="w-4 h-4 mr-2" />
                        {item.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>已使用</span>
                          <span>{used} / {formatLimit(limit)}</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                        <p className="text-xs text-gray-600">
                          {limit === -1 ? '无限制' : `剩余 ${Math.max(0, limit - used)}`}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: '订阅管理 - AI数字营销平台',
      description: '管理您的订阅计划和账单信息'
    }
  }
}
