// 用户注册页面
// 提供用户注册功能，包括表单验证、密码强度检查和邮箱验证

import React, { useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff, Mail, Lock, User, Phone, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { Progress } from '@/components/ui/progress'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/useToast'

// 注册表单验证模式
const registerSchema = z.object({
  firstName: z.string()
    .min(1, '请输入名字')
    .max(50, '名字长度不能超过50个字符')
    .regex(/^[a-zA-Z\u4e00-\u9fa5\s]+$/, '名字只能包含字母、中文和空格'),
  lastName: z.string()
    .min(1, '请输入姓氏')
    .max(50, '姓氏长度不能超过50个字符')
    .regex(/^[a-zA-Z\u4e00-\u9fa5\s]+$/, '姓氏只能包含字母、中文和空格'),
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string()
    .min(8, '密码长度至少8个字符')
    .max(128, '密码长度不能超过128个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  confirmPassword: z.string().min(1, '请确认密码'),
  phone: z.string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional()
    .or(z.literal('')),
  agreeToTerms: z.boolean().refine(val => val === true, '请同意服务条款和隐私政策')
}).refine(data => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

type RegisterFormData = z.infer<typeof registerSchema>

/**
 * 密码强度检查
 */
const checkPasswordStrength = (password: string) => {
  let score = 0
  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }

  Object.values(checks).forEach(check => {
    if (check) score += 20
  })

  return {
    score: Math.min(score, 100),
    checks,
    level: score < 40 ? 'weak' : score < 80 ? 'medium' : 'strong'
  }
}

/**
 * 注册页面组件
 */
export default function RegisterPage() {
  const router = useRouter()
  const { register: registerUser, isLoading } = useAuth()
  const { toast } = useToast()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [registrationSuccess, setRegistrationSuccess] = useState(false)

  // 表单处理
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      agreeToTerms: false
    }
  })

  const password = watch('password')
  const agreeToTerms = watch('agreeToTerms')
  const passwordStrength = password ? checkPasswordStrength(password) : null

  /**
   * 处理注册提交
   */
  const onSubmit = async (data: RegisterFormData) => {
    try {
      setError(null)
      
      await registerUser({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        password: data.password,
        phone: data.phone || undefined
      })

      setRegistrationSuccess(true)
      
      toast({
        title: '注册成功',
        description: '请检查您的邮箱并验证邮箱地址',
        variant: 'success'
      })

    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || '注册失败，请重试'
      setError(errorMessage)
      
      toast({
        title: '注册失败',
        description: errorMessage,
        variant: 'destructive'
      })
    }
  }

  // 如果注册成功，显示成功页面
  if (registrationSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          <Card className="shadow-lg">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-green-800">
                注册成功！
              </CardTitle>
              <CardDescription>
                我们已向您的邮箱发送了验证邮件
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600">
                请检查您的邮箱并点击验证链接以激活您的账户。
              </p>
              <p className="text-sm text-gray-500">
                如果您没有收到邮件，请检查垃圾邮件文件夹。
              </p>
            </CardContent>
            <CardFooter className="flex flex-col space-y-3">
              <Button
                onClick={() => router.push('/auth/login')}
                className="w-full"
              >
                前往登录
              </Button>
              <Button
                variant="outline"
                onClick={() => setRegistrationSuccess(false)}
                className="w-full"
              >
                重新注册
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI数字营销平台
          </h1>
          <p className="text-gray-600">
            创建您的账户，开始智能营销之旅
          </p>
        </div>

        {/* 注册表单 */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              用户注册
            </CardTitle>
            <CardDescription className="text-center">
              填写以下信息创建您的账户
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              {/* 错误提示 */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* 姓名输入 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">名字</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="firstName"
                      placeholder="名字"
                      className="pl-10"
                      {...register('firstName')}
                      disabled={isSubmitting || isLoading}
                    />
                  </div>
                  {errors.firstName && (
                    <p className="text-sm text-red-600">{errors.firstName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">姓氏</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="lastName"
                      placeholder="姓氏"
                      className="pl-10"
                      {...register('lastName')}
                      disabled={isSubmitting || isLoading}
                    />
                  </div>
                  {errors.lastName && (
                    <p className="text-sm text-red-600">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              {/* 邮箱输入 */}
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="请输入您的邮箱"
                    className="pl-10"
                    {...register('email')}
                    disabled={isSubmitting || isLoading}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              {/* 手机号输入（可选） */}
              <div className="space-y-2">
                <Label htmlFor="phone">手机号（可选）</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="请输入您的手机号"
                    className="pl-10"
                    {...register('phone')}
                    disabled={isSubmitting || isLoading}
                  />
                </div>
                {errors.phone && (
                  <p className="text-sm text-red-600">{errors.phone.message}</p>
                )}
              </div>

              {/* 密码输入 */}
              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="请输入密码"
                    className="pl-10 pr-10"
                    {...register('password')}
                    disabled={isSubmitting || isLoading}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isSubmitting || isLoading}
                  >
                    {showPassword ? <EyeOff /> : <Eye />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password.message}</p>
                )}
                
                {/* 密码强度指示器 */}
                {password && passwordStrength && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">密码强度</span>
                      <span className={`text-sm font-medium ${
                        passwordStrength.level === 'weak' ? 'text-red-600' :
                        passwordStrength.level === 'medium' ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {passwordStrength.level === 'weak' ? '弱' :
                         passwordStrength.level === 'medium' ? '中' : '强'}
                      </span>
                    </div>
                    <Progress 
                      value={passwordStrength.score} 
                      className={`h-2 ${
                        passwordStrength.level === 'weak' ? 'bg-red-100' :
                        passwordStrength.level === 'medium' ? 'bg-yellow-100' :
                        'bg-green-100'
                      }`}
                    />
                  </div>
                )}
              </div>

              {/* 确认密码输入 */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">确认密码</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="请再次输入密码"
                    className="pl-10 pr-10"
                    {...register('confirmPassword')}
                    disabled={isSubmitting || isLoading}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={isSubmitting || isLoading}
                  >
                    {showConfirmPassword ? <EyeOff /> : <Eye />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
                )}
              </div>

              {/* 同意条款 */}
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="agreeToTerms"
                  checked={agreeToTerms}
                  onCheckedChange={(checked) => setValue('agreeToTerms', !!checked)}
                  disabled={isSubmitting || isLoading}
                  className="mt-1"
                />
                <Label
                  htmlFor="agreeToTerms"
                  className="text-sm leading-relaxed peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  我已阅读并同意
                  <Link href="/terms" className="text-blue-600 hover:underline">
                    服务条款
                  </Link>
                  和
                  <Link href="/privacy" className="text-blue-600 hover:underline">
                    隐私政策
                  </Link>
                </Label>
              </div>
              {errors.agreeToTerms && (
                <p className="text-sm text-red-600">{errors.agreeToTerms.message}</p>
              )}
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              {/* 注册按钮 */}
              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting || isLoading ? '注册中...' : '创建账户'}
              </Button>

              {/* 登录链接 */}
              <div className="text-center text-sm">
                <span className="text-gray-600">已有账户？</span>
                <Link
                  href="/auth/login"
                  className="ml-1 text-blue-600 hover:text-blue-500 hover:underline font-medium"
                >
                  立即登录
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}

/**
 * 页面配置
 */
RegisterPage.getLayout = function getLayout(page: React.ReactElement) {
  return page // 不使用默认布局
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: '用户注册 - AI数字营销平台',
      description: '注册AI数字营销平台账户，开始您的智能营销之旅'
    }
  }
}
