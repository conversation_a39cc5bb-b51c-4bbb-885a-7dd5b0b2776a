// 用户登录页面
// 提供用户登录功能，包括表单验证、错误处理和状态管理

import React, { useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff, Mail, Lock, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/useToast'

// 登录表单验证模式
const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(1, '请输入密码'),
  rememberMe: z.boolean().optional()
})

type LoginFormData = z.infer<typeof loginSchema>

/**
 * 登录页面组件
 */
export default function LoginPage() {
  const router = useRouter()
  const { login, isLoading } = useAuth()
  const { toast } = useToast()
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 表单处理
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      rememberMe: false
    }
  })

  const rememberMe = watch('rememberMe')

  /**
   * 处理登录提交
   */
  const onSubmit = async (data: LoginFormData) => {
    try {
      setError(null)
      
      await login({
        email: data.email,
        password: data.password,
        rememberMe: data.rememberMe
      })

      toast({
        title: '登录成功',
        description: '欢迎回到AI数字营销平台！',
        variant: 'success'
      })

      // 重定向到仪表板或返回页面
      const returnUrl = router.query.returnUrl as string
      router.push(returnUrl || '/dashboard')
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || '登录失败，请重试'
      setError(errorMessage)
      
      toast({
        title: '登录失败',
        description: errorMessage,
        variant: 'destructive'
      })
    }
  }

  /**
   * 切换密码显示状态
   */
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI数字营销平台
          </h1>
          <p className="text-gray-600">
            登录您的账户以继续使用
          </p>
        </div>

        {/* 登录表单 */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              用户登录
            </CardTitle>
            <CardDescription className="text-center">
              输入您的邮箱和密码登录账户
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              {/* 错误提示 */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* 邮箱输入 */}
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="请输入您的邮箱"
                    className="pl-10"
                    {...register('email')}
                    disabled={isSubmitting || isLoading}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              {/* 密码输入 */}
              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="请输入您的密码"
                    className="pl-10 pr-10"
                    {...register('password')}
                    disabled={isSubmitting || isLoading}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                    onClick={togglePasswordVisibility}
                    disabled={isSubmitting || isLoading}
                  >
                    {showPassword ? <EyeOff /> : <Eye />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password.message}</p>
                )}
              </div>

              {/* 记住我和忘记密码 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="rememberMe"
                    checked={rememberMe}
                    onCheckedChange={(checked) => setValue('rememberMe', !!checked)}
                    disabled={isSubmitting || isLoading}
                  />
                  <Label
                    htmlFor="rememberMe"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    记住我
                  </Label>
                </div>
                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-blue-600 hover:text-blue-500 hover:underline"
                >
                  忘记密码？
                </Link>
              </div>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              {/* 登录按钮 */}
              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting || isLoading ? '登录中...' : '登录'}
              </Button>

              {/* 注册链接 */}
              <div className="text-center text-sm">
                <span className="text-gray-600">还没有账户？</span>
                <Link
                  href="/auth/register"
                  className="ml-1 text-blue-600 hover:text-blue-500 hover:underline font-medium"
                >
                  立即注册
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>

        {/* 其他登录方式 */}
        <div className="text-center">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gradient-to-br from-blue-50 to-indigo-100 text-gray-500">
                或者
              </span>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 gap-3">
            {/* 演示账户登录 */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                setValue('email', '<EMAIL>')
                setValue('password', 'demo123')
              }}
              disabled={isSubmitting || isLoading}
            >
              使用演示账户登录
            </Button>
          </div>
        </div>

        {/* 页脚信息 */}
        <div className="text-center text-xs text-gray-500">
          <p>
            登录即表示您同意我们的
            <Link href="/terms" className="text-blue-600 hover:underline">
              服务条款
            </Link>
            和
            <Link href="/privacy" className="text-blue-600 hover:underline">
              隐私政策
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

/**
 * 页面配置
 */
LoginPage.getLayout = function getLayout(page: React.ReactElement) {
  return page // 不使用默认布局
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: '用户登录 - AI数字营销平台',
      description: '登录AI数字营销平台，开始您的智能营销之旅'
    }
  }
}
