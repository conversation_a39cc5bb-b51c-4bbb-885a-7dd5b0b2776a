// 营销活动管理页面
// 提供营销活动的创建、编辑、管理和监控功能

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { 
  Megaphone, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Play,
  Pause,
  Edit,
  Trash2,
  Eye,
  BarChart3,
  Calendar,
  DollarSign,
  Users,
  Target
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/useToast'
import { useCampaigns } from '@/hooks/useCampaigns'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

// 活动状态选项
const statusOptions = [
  { value: 'ALL', label: '全部状态' },
  { value: 'DRAFT', label: '草稿' },
  { value: 'SCHEDULED', label: '已安排' },
  { value: 'ACTIVE', label: '进行中' },
  { value: 'PAUSED', label: '已暂停' },
  { value: 'COMPLETED', label: '已完成' },
  { value: 'CANCELLED', label: '已取消' }
]

// 活动类型选项
const typeOptions = [
  { value: 'ALL', label: '全部类型' },
  { value: 'EMAIL', label: '邮件营销' },
  { value: 'SMS', label: '短信营销' },
  { value: 'SOCIAL_MEDIA', label: '社交媒体' },
  { value: 'DISPLAY_AD', label: '展示广告' },
  { value: 'SEARCH_AD', label: '搜索广告' },
  { value: 'CONTENT_MARKETING', label: '内容营销' }
]

/**
 * 营销活动管理页面组件
 */
export default function CampaignsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const {
    campaigns,
    stats,
    isLoading,
    pagination,
    getCampaigns,
    updateCampaignStatus,
    deleteCampaign
  } = useCampaigns()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('ALL')
  const [selectedType, setSelectedType] = useState('ALL')
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)

  // 加载活动列表
  useEffect(() => {
    loadCampaigns()
  }, [currentPage, selectedStatus, selectedType])

  /**
   * 加载活动列表
   */
  const loadCampaigns = async () => {
    try {
      await getCampaigns({
        page: currentPage,
        limit: 20,
        status: selectedStatus !== 'ALL' ? [selectedStatus as any] : undefined,
        type: selectedType !== 'ALL' ? [selectedType as any] : undefined,
        search: searchQuery
      })
    } catch (error: any) {
      toast({
        title: '加载失败',
        description: error.message || '无法加载营销活动',
        variant: 'destructive'
      })
    }
  }

  /**
   * 搜索处理
   */
  const handleSearch = () => {
    setCurrentPage(1)
    loadCampaigns()
  }

  /**
   * 更新活动状态
   */
  const handleStatusUpdate = async (campaignId: string, newStatus: string) => {
    try {
      await updateCampaignStatus(campaignId, newStatus as any)
      toast({
        title: '状态更新成功',
        description: '活动状态已更新',
        variant: 'success'
      })
      loadCampaigns()
    } catch (error: any) {
      toast({
        title: '状态更新失败',
        description: error.message || '无法更新活动状态',
        variant: 'destructive'
      })
    }
  }

  /**
   * 删除活动
   */
  const handleDelete = async (campaignId: string) => {
    try {
      await deleteCampaign(campaignId)
      toast({
        title: '删除成功',
        description: '营销活动已删除',
        variant: 'success'
      })
      loadCampaigns()
    } catch (error: any) {
      toast({
        title: '删除失败',
        description: error.message || '无法删除营销活动',
        variant: 'destructive'
      })
    }
  }

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'DRAFT': 'bg-gray-100 text-gray-800',
      'SCHEDULED': 'bg-blue-100 text-blue-800',
      'ACTIVE': 'bg-green-100 text-green-800',
      'PAUSED': 'bg-yellow-100 text-yellow-800',
      'COMPLETED': 'bg-purple-100 text-purple-800',
      'CANCELLED': 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  /**
   * 获取状态标签
   */
  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'DRAFT': '草稿',
      'SCHEDULED': '已安排',
      'ACTIVE': '进行中',
      'PAUSED': '已暂停',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    }
    return labels[status] || status
  }

  /**
   * 获取类型标签
   */
  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'EMAIL': '邮件营销',
      'SMS': '短信营销',
      'SOCIAL_MEDIA': '社交媒体',
      'DISPLAY_AD': '展示广告',
      'SEARCH_AD': '搜索广告',
      'CONTENT_MARKETING': '内容营销'
    }
    return labels[type] || type
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('zh-CN')
  }

  /**
   * 格式化预算
   */
  const formatBudget = (budget: any) => {
    if (!budget || !budget.total) return '-'
    return `¥${budget.total.toLocaleString()}`
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Megaphone className="w-8 h-8 mr-3 text-blue-600" />
              营销活动管理
            </h1>
            <p className="text-gray-600 mt-2">
              创建、管理和监控您的营销活动
            </p>
          </div>
          <Button onClick={() => router.push('/marketing/campaigns/create')}>
            <Plus className="w-4 h-4 mr-2" />
            创建活动
          </Button>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Target className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总活动数</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.totalCampaigns || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Play className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">活跃活动</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.activeCampaigns || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">已完成</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.completedCampaigns || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总预算</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ¥{(stats?.totalBudget || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 筛选和搜索 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              筛选和搜索
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="search">搜索活动</Label>
                <div className="flex space-x-2">
                  <Input
                    id="search"
                    placeholder="搜索活动名称或描述..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <Button onClick={handleSearch}>
                    <Search className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label>状态</Label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>类型</Label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {typeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 活动列表 */}
        <Card>
          <CardHeader>
            <CardTitle>营销活动</CardTitle>
            <CardDescription>
              管理您的所有营销活动
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : campaigns.length === 0 ? (
              <div className="text-center py-8">
                <Megaphone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  暂无营销活动
                </h3>
                <p className="text-gray-500 mb-4">
                  创建您的第一个营销活动来开始推广
                </p>
                <Button onClick={() => router.push('/marketing/campaigns/create')}>
                  <Plus className="w-4 h-4 mr-2" />
                  创建活动
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {campaigns.map((campaign) => (
                  <div
                    key={campaign.id}
                    className="border rounded-lg p-6 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {campaign.name}
                          </h3>
                          <Badge className={getStatusColor(campaign.status)}>
                            {getStatusLabel(campaign.status)}
                          </Badge>
                          <Badge variant="outline">
                            {getTypeLabel(campaign.type)}
                          </Badge>
                        </div>

                        {campaign.description && (
                          <p className="text-gray-600 mb-3">
                            {campaign.description}
                          </p>
                        )}

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">开始日期：</span>
                            <span className="font-medium">
                              {formatDate(campaign.schedule.startDate)}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">预算：</span>
                            <span className="font-medium">
                              {formatBudget(campaign.budget)}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">目标受众：</span>
                            <span className="font-medium">
                              {campaign.targetAudience?.segments?.length || 0} 个群体
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">创建时间：</span>
                            <span className="font-medium">
                              {formatDate(campaign.createdAt)}
                            </span>
                          </div>
                        </div>

                        {/* 进度条（如果活动正在进行） */}
                        {campaign.status === 'ACTIVE' && campaign.schedule.endDate && (
                          <div className="mt-4">
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>活动进度</span>
                              <span>
                                {Math.round(
                                  ((new Date().getTime() - new Date(campaign.schedule.startDate).getTime()) /
                                  (new Date(campaign.schedule.endDate).getTime() - new Date(campaign.schedule.startDate).getTime())) * 100
                                )}%
                              </span>
                            </div>
                            <Progress 
                              value={Math.round(
                                ((new Date().getTime() - new Date(campaign.schedule.startDate).getTime()) /
                                (new Date(campaign.schedule.endDate).getTime() - new Date(campaign.schedule.startDate).getTime())) * 100
                              )}
                              className="h-2"
                            />
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedCampaign(campaign)
                            setDetailsDialogOpen(true)
                          }}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/marketing/campaigns/${campaign.id}/analytics`)}
                        >
                          <BarChart3 className="w-4 h-4" />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem
                              onClick={() => router.push(`/marketing/campaigns/${campaign.id}/edit`)}
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              编辑
                            </DropdownMenuItem>
                            
                            {campaign.status === 'DRAFT' && (
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(campaign.id, 'ACTIVE')}
                              >
                                <Play className="w-4 h-4 mr-2" />
                                启动
                              </DropdownMenuItem>
                            )}
                            
                            {campaign.status === 'ACTIVE' && (
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(campaign.id, 'PAUSED')}
                              >
                                <Pause className="w-4 h-4 mr-2" />
                                暂停
                              </DropdownMenuItem>
                            )}
                            
                            {campaign.status === 'PAUSED' && (
                              <DropdownMenuItem
                                onClick={() => handleStatusUpdate(campaign.id, 'ACTIVE')}
                              >
                                <Play className="w-4 h-4 mr-2" />
                                恢复
                              </DropdownMenuItem>
                            )}
                            
                            <DropdownMenuItem
                              onClick={() => handleDelete(campaign.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 活动详情对话框 */}
        <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>活动详情</DialogTitle>
            </DialogHeader>
            {selectedCampaign && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium mb-2">基本信息</h3>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-gray-500">活动名称：</span>
                        <span className="font-medium">{selectedCampaign.name}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">活动类型：</span>
                        <span className="font-medium">{getTypeLabel(selectedCampaign.type)}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">状态：</span>
                        <Badge className={getStatusColor(selectedCampaign.status)}>
                          {getStatusLabel(selectedCampaign.status)}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">预算信息</h3>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-gray-500">总预算：</span>
                        <span className="font-medium">{formatBudget(selectedCampaign.budget)}</span>
                      </div>
                      {selectedCampaign.budget?.daily && (
                        <div>
                          <span className="text-gray-500">日预算：</span>
                          <span className="font-medium">¥{selectedCampaign.budget.daily.toLocaleString()}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">活动内容</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    {selectedCampaign.content?.subject && (
                      <div className="mb-2">
                        <span className="text-gray-500">主题：</span>
                        <span className="font-medium">{selectedCampaign.content.subject}</span>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-500">内容：</span>
                      <p className="mt-1">{selectedCampaign.content?.message}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">目标受众</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    {selectedCampaign.targetAudience?.demographics && (
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        {selectedCampaign.targetAudience.demographics.ageRange && (
                          <div>
                            <span className="text-gray-500">年龄范围：</span>
                            <span className="font-medium">{selectedCampaign.targetAudience.demographics.ageRange}</span>
                          </div>
                        )}
                        {selectedCampaign.targetAudience.demographics.gender && (
                          <div>
                            <span className="text-gray-500">性别：</span>
                            <span className="font-medium">{selectedCampaign.targetAudience.demographics.gender}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  )
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: '营销活动管理 - AI数字营销平台',
      description: '创建、管理和监控您的营销活动'
    }
  }
}
