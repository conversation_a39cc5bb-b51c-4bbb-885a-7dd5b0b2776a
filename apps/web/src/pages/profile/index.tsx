// 用户资料页面
// 显示和编辑用户个人信息、偏好设置和统计数据

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { User, Mail, Phone, Globe, Building, Camera, Save, Edit3 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/useToast'
import { useUserProfile } from '@/hooks/useUserProfile'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

// 用户资料表单验证模式
const profileSchema = z.object({
  firstName: z.string()
    .min(1, '请输入名字')
    .max(50, '名字长度不能超过50个字符'),
  lastName: z.string()
    .min(1, '请输入姓氏')
    .max(50, '姓氏长度不能超过50个字符'),
  phone: z.string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional()
    .or(z.literal('')),
  bio: z.string()
    .max(500, '个人简介不能超过500个字符')
    .optional()
    .or(z.literal('')),
  company: z.string()
    .max(100, '公司名称不能超过100个字符')
    .optional()
    .or(z.literal('')),
  website: z.string()
    .url('请输入有效的网址')
    .optional()
    .or(z.literal(''))
})

type ProfileFormData = z.infer<typeof profileSchema>

/**
 * 用户资料页面组件
 */
export default function ProfilePage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const { 
    profile, 
    stats, 
    preferences, 
    isLoading, 
    updateProfile, 
    updatePreferences 
  } = useUserProfile()
  
  const [isEditing, setIsEditing] = useState(false)
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)

  // 表单处理
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema)
  })

  // 初始化表单数据
  useEffect(() => {
    if (profile) {
      reset({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        phone: profile.phone || '',
        bio: profile.bio || '',
        company: profile.company || '',
        website: profile.website || ''
      })
    }
  }, [profile, reset])

  /**
   * 处理头像文件选择
   */
  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB限制
        toast({
          title: '文件过大',
          description: '头像文件大小不能超过5MB',
          variant: 'destructive'
        })
        return
      }

      setAvatarFile(file)
      
      // 创建预览
      const reader = new FileReader()
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  /**
   * 处理资料更新提交
   */
  const onSubmit = async (data: ProfileFormData) => {
    try {
      await updateProfile(data)
      
      setIsEditing(false)
      setAvatarFile(null)
      setAvatarPreview(null)
      
      toast({
        title: '资料更新成功',
        description: '您的个人资料已成功更新',
        variant: 'success'
      })
    } catch (error: any) {
      toast({
        title: '更新失败',
        description: error.message || '资料更新失败，请重试',
        variant: 'destructive'
      })
    }
  }

  /**
   * 取消编辑
   */
  const handleCancelEdit = () => {
    setIsEditing(false)
    setAvatarFile(null)
    setAvatarPreview(null)
    reset()
  }

  /**
   * 获取用户全名
   */
  const getFullName = () => {
    if (!profile) return ''
    return `${profile.firstName} ${profile.lastName}`.trim()
  }

  /**
   * 获取用户头像初始字母
   */
  const getAvatarFallback = () => {
    if (!profile) return 'U'
    const firstName = profile.firstName?.[0] || ''
    const lastName = profile.lastName?.[0] || ''
    return (firstName + lastName).toUpperCase() || 'U'
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">个人资料</h1>
          <p className="text-gray-600 mt-2">管理您的个人信息和偏好设置</p>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile">基本信息</TabsTrigger>
            <TabsTrigger value="preferences">偏好设置</TabsTrigger>
            <TabsTrigger value="stats">统计数据</TabsTrigger>
          </TabsList>

          {/* 基本信息标签页 */}
          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>基本信息</CardTitle>
                    <CardDescription>
                      管理您的个人基本信息
                    </CardDescription>
                  </div>
                  {!isEditing && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditing(true)}
                    >
                      <Edit3 className="w-4 h-4 mr-2" />
                      编辑
                    </Button>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* 头像部分 */}
                  <div className="flex items-center space-x-6">
                    <div className="relative">
                      <Avatar className="w-24 h-24">
                        <AvatarImage 
                          src={avatarPreview || profile?.avatar} 
                          alt={getFullName()} 
                        />
                        <AvatarFallback className="text-lg">
                          {getAvatarFallback()}
                        </AvatarFallback>
                      </Avatar>
                      {isEditing && (
                        <label className="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 cursor-pointer hover:bg-blue-700 transition-colors">
                          <Camera className="w-4 h-4" />
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleAvatarChange}
                            className="hidden"
                          />
                        </label>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{getFullName()}</h3>
                      <p className="text-gray-600">{profile?.email}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant={profile?.emailVerified ? 'default' : 'secondary'}>
                          {profile?.emailVerified ? '邮箱已验证' : '邮箱未验证'}
                        </Badge>
                        <Badge variant="outline">
                          {profile?.role === 'ADMIN' ? '管理员' : 
                           profile?.role === 'SUPER_ADMIN' ? '超级管理员' : '用户'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 基本信息表单 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* 名字 */}
                    <div className="space-y-2">
                      <Label htmlFor="firstName">名字</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="firstName"
                          placeholder="请输入名字"
                          className="pl-10"
                          {...register('firstName')}
                          disabled={!isEditing || isSubmitting}
                        />
                      </div>
                      {errors.firstName && (
                        <p className="text-sm text-red-600">{errors.firstName.message}</p>
                      )}
                    </div>

                    {/* 姓氏 */}
                    <div className="space-y-2">
                      <Label htmlFor="lastName">姓氏</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="lastName"
                          placeholder="请输入姓氏"
                          className="pl-10"
                          {...register('lastName')}
                          disabled={!isEditing || isSubmitting}
                        />
                      </div>
                      {errors.lastName && (
                        <p className="text-sm text-red-600">{errors.lastName.message}</p>
                      )}
                    </div>

                    {/* 邮箱（只读） */}
                    <div className="space-y-2">
                      <Label htmlFor="email">邮箱地址</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="email"
                          value={profile?.email || ''}
                          className="pl-10 bg-gray-50"
                          disabled
                        />
                      </div>
                      <p className="text-sm text-gray-500">邮箱地址不可修改</p>
                    </div>

                    {/* 手机号 */}
                    <div className="space-y-2">
                      <Label htmlFor="phone">手机号</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="phone"
                          placeholder="请输入手机号"
                          className="pl-10"
                          {...register('phone')}
                          disabled={!isEditing || isSubmitting}
                        />
                      </div>
                      {errors.phone && (
                        <p className="text-sm text-red-600">{errors.phone.message}</p>
                      )}
                    </div>

                    {/* 公司 */}
                    <div className="space-y-2">
                      <Label htmlFor="company">公司</Label>
                      <div className="relative">
                        <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="company"
                          placeholder="请输入公司名称"
                          className="pl-10"
                          {...register('company')}
                          disabled={!isEditing || isSubmitting}
                        />
                      </div>
                      {errors.company && (
                        <p className="text-sm text-red-600">{errors.company.message}</p>
                      )}
                    </div>

                    {/* 网站 */}
                    <div className="space-y-2">
                      <Label htmlFor="website">个人网站</Label>
                      <div className="relative">
                        <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="website"
                          placeholder="https://example.com"
                          className="pl-10"
                          {...register('website')}
                          disabled={!isEditing || isSubmitting}
                        />
                      </div>
                      {errors.website && (
                        <p className="text-sm text-red-600">{errors.website.message}</p>
                      )}
                    </div>
                  </div>

                  {/* 个人简介 */}
                  <div className="space-y-2">
                    <Label htmlFor="bio">个人简介</Label>
                    <Textarea
                      id="bio"
                      placeholder="介绍一下您自己..."
                      rows={4}
                      {...register('bio')}
                      disabled={!isEditing || isSubmitting}
                    />
                    {errors.bio && (
                      <p className="text-sm text-red-600">{errors.bio.message}</p>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  {isEditing && (
                    <div className="flex items-center space-x-4 pt-4">
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {isSubmitting ? '保存中...' : '保存更改'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleCancelEdit}
                        disabled={isSubmitting}
                      >
                        取消
                      </Button>
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>

            {/* 账户信息 */}
            <Card>
              <CardHeader>
                <CardTitle>账户信息</CardTitle>
                <CardDescription>
                  您的账户状态和重要信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">注册时间</Label>
                    <p className="text-sm">
                      {profile?.createdAt ? new Date(profile.createdAt).toLocaleDateString('zh-CN') : '-'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">最后登录</Label>
                    <p className="text-sm">
                      {profile?.lastLoginAt ? new Date(profile.lastLoginAt).toLocaleDateString('zh-CN') : '-'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">账户状态</Label>
                    <Badge variant={profile?.status === 'ACTIVE' ? 'default' : 'secondary'}>
                      {profile?.status === 'ACTIVE' ? '正常' : '异常'}
                    </Badge>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">用户角色</Label>
                    <Badge variant="outline">
                      {profile?.role === 'ADMIN' ? '管理员' : 
                       profile?.role === 'SUPER_ADMIN' ? '超级管理员' : '普通用户'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 偏好设置标签页 */}
          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle>偏好设置</CardTitle>
                <CardDescription>
                  自定义您的使用体验
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertDescription>
                    偏好设置功能正在开发中，敬请期待...
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 统计数据标签页 */}
          <TabsContent value="stats">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">
                    总营销活动
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.totalCampaigns || 0}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">
                    活跃活动
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.activeCampaigns || 0}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">
                    AI生成次数
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.totalAIGenerations || 0}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">
                    总消费
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">¥{stats?.totalSpent || 0}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">
                    加入天数
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.joinedDays || 0}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">
                    最后登录
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.lastLoginDays || 0}天前</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}

/**
 * 页面元数据
 */
export async function getStaticProps() {
  return {
    props: {
      title: '个人资料 - AI数字营销平台',
      description: '管理您的个人信息和偏好设置'
    }
  }
}
