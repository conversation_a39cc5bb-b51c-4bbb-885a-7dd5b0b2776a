// AI数字营销平台 Service Worker
// 提供离线功能、缓存管理和推送通知支持

const CACHE_NAME = 'ai-marketing-v1.0.0';
const STATIC_CACHE = 'ai-marketing-static-v1.0.0';
const DYNAMIC_CACHE = 'ai-marketing-dynamic-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/dashboard',
  '/dashboard/ai',
  '/dashboard/campaigns',
  '/dashboard/analytics',
  '/dashboard/billing',
  '/offline',
  '/manifest.json',
  // CSS和JS文件会在运行时动态添加
];

// 需要缓存的API端点
const CACHE_API_ROUTES = [
  '/api/user/profile',
  '/api/dashboard/stats',
  '/api/campaigns',
  '/api/analytics/overview'
];

// 安装事件 - 缓存静态资源
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker 安装中...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('📦 缓存静态资源');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('✅ Service Worker 安装完成');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ Service Worker 安装失败:', error);
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker 激活中...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('🗑️ 删除旧缓存:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker 激活完成');
        return self.clients.claim();
      })
  );
});

// 拦截网络请求
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 跳过非GET请求和Chrome扩展请求
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }
  
  // 处理导航请求
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigationRequest(request));
    return;
  }
  
  // 处理API请求
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }
  
  // 处理静态资源请求
  event.respondWith(handleStaticRequest(request));
});

// 处理导航请求（页面请求）
async function handleNavigationRequest(request) {
  try {
    // 尝试从网络获取
    const networkResponse = await fetch(request);
    
    // 缓存成功的响应
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('🌐 网络请求失败，尝试从缓存获取:', request.url);
    
    // 从缓存获取
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 返回离线页面
    const offlinePage = await caches.match('/offline');
    if (offlinePage) {
      return offlinePage;
    }
    
    // 最后的备选方案
    return new Response('页面暂时无法访问，请检查网络连接', {
      status: 503,
      statusText: 'Service Unavailable',
      headers: { 'Content-Type': 'text/plain; charset=utf-8' }
    });
  }
}

// 处理API请求
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  try {
    // 尝试从网络获取
    const networkResponse = await fetch(request);
    
    // 缓存GET请求的成功响应
    if (networkResponse.ok && CACHE_API_ROUTES.some(route => url.pathname.startsWith(route))) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('🌐 API请求失败，尝试从缓存获取:', request.url);
    
    // 从缓存获取
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      // 添加离线标识头
      const response = cachedResponse.clone();
      response.headers.set('X-Served-From', 'cache');
      return response;
    }
    
    // 返回离线响应
    return new Response(JSON.stringify({
      error: '网络连接失败',
      message: '请检查网络连接后重试',
      offline: true
    }), {
      status: 503,
      statusText: 'Service Unavailable',
      headers: { 'Content-Type': 'application/json; charset=utf-8' }
    });
  }
}

// 处理静态资源请求
async function handleStaticRequest(request) {
  try {
    // 先尝试从缓存获取
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 从网络获取
    const networkResponse = await fetch(request);
    
    // 缓存成功的响应
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('🌐 静态资源请求失败:', request.url);
    
    // 返回通用错误响应
    return new Response('资源暂时无法访问', {
      status: 503,
      statusText: 'Service Unavailable',
      headers: { 'Content-Type': 'text/plain; charset=utf-8' }
    });
  }
}

// 推送通知事件
self.addEventListener('push', (event) => {
  console.log('📱 收到推送通知');
  
  if (!event.data) {
    return;
  }
  
  const data = event.data.json();
  const options = {
    body: data.body || '您有新的消息',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    image: data.image,
    data: data.data,
    actions: [
      {
        action: 'view',
        title: '查看详情',
        icon: '/icons/action-view.png'
      },
      {
        action: 'dismiss',
        title: '忽略',
        icon: '/icons/action-dismiss.png'
      }
    ],
    requireInteraction: true,
    silent: false,
    vibrate: [200, 100, 200]
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title || 'AI数字营销平台', options)
  );
});

// 通知点击事件
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 通知被点击:', event.action);
  
  event.notification.close();
  
  if (event.action === 'view') {
    // 打开相关页面
    const urlToOpen = event.notification.data?.url || '/dashboard';
    
    event.waitUntil(
      clients.matchAll({ type: 'window', includeUncontrolled: true })
        .then((clientList) => {
          // 查找已打开的窗口
          for (const client of clientList) {
            if (client.url.includes(urlToOpen) && 'focus' in client) {
              return client.focus();
            }
          }
          
          // 打开新窗口
          if (clients.openWindow) {
            return clients.openWindow(urlToOpen);
          }
        })
    );
  }
});

// 后台同步事件
self.addEventListener('sync', (event) => {
  console.log('🔄 后台同步:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

// 执行后台同步
async function doBackgroundSync() {
  try {
    // 同步离线时的操作
    console.log('🔄 执行后台数据同步');
    
    // 这里可以添加具体的同步逻辑
    // 例如：上传离线时创建的内容、同步用户数据等
    
  } catch (error) {
    console.error('❌ 后台同步失败:', error);
  }
}

// 消息事件 - 与主线程通信
self.addEventListener('message', (event) => {
  console.log('💬 收到消息:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});
