'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Check, Globe, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  supportedLanguages, 
  changeLanguage, 
  getCurrentLanguage,
  type SupportedLanguage 
} from '@/lib/i18n/config'

/**
 * 语言切换组件
 * 提供语言选择和切换功能
 */
export function LanguageSwitcher({ 
  variant = 'default',
  size = 'default',
  showFlag = true,
  showText = true,
  className = ''
}: {
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'sm' | 'default' | 'lg'
  showFlag?: boolean
  showText?: boolean
  className?: string
}) {
  const { t, i18n } = useTranslation('common')
  const [currentLang, setCurrentLang] = useState<SupportedLanguage>('zh-CN')
  const [isChanging, setIsChanging] = useState(false)

  useEffect(() => {
    setCurrentLang(getCurrentLanguage())
    
    // 监听语言变化事件
    const handleLanguageChange = (event: CustomEvent) => {
      setCurrentLang(event.detail.language)
    }
    
    window.addEventListener('languageChanged', handleLanguageChange as EventListener)
    
    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener)
    }
  }, [])

  const handleLanguageChange = async (language: SupportedLanguage) => {
    if (language === currentLang || isChanging) return
    
    setIsChanging(true)
    
    try {
      const success = await changeLanguage(language)
      if (success) {
        setCurrentLang(language)
        
        // 显示成功提示
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.ready.then((registration) => {
            registration.showNotification(t('language.change'), {
              body: `${t('language.current')}: ${supportedLanguages[language].name}`,
              icon: '/icons/icon-192x192.png',
              badge: '/icons/badge-72x72.png',
              silent: true
            })
          })
        }
      }
    } catch (error) {
      console.error('Language change failed:', error)
    } finally {
      setIsChanging(false)
    }
  }

  const currentLanguageInfo = supportedLanguages[currentLang]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={`${className} ${isChanging ? 'opacity-50 cursor-not-allowed' : ''}`}
          disabled={isChanging}
        >
          <div className="flex items-center space-x-2">
            {showFlag && (
              <span className="text-lg" role="img" aria-label={currentLanguageInfo.name}>
                {currentLanguageInfo.flag}
              </span>
            )}
            {showText && (
              <span className="hidden sm:inline">
                {currentLanguageInfo.nativeName}
              </span>
            )}
            <Globe className="w-4 h-4 sm:hidden" />
            <ChevronDown className="w-3 h-3 opacity-50" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-48">
        {Object.entries(supportedLanguages).map(([code, info]) => (
          <DropdownMenuItem
            key={code}
            onClick={() => handleLanguageChange(code as SupportedLanguage)}
            className="flex items-center justify-between cursor-pointer"
          >
            <div className="flex items-center space-x-3">
              <span className="text-lg" role="img" aria-label={info.name}>
                {info.flag}
              </span>
              <div className="flex flex-col">
                <span className="font-medium">{info.nativeName}</span>
                <span className="text-xs text-gray-500">{info.name}</span>
              </div>
            </div>
            {currentLang === code && (
              <Check className="w-4 h-4 text-blue-600" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 紧凑型语言切换器
 * 适用于移动端或空间有限的场景
 */
export function CompactLanguageSwitcher({ className = '' }: { className?: string }) {
  return (
    <LanguageSwitcher
      variant="ghost"
      size="sm"
      showFlag={true}
      showText={false}
      className={className}
    />
  )
}

/**
 * 语言切换按钮组
 * 显示所有支持的语言作为按钮组
 */
export function LanguageButtonGroup({ 
  className = '',
  orientation = 'horizontal'
}: { 
  className?: string
  orientation?: 'horizontal' | 'vertical'
}) {
  const { t } = useTranslation('common')
  const [currentLang, setCurrentLang] = useState<SupportedLanguage>('zh-CN')
  const [isChanging, setIsChanging] = useState(false)

  useEffect(() => {
    setCurrentLang(getCurrentLanguage())
  }, [])

  const handleLanguageChange = async (language: SupportedLanguage) => {
    if (language === currentLang || isChanging) return
    
    setIsChanging(true)
    
    try {
      const success = await changeLanguage(language)
      if (success) {
        setCurrentLang(language)
      }
    } catch (error) {
      console.error('Language change failed:', error)
    } finally {
      setIsChanging(false)
    }
  }

  const containerClass = orientation === 'horizontal' 
    ? 'flex flex-wrap gap-2' 
    : 'flex flex-col gap-2'

  return (
    <div className={`${containerClass} ${className}`}>
      {Object.entries(supportedLanguages).map(([code, info]) => (
        <Button
          key={code}
          variant={currentLang === code ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleLanguageChange(code as SupportedLanguage)}
          disabled={isChanging}
          className="flex items-center space-x-2"
        >
          <span className="text-sm" role="img" aria-label={info.name}>
            {info.flag}
          </span>
          <span className="text-xs">{info.nativeName}</span>
        </Button>
      ))}
    </div>
  )
}

/**
 * 语言检测和自动切换Hook
 * 自动检测用户首选语言并切换
 */
export function useLanguageDetection() {
  const [isDetecting, setIsDetecting] = useState(true)
  const [detectedLanguage, setDetectedLanguage] = useState<SupportedLanguage | null>(null)

  useEffect(() => {
    const detectAndSetLanguage = async () => {
      try {
        // 检查是否已有保存的语言设置
        const savedLanguage = localStorage.getItem('i18nextLng')
        if (savedLanguage && savedLanguage in supportedLanguages) {
          setDetectedLanguage(savedLanguage as SupportedLanguage)
          setIsDetecting(false)
          return
        }

        // 检测浏览器语言
        const browserLang = navigator.language || navigator.languages?.[0]
        let targetLanguage: SupportedLanguage = 'zh-CN'

        // 精确匹配
        if (browserLang in supportedLanguages) {
          targetLanguage = browserLang as SupportedLanguage
        } else {
          // 语言代码匹配
          const langCode = browserLang.split('-')[0]
          if (langCode in supportedLanguages) {
            targetLanguage = langCode as SupportedLanguage
          }
        }

        // 自动切换到检测到的语言
        const success = await changeLanguage(targetLanguage)
        if (success) {
          setDetectedLanguage(targetLanguage)
        }
      } catch (error) {
        console.error('Language detection failed:', error)
      } finally {
        setIsDetecting(false)
      }
    }

    detectAndSetLanguage()
  }, [])

  return {
    isDetecting,
    detectedLanguage
  }
}

/**
 * 语言切换提示组件
 * 当检测到用户可能需要切换语言时显示
 */
export function LanguageSuggestion() {
  const { t } = useTranslation('common')
  const [showSuggestion, setShowSuggestion] = useState(false)
  const [suggestedLanguage, setSuggestedLanguage] = useState<SupportedLanguage | null>(null)

  useEffect(() => {
    const checkLanguageSuggestion = () => {
      const currentLang = getCurrentLanguage()
      const browserLang = navigator.language || navigator.languages?.[0]
      
      // 如果浏览器语言与当前语言不同，且浏览器语言被支持
      if (browserLang !== currentLang && browserLang in supportedLanguages) {
        const hasDeclined = localStorage.getItem('language-suggestion-declined')
        if (!hasDeclined) {
          setSuggestedLanguage(browserLang as SupportedLanguage)
          setShowSuggestion(true)
        }
      }
    }

    // 延迟检查，避免影响初始加载
    setTimeout(checkLanguageSuggestion, 2000)
  }, [])

  const handleAccept = async () => {
    if (suggestedLanguage) {
      await changeLanguage(suggestedLanguage)
    }
    setShowSuggestion(false)
  }

  const handleDecline = () => {
    localStorage.setItem('language-suggestion-declined', 'true')
    setShowSuggestion(false)
  }

  if (!showSuggestion || !suggestedLanguage) {
    return null
  }

  const suggestedInfo = supportedLanguages[suggestedLanguage]

  return (
    <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm">
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4">
        <div className="flex items-start space-x-3">
          <span className="text-2xl" role="img" aria-label={suggestedInfo.name}>
            {suggestedInfo.flag}
          </span>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">
              {t('language.change')}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              切换到 {suggestedInfo.nativeName}？
            </p>
            <div className="flex space-x-2 mt-3">
              <Button size="sm" onClick={handleAccept}>
                切换
              </Button>
              <Button variant="outline" size="sm" onClick={handleDecline}>
                保持当前
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
