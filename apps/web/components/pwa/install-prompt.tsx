'use client'

import { useState, useEffect } from 'react'
import { X, Download, Smartphone } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

/**
 * PWA安装提示组件
 * 引导用户安装PWA应用到设备
 */
export function InstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // 检查是否已安装PWA
    const checkIfInstalled = () => {
      // 检查是否在PWA模式下运行
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches
      const isInWebAppiOS = (window.navigator as any).standalone === true
      const isInWebAppChrome = window.matchMedia('(display-mode: minimal-ui)').matches
      
      setIsInstalled(isStandalone || isInWebAppiOS || isInWebAppChrome)
    }

    checkIfInstalled()

    // 监听beforeinstallprompt事件
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      
      // 检查用户是否之前拒绝过安装
      const hasDeclined = localStorage.getItem('pwa-install-declined')
      const declineTime = hasDeclined ? parseInt(hasDeclined) : 0
      const daysSinceDecline = (Date.now() - declineTime) / (1000 * 60 * 60 * 24)
      
      // 如果用户拒绝安装超过7天，或者从未拒绝过，则显示提示
      if (!hasDeclined || daysSinceDecline > 7) {
        setTimeout(() => {
          setShowInstallPrompt(true)
        }, 3000) // 3秒后显示提示
      }
    }

    // 监听appinstalled事件
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setShowInstallPrompt(false)
      setDeferredPrompt(null)
      localStorage.removeItem('pwa-install-declined')
      
      // 显示安装成功提示
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.showNotification('安装成功！', {
            body: 'AI数字营销平台已成功安装到您的设备',
            icon: '/icons/icon-192x192.png',
            badge: '/icons/badge-72x72.png'
          })
        })
      }
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  // 处理安装按钮点击
  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    try {
      // 显示安装提示
      await deferredPrompt.prompt()
      
      // 等待用户选择
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        console.log('用户接受了PWA安装')
      } else {
        console.log('用户拒绝了PWA安装')
        localStorage.setItem('pwa-install-declined', Date.now().toString())
      }
      
      setDeferredPrompt(null)
      setShowInstallPrompt(false)
    } catch (error) {
      console.error('PWA安装失败:', error)
    }
  }

  // 处理关闭按钮点击
  const handleDismiss = () => {
    setShowInstallPrompt(false)
    localStorage.setItem('pwa-install-declined', Date.now().toString())
  }

  // 如果已安装或不显示提示，则不渲染
  if (isInstalled || !showInstallPrompt || !deferredPrompt) {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm">
      <Card className="shadow-lg border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Smartphone className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  安装应用
                </CardTitle>
                <CardDescription className="text-sm text-gray-600">
                  获得更好的使用体验
                </CardDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="text-sm text-gray-700">
              <p className="mb-2">安装AI数字营销平台到您的设备，享受：</p>
              <ul className="space-y-1 text-xs text-gray-600">
                <li>• 🚀 更快的加载速度</li>
                <li>• 📱 原生应用体验</li>
                <li>• 🔔 实时消息推送</li>
                <li>• 📴 离线功能支持</li>
              </ul>
            </div>
            
            <div className="flex space-x-2">
              <Button
                onClick={handleInstallClick}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
                size="sm"
              >
                <Download className="w-4 h-4 mr-2" />
                立即安装
              </Button>
              <Button
                variant="outline"
                onClick={handleDismiss}
                size="sm"
                className="px-4"
              >
                稍后
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * PWA更新提示组件
 * 当有新版本可用时提示用户更新
 */
export function UpdatePrompt() {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false)
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null)

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        // 监听更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                setWaitingWorker(newWorker)
                setShowUpdatePrompt(true)
              }
            })
          }
        })
      })
    }
  }, [])

  const handleUpdate = () => {
    if (waitingWorker) {
      waitingWorker.postMessage({ type: 'SKIP_WAITING' })
      setShowUpdatePrompt(false)
      window.location.reload()
    }
  }

  if (!showUpdatePrompt) {
    return null
  }

  return (
    <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm">
      <Card className="shadow-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-green-900">新版本可用</h3>
              <p className="text-sm text-green-700">点击更新获取最新功能</p>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={handleUpdate}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                更新
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUpdatePrompt(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
