'use client'

import { useState, useEffect } from 'react'
import { MobileNavigation, MobilePageContainer } from './mobile-navigation'
import { InstallPrompt, UpdatePrompt } from '../pwa/install-prompt'

/**
 * 移动端主布局组件
 * 提供移动设备优化的布局结构
 */
export function MobileLayout({ children }: { children: React.ReactNode }) {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // 检测是否为移动设备
    const checkMobile = () => {
      const userAgent = navigator.userAgent.toLowerCase()
      const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
      const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
      const isSmallScreen = window.innerWidth < 1024
      
      setIsMobile(isMobileDevice || isSmallScreen)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  if (!isMobile) {
    // 桌面端使用原有布局
    return <>{children}</>
  }

  return (
    <div className="mobile-layout">
      {/* 移动端导航 */}
      <MobileNavigation />
      
      {/* 页面内容 */}
      <MobilePageContainer>
        {children}
      </MobilePageContainer>
      
      {/* PWA安装提示 */}
      <InstallPrompt />
      <UpdatePrompt />
    </div>
  )
}

/**
 * 移动端卡片组件
 * 针对移动设备优化的卡片布局
 */
export function MobileCard({ 
  children, 
  className = '',
  padding = 'default'
}: { 
  children: React.ReactNode
  className?: string
  padding?: 'none' | 'small' | 'default' | 'large'
}) {
  const paddingClasses = {
    none: 'p-0',
    small: 'p-3',
    default: 'p-4',
    large: 'p-6'
  }

  return (
    <div className={`
      bg-white rounded-xl shadow-sm border border-gray-100 
      ${paddingClasses[padding]} 
      ${className}
    `}>
      {children}
    </div>
  )
}

/**
 * 移动端网格布局组件
 * 响应式网格布局，在移动端自动调整列数
 */
export function MobileGrid({ 
  children, 
  columns = 'auto',
  gap = 'default',
  className = ''
}: { 
  children: React.ReactNode
  columns?: 'auto' | 1 | 2 | 3
  gap?: 'small' | 'default' | 'large'
  className?: string
}) {
  const columnClasses = {
    auto: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
  }

  const gapClasses = {
    small: 'gap-3',
    default: 'gap-4',
    large: 'gap-6'
  }

  return (
    <div className={`
      grid ${columnClasses[columns]} ${gapClasses[gap]} 
      ${className}
    `}>
      {children}
    </div>
  )
}

/**
 * 移动端列表组件
 * 针对触摸操作优化的列表
 */
export function MobileList({ 
  items, 
  renderItem,
  onItemClick,
  className = ''
}: {
  items: any[]
  renderItem: (item: any, index: number) => React.ReactNode
  onItemClick?: (item: any, index: number) => void
  className?: string
}) {
  return (
    <div className={`space-y-2 ${className}`}>
      {items.map((item, index) => (
        <div
          key={index}
          className={`
            bg-white rounded-lg border border-gray-100 p-4 
            ${onItemClick ? 'cursor-pointer active:bg-gray-50 transition-colors' : ''}
          `}
          onClick={() => onItemClick?.(item, index)}
        >
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  )
}

/**
 * 移动端底部操作栏组件
 * 固定在底部的操作按钮栏
 */
export function MobileActionBar({ 
  children,
  className = ''
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={`
      fixed bottom-20 left-0 right-0 z-30 
      bg-white border-t border-gray-200 p-4 
      ${className}
    `}>
      <div className="flex space-x-3">
        {children}
      </div>
    </div>
  )
}

/**
 * 移动端滑动容器组件
 * 支持水平滑动的容器
 */
export function MobileScrollContainer({ 
  children,
  className = ''
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={`
      overflow-x-auto scrollbar-hide 
      ${className}
    `}>
      <div className="flex space-x-4 pb-2">
        {children}
      </div>
    </div>
  )
}

/**
 * 移动端模态框组件
 * 全屏模态框，适合移动设备
 */
export function MobileModal({
  isOpen,
  onClose,
  title,
  children,
  showCloseButton = true
}: {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  showCloseButton?: boolean
}) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* 模态框内容 */}
      <div className="relative h-full bg-white flex flex-col">
        {/* 头部 */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              {title}
            </h2>
            {showCloseButton && (
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        )}
        
        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-4">
          {children}
        </div>
      </div>
    </div>
  )
}

/**
 * 移动端下拉刷新组件
 * 支持下拉刷新功能
 */
export function MobilePullToRefresh({
  onRefresh,
  children,
  className = ''
}: {
  onRefresh: () => Promise<void>
  children: React.ReactNode
  className?: string
}) {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const [startY, setStartY] = useState(0)

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartY(e.touches[0].clientY)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    const currentY = e.touches[0].clientY
    const distance = currentY - startY
    
    if (distance > 0 && window.scrollY === 0) {
      setPullDistance(Math.min(distance, 100))
    }
  }

  const handleTouchEnd = async () => {
    if (pullDistance > 60 && !isRefreshing) {
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setIsRefreshing(false)
      }
    }
    setPullDistance(0)
  }

  return (
    <div
      className={className}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 刷新指示器 */}
      {(pullDistance > 0 || isRefreshing) && (
        <div 
          className="flex items-center justify-center py-4 text-gray-500"
          style={{ transform: `translateY(${pullDistance}px)` }}
        >
          {isRefreshing ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-sm">正在刷新...</span>
            </div>
          ) : pullDistance > 60 ? (
            <span className="text-sm">松开刷新</span>
          ) : (
            <span className="text-sm">下拉刷新</span>
          )}
        </div>
      )}
      
      {children}
    </div>
  )
}
