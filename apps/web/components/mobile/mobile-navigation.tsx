'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { 
  Home, 
  Sparkles, 
  Target, 
  BarChart3, 
  CreditCard, 
  Menu, 
  X,
  User,
  Settings,
  LogOut
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
}

const navigationItems: NavigationItem[] = [
  {
    name: '仪表板',
    href: '/dashboard',
    icon: Home,
  },
  {
    name: 'AI生成',
    href: '/dashboard/ai',
    icon: Sparkles,
  },
  {
    name: '营销活动',
    href: '/dashboard/campaigns',
    icon: Target,
    badge: 3, // 示例：3个活跃活动
  },
  {
    name: '数据分析',
    href: '/dashboard/analytics',
    icon: BarChart3,
  },
  {
    name: '订阅管理',
    href: '/dashboard/billing',
    icon: CreditCard,
  },
]

/**
 * 移动端导航组件
 * 提供移动设备友好的导航体验
 */
export function MobileNavigation() {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  // 关闭导航菜单
  const closeNavigation = () => {
    setIsOpen(false)
  }

  // 路径变化时关闭菜单
  useEffect(() => {
    closeNavigation()
  }, [pathname])

  return (
    <>
      {/* 移动端顶部导航栏 */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo和标题 */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-lg font-semibold text-gray-900">AI营销</h1>
          </div>

          {/* 菜单按钮 */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0"
                data-testid="mobile-menu-button"
              >
                <Menu className="w-5 h-5" />
                <span className="sr-only">打开菜单</span>
              </Button>
            </SheetTrigger>
            
            <SheetContent 
              side="right" 
              className="w-80 p-0"
              data-testid="mobile-nav"
            >
              <div className="flex flex-col h-full">
                {/* 头部 */}
                <SheetHeader className="p-6 pb-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src="/avatars/user.jpg" alt="用户头像" />
                      <AvatarFallback className="bg-blue-100 text-blue-600">
                        用户
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <SheetTitle className="text-left text-lg font-semibold text-gray-900">
                        张三
                      </SheetTitle>
                      <p className="text-sm text-gray-500 truncate">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                </SheetHeader>

                <Separator />

                {/* 导航菜单 */}
                <nav className="flex-1 px-4 py-4">
                  <div className="space-y-1">
                    {navigationItems.map((item) => {
                      const isActive = pathname === item.href || 
                        (item.href !== '/dashboard' && pathname.startsWith(item.href))
                      
                      return (
                        <Link
                          key={item.href}
                          href={item.href}
                          className={`
                            flex items-center justify-between px-3 py-3 rounded-lg text-sm font-medium transition-colors
                            ${isActive 
                              ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                            }
                          `}
                          onClick={closeNavigation}
                        >
                          <div className="flex items-center space-x-3">
                            <item.icon className={`w-5 h-5 ${isActive ? 'text-blue-600' : 'text-gray-500'}`} />
                            <span>{item.name}</span>
                          </div>
                          {item.badge && (
                            <span className="bg-red-100 text-red-600 text-xs font-medium px-2 py-1 rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </Link>
                      )
                    })}
                  </div>
                </nav>

                <Separator />

                {/* 底部菜单 */}
                <div className="p-4 space-y-1">
                  <Link
                    href="/dashboard/profile"
                    className="flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors"
                    onClick={closeNavigation}
                  >
                    <User className="w-5 h-5 text-gray-500" />
                    <span>个人资料</span>
                  </Link>
                  
                  <Link
                    href="/dashboard/settings"
                    className="flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors"
                    onClick={closeNavigation}
                  >
                    <Settings className="w-5 h-5 text-gray-500" />
                    <span>设置</span>
                  </Link>
                  
                  <button
                    className="w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium text-red-600 hover:bg-red-50 transition-colors"
                    onClick={() => {
                      closeNavigation()
                      // 这里添加登出逻辑
                      console.log('用户登出')
                    }}
                  >
                    <LogOut className="w-5 h-5" />
                    <span>退出登录</span>
                  </button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* 移动端底部导航栏 */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200">
        <div className="grid grid-cols-5 py-2">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href || 
              (item.href !== '/dashboard' && pathname.startsWith(item.href))
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`
                  flex flex-col items-center justify-center py-2 px-1 relative
                  ${isActive ? 'text-blue-600' : 'text-gray-500'}
                `}
              >
                <div className="relative">
                  <item.icon className="w-5 h-5" />
                  {item.badge && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-medium w-4 h-4 rounded-full flex items-center justify-center">
                      {item.badge > 9 ? '9+' : item.badge}
                    </span>
                  )}
                </div>
                <span className="text-xs mt-1 font-medium truncate max-w-full">
                  {item.name}
                </span>
                {isActive && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-blue-600 rounded-full" />
                )}
              </Link>
            )
          })}
        </div>
      </div>

      {/* 为固定导航栏添加顶部和底部间距 */}
      <div className="lg:hidden h-16" /> {/* 顶部间距 */}
      <div className="lg:hidden h-20" /> {/* 底部间距，在页面底部添加 */}
    </>
  )
}

/**
 * 移动端页面容器组件
 * 为移动端页面提供正确的间距和布局
 */
export function MobilePageContainer({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <div className={`lg:hidden min-h-screen bg-gray-50 ${className}`}>
      {/* 顶部导航栏占位 */}
      <div className="h-16" />
      
      {/* 页面内容 */}
      <main className="pb-20 px-4 py-4">
        {children}
      </main>
      
      {/* 底部导航栏占位 */}
      <div className="h-20" />
    </div>
  )
}

/**
 * 移动端手势支持Hook
 * 提供滑动手势支持
 */
export function useMobileGestures() {
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const minSwipeDistance = 50

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    return { isLeftSwipe, isRightSwipe, distance }
  }

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd,
  }
}
