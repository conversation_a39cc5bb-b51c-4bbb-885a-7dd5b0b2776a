# AI数字营销平台 Web前端 Dockerfile
# 多阶段构建，优化镜像大小和性能

# ===========================================
# 基础镜像阶段
# ===========================================
FROM node:20-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    libc6-compat \
    openssl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# 设置环境变量
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# ===========================================
# 依赖安装阶段
# ===========================================
FROM base AS deps

# 复制包管理文件
COPY package.json package-lock.json* ./
COPY apps/web/package.json ./apps/web/

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# ===========================================
# 构建阶段
# ===========================================
FROM base AS builder

# 复制源代码和依赖
COPY . .
COPY --from=deps /app/node_modules ./node_modules

# 设置构建环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 构建Next.js应用
RUN npm run build --workspace=apps/web

# ===========================================
# 生产运行阶段
# ===========================================
FROM base AS runner

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 复制必要文件
COPY --from=builder /app/apps/web/public ./public

# 复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./.next/static

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动命令
CMD ["node", "server.js"]

# ===========================================
# 开发环境阶段
# ===========================================
FROM base AS development

# 复制源代码
COPY . .

# 安装所有依赖（包括开发依赖）
RUN npm ci

# 切换到Web目录
WORKDIR /app/apps/web

# 暴露端口
EXPOSE 3000

# 开发环境启动命令
CMD ["npm", "run", "dev"]
