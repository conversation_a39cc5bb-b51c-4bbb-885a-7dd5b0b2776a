'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { ThemeProvider } from 'next-themes'
import { useState } from 'react'

export function Providers({ children }: { children: React.ReactNode }) {
  // 创建QueryClient实例，避免在服务端和客户端之间共享
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // 数据在5分钟后过期
            staleTime: 5 * 60 * 1000,
            // 缓存时间为10分钟
            gcTime: 10 * 60 * 1000,
            // 窗口重新获得焦点时重新获取数据
            refetchOnWindowFocus: false,
            // 网络重连时重新获取数据
            refetchOnReconnect: true,
            // 失败重试次数
            retry: (failureCount, error: any) => {
              // 对于4xx错误不重试
              if (error?.status >= 400 && error?.status < 500) {
                return false
              }
              // 最多重试3次
              return failureCount < 3
            },
          },
          mutations: {
            // 失败重试次数
            retry: 1,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        {children}
      </ThemeProvider>
      {/* 开发环境下显示React Query开发工具 */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  )
}
