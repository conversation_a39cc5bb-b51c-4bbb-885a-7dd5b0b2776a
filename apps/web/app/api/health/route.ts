import { NextResponse } from 'next/server'

/**
 * 健康检查API端点
 * 用于监控前端应用的运行状态
 */
export async function GET() {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
      checks: {
        api: 'pending', // 这里可以添加对后端API的健康检查
      }
    }

    // 可以在这里添加更多的健康检查逻辑
    // 例如检查数据库连接、外部服务等

    return NextResponse.json(healthData, { status: 200 })
  } catch (error) {
    console.error('健康检查失败:', error)
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    )
  }
}
