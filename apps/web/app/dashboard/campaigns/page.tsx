'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Play, 
  Pause, 
  Edit, 
  Trash2,
  BarChart3,
  Users,
  Mail,
  MessageSquare,
  Smartphone,
  Monitor
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// 模拟数据
const mockCampaigns = [
  {
    id: '1',
    name: '春季新品推广活动',
    description: '针对春季新品的全渠道营销推广',
    type: 'EMAIL',
    status: 'ACTIVE',
    budget: 50000,
    targetAudience: '年轻女性用户',
    createdAt: '2024-01-15',
    metrics: {
      impressions: 125000,
      clicks: 3200,
      conversions: 156,
      ctr: 2.56,
      conversionRate: 4.88
    }
  },
  {
    id: '2',
    name: '会员专享优惠',
    description: '针对VIP会员的专属优惠活动',
    type: 'SMS',
    status: 'DRAFT',
    budget: 20000,
    targetAudience: 'VIP会员',
    createdAt: '2024-01-20',
    metrics: {
      impressions: 0,
      clicks: 0,
      conversions: 0,
      ctr: 0,
      conversionRate: 0
    }
  }
]

const campaignTypes = [
  { value: 'EMAIL', label: '邮件营销', icon: Mail },
  { value: 'SMS', label: '短信营销', icon: MessageSquare },
  { value: 'SOCIAL', label: '社交媒体', icon: Users },
  { value: 'PUSH', label: '推送通知', icon: Smartphone },
  { value: 'DISPLAY', label: '展示广告', icon: Monitor },
  { value: 'SEARCH', label: '搜索广告', icon: Search },
]

const statusColors = {
  DRAFT: 'bg-gray-100 text-gray-800',
  ACTIVE: 'bg-green-100 text-green-800',
  PAUSED: 'bg-yellow-100 text-yellow-800',
  COMPLETED: 'bg-blue-100 text-blue-800',
  CANCELLED: 'bg-red-100 text-red-800',
}

const statusLabels = {
  DRAFT: '草稿',
  ACTIVE: '活跃',
  PAUSED: '暂停',
  COMPLETED: '已完成',
  CANCELLED: '已取消',
}

export default function CampaignsPage() {
  const [campaigns, setCampaigns] = useState(mockCampaigns)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const { toast } = useToast()

  // 新建活动表单状态
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    description: '',
    type: '',
    budget: '',
    targetAudience: ''
  })

  const handleCreateCampaign = async () => {
    if (!newCampaign.name || !newCampaign.type) {
      toast({
        title: "错误",
        description: "请填写活动名称和类型",
        variant: "destructive",
      })
      return
    }

    try {
      // TODO: 调用API创建活动
      const campaign = {
        id: Date.now().toString(),
        ...newCampaign,
        status: 'DRAFT' as const,
        budget: parseFloat(newCampaign.budget) || 0,
        createdAt: new Date().toISOString().split('T')[0],
        metrics: {
          impressions: 0,
          clicks: 0,
          conversions: 0,
          ctr: 0,
          conversionRate: 0
        }
      }

      setCampaigns([campaign, ...campaigns])
      setNewCampaign({
        name: '',
        description: '',
        type: '',
        budget: '',
        targetAudience: ''
      })
      setShowCreateForm(false)

      toast({
        title: "创建成功",
        description: "营销活动已创建",
      })
    } catch (error) {
      toast({
        title: "创建失败",
        description: "创建活动时出现错误，请重试",
        variant: "destructive",
      })
    }
  }

  const handleStatusChange = (campaignId: string, newStatus: string) => {
    setCampaigns(campaigns.map(campaign => 
      campaign.id === campaignId 
        ? { ...campaign, status: newStatus as any }
        : campaign
    ))

    toast({
      title: "状态更新",
      description: `活动状态已更新为${statusLabels[newStatus as keyof typeof statusLabels]}`,
    })
  }

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         campaign.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || campaign.type === filterType
    const matchesStatus = filterStatus === 'all' || campaign.status === filterStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const getCampaignTypeIcon = (type: string) => {
    const campaignType = campaignTypes.find(t => t.value === type)
    return campaignType?.icon || Mail
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">营销活动</h1>
          <p className="text-gray-600">创建和管理您的营销活动，提升营销效果</p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          新建活动
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索活动名称或描述..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="活动类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                {campaignTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="活动状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                {Object.entries(statusLabels).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 活动列表 */}
      <div className="grid gap-6">
        {filteredCampaigns.map(campaign => {
          const TypeIcon = getCampaignTypeIcon(campaign.type)
          return (
            <Card key={campaign.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <TypeIcon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{campaign.name}</CardTitle>
                      <CardDescription className="mt-1">
                        {campaign.description}
                      </CardDescription>
                      <div className="flex items-center gap-4 mt-3">
                        <Badge className={statusColors[campaign.status as keyof typeof statusColors]}>
                          {statusLabels[campaign.status as keyof typeof statusLabels]}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {campaignTypes.find(t => t.value === campaign.type)?.label}
                        </span>
                        <span className="text-sm text-gray-500">
                          预算: ¥{campaign.budget.toLocaleString()}
                        </span>
                        <span className="text-sm text-gray-500">
                          创建于: {campaign.createdAt}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {campaign.status === 'ACTIVE' ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusChange(campaign.id, 'PAUSED')}
                      >
                        <Pause className="w-4 h-4 mr-1" />
                        暂停
                      </Button>
                    ) : campaign.status === 'PAUSED' ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusChange(campaign.id, 'ACTIVE')}
                      >
                        <Play className="w-4 h-4 mr-1" />
                        启动
                      </Button>
                    ) : campaign.status === 'DRAFT' ? (
                      <Button
                        size="sm"
                        onClick={() => handleStatusChange(campaign.id, 'ACTIVE')}
                      >
                        <Play className="w-4 h-4 mr-1" />
                        启动
                      </Button>
                    ) : null}
                    <Button variant="ghost" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {campaign.metrics.impressions.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">展示次数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {campaign.metrics.clicks.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">点击次数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {campaign.metrics.conversions.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">转化次数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {campaign.metrics.ctr.toFixed(2)}%
                    </div>
                    <div className="text-sm text-gray-500">点击率</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {campaign.metrics.conversionRate.toFixed(2)}%
                    </div>
                    <div className="text-sm text-gray-500">转化率</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredCampaigns.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">暂无营销活动</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || filterType !== 'all' || filterStatus !== 'all' 
                ? '没有找到符合条件的活动' 
                : '开始创建您的第一个营销活动'}
            </p>
            {!searchTerm && filterType === 'all' && filterStatus === 'all' && (
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                新建活动
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* 创建活动对话框 */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>创建新的营销活动</CardTitle>
              <CardDescription>
                填写活动信息，开始您的营销之旅
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">活动名称 *</Label>
                <Input
                  id="name"
                  placeholder="输入活动名称"
                  value={newCampaign.name}
                  onChange={(e) => setNewCampaign({...newCampaign, name: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">活动描述</Label>
                <Textarea
                  id="description"
                  placeholder="描述活动的目标和内容"
                  value={newCampaign.description}
                  onChange={(e) => setNewCampaign({...newCampaign, description: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>活动类型 *</Label>
                <Select value={newCampaign.type} onValueChange={(value) => setNewCampaign({...newCampaign, type: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择活动类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {campaignTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="budget">活动预算</Label>
                  <Input
                    id="budget"
                    type="number"
                    placeholder="0"
                    value={newCampaign.budget}
                    onChange={(e) => setNewCampaign({...newCampaign, budget: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetAudience">目标受众</Label>
                  <Input
                    id="targetAudience"
                    placeholder="例如：年轻女性用户"
                    value={newCampaign.targetAudience}
                    onChange={(e) => setNewCampaign({...newCampaign, targetAudience: e.target.value})}
                  />
                </div>
              </div>

              <div className="flex justify-end gap-4 pt-4">
                <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateCampaign}>
                  创建活动
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
