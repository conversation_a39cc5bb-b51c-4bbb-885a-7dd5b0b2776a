'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  Check, 
  X, 
  Settings,
  Download,
  AlertCircle,
  Crown,
  Zap,
  Star
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// 订阅计划配置
const plans = [
  {
    id: 'basic',
    name: '基础版',
    price: 99,
    priceId: 'price_basic_monthly',
    description: '适合个人用户和小团队',
    features: [
      '每月100次AI内容生成',
      '5个营销活动',
      '基础数据分析',
      '邮件支持',
      '基础模板库'
    ],
    limitations: [
      '不支持高级AI功能',
      '有限的数据导出'
    ],
    popular: false
  },
  {
    id: 'pro',
    name: '专业版',
    price: 299,
    priceId: 'price_pro_monthly',
    description: '适合成长中的企业',
    features: [
      '每月500次AI内容生成',
      '无限营销活动',
      '高级数据分析',
      '优先支持',
      '完整模板库',
      '自定义品牌',
      'API访问',
      '团队协作'
    ],
    limitations: [],
    popular: true
  },
  {
    id: 'enterprise',
    name: '企业版',
    price: 999,
    priceId: 'price_enterprise_monthly',
    description: '适合大型企业',
    features: [
      '无限AI内容生成',
      '无限营销活动',
      '企业级数据分析',
      '专属客户经理',
      '定制化功能',
      '私有部署选项',
      'SSO集成',
      '高级安全功能',
      '培训和咨询'
    ],
    limitations: [],
    popular: false
  }
]

// 模拟当前订阅数据
const mockSubscription = {
  id: 'sub_1234567890',
  status: 'active',
  planId: 'pro',
  currentPeriodStart: '2024-01-01',
  currentPeriodEnd: '2024-02-01',
  cancelAtPeriodEnd: false,
  trialEnd: null,
}

export default function BillingPage() {
  const [subscription, setSubscription] = useState(mockSubscription)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const { toast } = useToast()

  const currentPlan = plans.find(plan => plan.id === subscription?.planId)

  const handleUpgrade = async (priceId: string, planId: string) => {
    setIsLoading(true)
    setSelectedPlan(planId)
    
    try {
      // TODO: 调用API创建支付会话
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 模拟重定向到Stripe
      toast({
        title: "正在跳转到支付页面",
        description: "请稍候，正在为您创建支付会话...",
      })
      
      // 实际应用中这里会重定向到Stripe Checkout
      // window.location.href = checkoutUrl
      
    } catch (error) {
      toast({
        title: "升级失败",
        description: "创建支付会话时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setSelectedPlan(null)
    }
  }

  const handleCancelSubscription = async () => {
    if (!confirm('确定要取消订阅吗？订阅将在当前计费周期结束时取消。')) {
      return
    }

    setIsLoading(true)
    try {
      // TODO: 调用API取消订阅
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setSubscription({
        ...subscription,
        cancelAtPeriodEnd: true
      })
      
      toast({
        title: "订阅已取消",
        description: "您的订阅将在当前计费周期结束时取消",
      })
    } catch (error) {
      toast({
        title: "取消失败",
        description: "取消订阅时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleManageBilling = async () => {
    setIsLoading(true)
    try {
      // TODO: 调用API创建客户门户会话
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "正在跳转到账单管理",
        description: "请稍候，正在为您创建管理会话...",
      })
      
      // 实际应用中这里会重定向到Stripe客户门户
      // window.location.href = portalUrl
      
    } catch (error) {
      toast({
        title: "跳转失败",
        description: "创建管理会话时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">订阅管理</h1>
        <p className="text-gray-600">管理您的订阅计划和账单信息</p>
      </div>

      {/* 当前订阅状态 */}
      {subscription && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              当前订阅
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    {currentPlan?.id === 'enterprise' ? (
                      <Crown className="w-6 h-6 text-blue-600" />
                    ) : currentPlan?.id === 'pro' ? (
                      <Zap className="w-6 h-6 text-blue-600" />
                    ) : (
                      <Star className="w-6 h-6 text-blue-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{currentPlan?.name}</h3>
                    <p className="text-gray-600">{currentPlan?.description}</p>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge 
                      className={
                        subscription.status === 'active' 
                          ? 'bg-green-100 text-green-800'
                          : subscription.status === 'trialing'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-red-100 text-red-800'
                      }
                    >
                      {subscription.status === 'active' ? '活跃' : 
                       subscription.status === 'trialing' ? '试用中' : '已取消'}
                    </Badge>
                    {subscription.cancelAtPeriodEnd && (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        将于期末取消
                      </Badge>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center gap-2 mb-1">
                      <Calendar className="w-4 h-4" />
                      计费周期: {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4" />
                      月费: ¥{currentPlan?.price}/月
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-3">
                <Button 
                  variant="outline" 
                  onClick={handleManageBilling}
                  disabled={isLoading}
                  className="w-full"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  管理账单信息
                </Button>
                
                <Button 
                  variant="outline" 
                  className="w-full"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下载发票
                </Button>
                
                {!subscription.cancelAtPeriodEnd && (
                  <Button 
                    variant="destructive" 
                    onClick={handleCancelSubscription}
                    disabled={isLoading}
                    className="w-full"
                  >
                    取消订阅
                  </Button>
                )}
              </div>
            </div>

            {subscription.cancelAtPeriodEnd && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800">订阅即将取消</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      您的订阅将在 {formatDate(subscription.currentPeriodEnd)} 取消。
                      在此之前，您仍可以正常使用所有功能。
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 订阅计划 */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-6">选择订阅计划</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${plan.popular ? 'border-blue-500 shadow-lg' : ''} ${
                currentPlan?.id === plan.id ? 'bg-blue-50 border-blue-300' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white px-3 py-1">
                    最受欢迎
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
                <div className="mt-4">
                  <span className="text-3xl font-bold">¥{plan.price}</span>
                  <span className="text-gray-600">/月</span>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">包含功能:</h4>
                    <ul className="space-y-2">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-2 text-sm">
                          <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {plan.limitations.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">限制:</h4>
                      <ul className="space-y-2">
                        {plan.limitations.map((limitation, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                            <X className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                            <span>{limitation}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div className="pt-2">
                    {currentPlan?.id === plan.id ? (
                      <Button disabled className="w-full">
                        当前计划
                      </Button>
                    ) : (
                      <Button 
                        onClick={() => handleUpgrade(plan.priceId, plan.id)}
                        disabled={isLoading}
                        className="w-full"
                        variant={plan.popular ? "default" : "outline"}
                      >
                        {isLoading && selectedPlan === plan.id ? (
                          "处理中..."
                        ) : currentPlan && plans.findIndex(p => p.id === currentPlan.id) < plans.findIndex(p => p.id === plan.id) ? (
                          "升级到此计划"
                        ) : (
                          "选择此计划"
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 账单历史 */}
      <Card>
        <CardHeader>
          <CardTitle>账单历史</CardTitle>
          <CardDescription>查看您的付款记录和发票</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <CreditCard className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>暂无账单记录</p>
            <p className="text-sm">您的付款记录将在这里显示</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
