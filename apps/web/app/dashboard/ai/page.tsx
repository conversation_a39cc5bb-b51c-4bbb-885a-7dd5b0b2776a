'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Loader2, Sparkles, Copy, Download, RefreshCw } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function AIContentPage() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [prompt, setPrompt] = useState('')
  const [contentType, setContentType] = useState('marketing_copy')
  const [tone, setTone] = useState('professional')
  const [length, setLength] = useState('medium')
  const { toast } = useToast()

  const handleGenerateContent = async () => {
    if (!prompt.trim()) {
      toast({
        title: "错误",
        description: "请输入生成提示词",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    try {
      // TODO: 调用API生成内容
      // 这里先模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const mockContent = `根据您的提示词"${prompt}"，我为您生成了以下${contentType === 'marketing_copy' ? '营销文案' : '内容'}：

这是一个专业的营销文案示例，突出了产品的核心价值主张。通过精心设计的语言和结构，能够有效吸引目标受众的注意力，并促使他们采取行动。

主要特点：
• 清晰的价值主张
• 吸引人的标题
• 强有力的行动号召
• 针对目标受众的个性化内容

这个文案经过AI优化，确保了语言的流畅性和说服力，能够在各种营销渠道中有效使用。`

      setGeneratedContent(mockContent)
      toast({
        title: "生成成功",
        description: "AI内容已生成完成",
      })
    } catch (error) {
      toast({
        title: "生成失败",
        description: "内容生成过程中出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopyContent = () => {
    navigator.clipboard.writeText(generatedContent)
    toast({
      title: "复制成功",
      description: "内容已复制到剪贴板",
    })
  }

  const handleOptimizeContent = async () => {
    if (!generatedContent.trim()) {
      toast({
        title: "错误",
        description: "没有可优化的内容",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    try {
      // TODO: 调用内容优化API
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const optimizedContent = generatedContent + "\n\n[优化建议]\n• 可以添加更多具体的数据支撑\n• 建议增强情感化表达\n• 可以优化关键词密度"
      
      setGeneratedContent(optimizedContent)
      toast({
        title: "优化完成",
        description: "内容已优化并添加了改进建议",
      })
    } catch (error) {
      toast({
        title: "优化失败",
        description: "内容优化过程中出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">AI内容生成</h1>
        <p className="text-gray-600">使用AI技术生成高质量的营销内容，提升您的营销效果</p>
      </div>

      <Tabs defaultValue="text" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="text">文本生成</TabsTrigger>
          <TabsTrigger value="image">图像生成</TabsTrigger>
          <TabsTrigger value="optimize">内容优化</TabsTrigger>
        </TabsList>

        <TabsContent value="text" className="space-y-6">
          <div className="grid lg:grid-cols-2 gap-6">
            {/* 输入区域 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  内容生成设置
                </CardTitle>
                <CardDescription>
                  配置生成参数，让AI为您创作精彩内容
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="prompt">生成提示词</Label>
                  <Textarea
                    id="prompt"
                    placeholder="请描述您想要生成的内容，例如：为一款智能手表写一段营销文案，突出健康监测功能..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>内容类型</Label>
                    <Select value={contentType} onValueChange={setContentType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="marketing_copy">营销文案</SelectItem>
                        <SelectItem value="social_media">社交媒体</SelectItem>
                        <SelectItem value="email">邮件营销</SelectItem>
                        <SelectItem value="blog">博客文章</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>语调风格</Label>
                    <Select value={tone} onValueChange={setTone}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="professional">专业</SelectItem>
                        <SelectItem value="casual">轻松</SelectItem>
                        <SelectItem value="friendly">友好</SelectItem>
                        <SelectItem value="persuasive">说服性</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>内容长度</Label>
                    <Select value={length} onValueChange={setLength}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="short">简短</SelectItem>
                        <SelectItem value="medium">中等</SelectItem>
                        <SelectItem value="long">详细</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button 
                  onClick={handleGenerateContent} 
                  disabled={isGenerating || !prompt.trim()}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      生成内容
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* 输出区域 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  生成结果
                  <div className="flex gap-2">
                    {generatedContent && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleOptimizeContent}
                          disabled={isGenerating}
                        >
                          <RefreshCw className="w-4 h-4 mr-1" />
                          优化
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCopyContent}
                        >
                          <Copy className="w-4 h-4 mr-1" />
                          复制
                        </Button>
                      </>
                    )}
                  </div>
                </CardTitle>
                <CardDescription>
                  AI生成的内容将在这里显示
                </CardDescription>
              </CardHeader>
              <CardContent>
                {generatedContent ? (
                  <div className="space-y-4">
                    <div className="flex gap-2 mb-4">
                      <Badge variant="secondary">{contentType}</Badge>
                      <Badge variant="outline">{tone}</Badge>
                      <Badge variant="outline">{length}</Badge>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <pre className="whitespace-pre-wrap text-sm font-sans">
                        {generatedContent}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-500">
                    <Sparkles className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>输入提示词并点击生成按钮开始创作</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="image" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>图像生成</CardTitle>
              <CardDescription>
                使用AI生成营销图片和创意素材
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <p>图像生成功能正在开发中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimize" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>内容优化</CardTitle>
              <CardDescription>
                优化现有内容，提高质量和效果
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <p>内容优化功能正在开发中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
