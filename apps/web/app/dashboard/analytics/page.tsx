'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  MousePointer, 
  Target, 
  DollarSign,
  Activity,
  Download,
  RefreshCw,
  Eye,
  Clock,
  Zap,
  Brain
} from "lucide-react"

// 模拟数据
const mockDashboardData = {
  overview: {
    totalCampaigns: 12,
    activeCampaigns: 5,
    totalClicks: 15420,
    conversionRate: 3.2,
    aiGenerations: 45,
    thisMonthGenerations: 18,
  },
  recentActivity: [
    {
      id: '1',
      type: 'AI_GENERATION',
      title: '生成了文本内容：为智能手表写营销文案...',
      status: 'COMPLETED',
      createdAt: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: '2',
      type: 'CAMPAIGN_LAUNCHED',
      title: '启动了营销活动：春季新品推广',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: '3',
      type: 'AI_GENERATION',
      title: '生成了图像内容：产品展示图片...',
      status: 'COMPLETED',
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
    }
  ],
  topPerformingCampaigns: [
    {
      id: '1',
      name: '春季新品推广活动',
      type: 'EMAIL',
      metrics: {
        clicks: 3200,
        conversions: 156,
        conversionRate: 4.88,
        revenue: 45600
      }
    },
    {
      id: '2',
      name: '会员专享优惠',
      type: 'SMS',
      metrics: {
        clicks: 1800,
        conversions: 89,
        conversionRate: 4.94,
        revenue: 28900
      }
    }
  ],
  campaignMetrics: {
    daily: [
      { date: '01-20', clicks: 1200, impressions: 18500, conversions: 45 },
      { date: '01-21', clicks: 1350, impressions: 19200, conversions: 52 },
      { date: '01-22', clicks: 1180, impressions: 17800, conversions: 38 },
      { date: '01-23', clicks: 1420, impressions: 20100, conversions: 58 },
      { date: '01-24', clicks: 1290, impressions: 18900, conversions: 41 },
      { date: '01-25', clicks: 1380, impressions: 19600, conversions: 49 },
      { date: '01-26', clicks: 1450, impressions: 20800, conversions: 62 },
    ]
  }
}

const activityTypeLabels = {
  'AI_GENERATION': 'AI生成',
  'CAMPAIGN_CREATED': '活动创建',
  'CAMPAIGN_LAUNCHED': '活动启动',
  'CAMPAIGN_PAUSED': '活动暂停',
  'CAMPAIGN_COMPLETED': '活动完成'
}

const activityTypeColors = {
  'AI_GENERATION': 'bg-purple-100 text-purple-800',
  'CAMPAIGN_CREATED': 'bg-blue-100 text-blue-800',
  'CAMPAIGN_LAUNCHED': 'bg-green-100 text-green-800',
  'CAMPAIGN_PAUSED': 'bg-yellow-100 text-yellow-800',
  'CAMPAIGN_COMPLETED': 'bg-gray-100 text-gray-800'
}

export default function AnalyticsPage() {
  const [dashboardData, setDashboardData] = useState(mockDashboardData)
  const [isLoading, setIsLoading] = useState(false)
  const [timeRange, setTimeRange] = useState('7d')

  const refreshData = async () => {
    setIsLoading(true)
    try {
      // TODO: 调用API获取数据
      await new Promise(resolve => setTimeout(resolve, 1000))
      // setDashboardData(newData)
    } catch (error) {
      console.error('刷新数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else {
      return `${days}天前`
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">数据分析</h1>
          <p className="text-gray-600">深入了解您的营销表现和AI使用情况</p>
        </div>
        <div className="flex gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">最近7天</SelectItem>
              <SelectItem value="30d">最近30天</SelectItem>
              <SelectItem value="90d">最近90天</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={refreshData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button>
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="campaigns">营销活动</TabsTrigger>
          <TabsTrigger value="ai-usage">AI使用</TabsTrigger>
          <TabsTrigger value="insights">数据洞察</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* 关键指标卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总活动数</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.totalCampaigns}</div>
                <p className="text-xs text-muted-foreground">
                  活跃: {dashboardData.overview.activeCampaigns}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总点击数</CardTitle>
                <MousePointer className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.totalClicks.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  +12.5% 较上周
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">转化率</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.conversionRate}%</div>
                <p className="text-xs text-muted-foreground">
                  +0.3% 较上周
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI生成次数</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.aiGenerations}</div>
                <p className="text-xs text-muted-foreground">
                  本月: {dashboardData.overview.thisMonthGenerations}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 图表和活动 */}
          <div className="grid lg:grid-cols-2 gap-6">
            {/* 性能趋势图 */}
            <Card>
              <CardHeader>
                <CardTitle>性能趋势</CardTitle>
                <CardDescription>最近7天的点击和转化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>图表组件开发中...</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 最近活动 */}
            <Card>
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
                <CardDescription>最新的营销活动和AI使用记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {activity.type === 'AI_GENERATION' ? (
                          <Brain className="w-4 h-4 text-purple-600" />
                        ) : (
                          <Activity className="w-4 h-4 text-blue-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {activity.title}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge 
                            className={activityTypeColors[activity.type as keyof typeof activityTypeColors]}
                          >
                            {activityTypeLabels[activity.type as keyof typeof activityTypeLabels]}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {formatTime(activity.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 表现最佳的活动 */}
          <Card>
            <CardHeader>
              <CardTitle>表现最佳的活动</CardTitle>
              <CardDescription>根据转化率排序的营销活动</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.topPerformingCampaigns.map((campaign, index) => (
                  <div key={campaign.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="text-lg font-bold text-gray-400">#{index + 1}</div>
                      <div>
                        <h3 className="font-medium">{campaign.name}</h3>
                        <Badge variant="outline">{campaign.type}</Badge>
                      </div>
                    </div>
                    <div className="grid grid-cols-4 gap-8 text-center">
                      <div>
                        <div className="text-lg font-bold text-blue-600">
                          {campaign.metrics.clicks.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-500">点击</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600">
                          {campaign.metrics.conversions}
                        </div>
                        <div className="text-xs text-gray-500">转化</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600">
                          {campaign.metrics.conversionRate}%
                        </div>
                        <div className="text-xs text-gray-500">转化率</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-orange-600">
                          ¥{campaign.metrics.revenue.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-500">收入</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>营销活动分析</CardTitle>
              <CardDescription>详细的营销活动表现数据</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>营销活动分析功能开发中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai-usage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI使用分析</CardTitle>
              <CardDescription>AI内容生成的使用情况和效果分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>AI使用分析功能开发中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>数据洞察</CardTitle>
              <CardDescription>基于AI分析的营销建议和洞察</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>数据洞察功能开发中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
