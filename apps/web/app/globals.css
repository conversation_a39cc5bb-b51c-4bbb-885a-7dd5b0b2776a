@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* 自定义滚动条样式 */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--foreground));
  }
}

/* 加载动画 */
@layer components {
  .loading-spinner {
    @apply inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite];
  }

  .loading-dots {
    @apply inline-flex items-center;
  }

  .loading-dots::after {
    content: '';
    @apply ml-1 inline-block h-1 w-1 animate-pulse rounded-full bg-current;
    animation: loading-dots 1.4s infinite both;
  }

  @keyframes loading-dots {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
}

/* 渐变背景 */
@layer utilities {
  .gradient-bg {
    background: linear-gradient(135deg, 
      hsl(var(--primary)) 0%, 
      hsl(var(--primary) / 0.8) 50%, 
      hsl(var(--secondary)) 100%);
  }

  .gradient-text {
    background: linear-gradient(135deg, 
      hsl(var(--primary)) 0%, 
      hsl(var(--accent)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* 卡片阴影效果 */
@layer components {
  .card-shadow {
    @apply shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .card-shadow-lg {
    @apply shadow-md hover:shadow-lg transition-shadow duration-200;
  }
}

/* 按钮动画效果 */
@layer components {
  .btn-animate {
    @apply transition-all duration-200 ease-in-out;
    @apply hover:scale-105 active:scale-95;
  }

  .btn-glow {
    @apply relative overflow-hidden;
  }

  .btn-glow::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    @apply translate-x-[-100%] transition-transform duration-500;
  }

  .btn-glow:hover::before {
    @apply translate-x-[100%];
  }
}

/* 表单样式 */
@layer components {
  .form-input {
    @apply w-full rounded-md border border-input bg-background px-3 py-2 text-sm;
    @apply placeholder:text-muted-foreground;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    @apply disabled:cursor-not-allowed disabled:opacity-50;
  }

  .form-label {
    @apply text-sm font-medium leading-none;
    @apply peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .form-error {
    @apply text-sm font-medium text-destructive;
  }
}

/* 响应式文本大小 */
@layer utilities {
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    @apply text-black bg-white;
  }

  .card-shadow,
  .card-shadow-lg {
    @apply shadow-none border;
  }
}
