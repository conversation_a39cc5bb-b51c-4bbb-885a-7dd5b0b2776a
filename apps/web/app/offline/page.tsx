import { Metadata } from 'next'
import { WifiOff, RefreshCw, Home, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export const metadata: Metadata = {
  title: '离线模式 - AI数字营销平台',
  description: '当前处于离线状态，请检查网络连接',
}

/**
 * 离线页面组件
 * 当用户离线时显示的友好提示页面
 */
export default function OfflinePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto shadow-lg">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto mb-4 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <WifiOff className="w-8 h-8 text-gray-500" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            网络连接中断
          </CardTitle>
          <CardDescription className="text-gray-600 mt-2">
            当前无法连接到互联网，请检查您的网络设置
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 离线提示信息 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">离线模式说明</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 您可以继续浏览已缓存的页面</li>
              <li>• 部分功能在离线状态下不可用</li>
              <li>• 恢复网络后数据将自动同步</li>
            </ul>
          </div>
          
          {/* 可用的离线功能 */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-900 mb-2">离线可用功能</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• 查看已缓存的营销活动</li>
              <li>• 浏览历史生成内容</li>
              <li>• 查看离线数据报告</li>
              <li>• 编辑草稿内容</li>
            </ul>
          </div>
          
          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button 
              onClick={() => window.location.reload()} 
              className="w-full"
              size="lg"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              重新连接
            </Button>
            
            <div className="grid grid-cols-2 gap-3">
              <Button 
                variant="outline" 
                onClick={() => window.history.back()}
                className="w-full"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回上页
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/dashboard'}
                className="w-full"
              >
                <Home className="w-4 h-4 mr-2" />
                回到首页
              </Button>
            </div>
          </div>
          
          {/* 网络状态检测 */}
          <div className="text-center">
            <div id="network-status" className="text-sm text-gray-500">
              正在检测网络状态...
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* 网络状态检测脚本 */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // 网络状态检测
            function updateNetworkStatus() {
              const statusElement = document.getElementById('network-status');
              if (navigator.onLine) {
                statusElement.textContent = '网络已连接，正在重新加载...';
                statusElement.className = 'text-sm text-green-600';
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              } else {
                statusElement.textContent = '网络连接中断';
                statusElement.className = 'text-sm text-red-600';
              }
            }
            
            // 监听网络状态变化
            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);
            
            // 初始检测
            updateNetworkStatus();
            
            // 定期检测网络状态
            setInterval(() => {
              if (navigator.onLine) {
                fetch('/api/health', { 
                  method: 'HEAD',
                  cache: 'no-cache'
                })
                .then(() => {
                  const statusElement = document.getElementById('network-status');
                  statusElement.textContent = '网络已恢复，正在重新加载...';
                  statusElement.className = 'text-sm text-green-600';
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                })
                .catch(() => {
                  // 网络仍然不可用
                });
              }
            }, 5000);
          `,
        }}
      />
    </div>
  )
}

/**
 * 离线页面的静态生成配置
 * 确保离线页面可以被预渲染和缓存
 */
export const dynamic = 'force-static'
export const revalidate = false
