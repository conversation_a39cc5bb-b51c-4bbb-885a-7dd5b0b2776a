import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, Bot, BarChart3, Zap, Users, Target, Sparkles } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* 导航栏 */}
      <nav className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold gradient-text">AI数字营销平台</span>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <Link href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                功能特色
              </Link>
              <Link href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                价格方案
              </Link>
              <Link href="#about" className="text-gray-600 hover:text-gray-900 transition-colors">
                关于我们
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/login">
                <Button variant="ghost">登录</Button>
              </Link>
              <Link href="/auth/register">
                <Button className="btn-animate">
                  免费试用
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 英雄区域 */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
              <span className="gradient-text">AI驱动的</span>
              <br />
              智能数字营销平台
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              利用人工智能技术，自动生成营销内容，精准分析用户画像，
              实现营销活动自动化，让您的营销效果提升10倍
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="lg" className="btn-animate btn-glow">
                  <Sparkles className="w-5 h-5 mr-2" />
                  立即开始免费试用
                </Button>
              </Link>
              <Link href="#demo">
                <Button size="lg" variant="outline" className="btn-animate">
                  观看产品演示
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 功能特色 */}
      <section id="features" className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">强大的功能特色</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              集成最新AI技术，为您提供全方位的数字营销解决方案
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* AI内容生成 */}
            <Card className="card-shadow hover:scale-105 transition-transform duration-200">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Bot className="w-6 h-6 text-blue-600" />
                </div>
                <CardTitle>AI内容生成</CardTitle>
                <CardDescription>
                  智能生成营销文案、图片和视频内容，节省90%的内容创作时间
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 多种文案风格选择</li>
                  <li>• 自动图片生成和编辑</li>
                  <li>• 视频内容制作</li>
                  <li>• 内容质量评估</li>
                </ul>
              </CardContent>
            </Card>

            {/* 用户画像分析 */}
            <Card className="card-shadow hover:scale-105 transition-transform duration-200">
              <CardHeader>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <CardTitle>用户画像分析</CardTitle>
                <CardDescription>
                  深度分析用户行为数据，构建精准的用户画像，提升营销转化率
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 智能用户分群</li>
                  <li>• 行为预测分析</li>
                  <li>• 兴趣偏好识别</li>
                  <li>• 个性化推荐</li>
                </ul>
              </CardContent>
            </Card>

            {/* 营销自动化 */}
            <Card className="card-shadow hover:scale-105 transition-transform duration-200">
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="w-6 h-6 text-purple-600" />
                </div>
                <CardTitle>营销自动化</CardTitle>
                <CardDescription>
                  设计智能营销流程，自动执行营销活动，提高营销效率
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 多渠道营销管理</li>
                  <li>• 触发式营销流程</li>
                  <li>• A/B测试优化</li>
                  <li>• 营销漏斗分析</li>
                </ul>
              </CardContent>
            </Card>

            {/* 数据分析报告 */}
            <Card className="card-shadow hover:scale-105 transition-transform duration-200">
              <CardHeader>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                  <BarChart3 className="w-6 h-6 text-orange-600" />
                </div>
                <CardTitle>数据分析报告</CardTitle>
                <CardDescription>
                  实时监控营销效果，生成详细的数据分析报告，优化营销策略
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 实时数据监控</li>
                  <li>• ROI分析计算</li>
                  <li>• 自动报告生成</li>
                  <li>• 趋势预测分析</li>
                </ul>
              </CardContent>
            </Card>

            {/* 精准投放 */}
            <Card className="card-shadow hover:scale-105 transition-transform duration-200">
              <CardHeader>
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                  <Target className="w-6 h-6 text-red-600" />
                </div>
                <CardTitle>精准投放</CardTitle>
                <CardDescription>
                  基于AI算法优化广告投放策略，提高广告投放效果和ROI
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 智能受众定位</li>
                  <li>• 预算优化分配</li>
                  <li>• 投放时机优化</li>
                  <li>• 效果实时调整</li>
                </ul>
              </CardContent>
            </Card>

            {/* 多平台集成 */}
            <Card className="card-shadow hover:scale-105 transition-transform duration-200">
              <CardHeader>
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                  <Sparkles className="w-6 h-6 text-indigo-600" />
                </div>
                <CardTitle>多平台集成</CardTitle>
                <CardDescription>
                  无缝集成主流营销平台和工具，统一管理所有营销渠道
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 社交媒体平台</li>
                  <li>• 邮件营销工具</li>
                  <li>• CRM系统集成</li>
                  <li>• 电商平台对接</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 数据统计 */}
      <section className="py-20 px-4 gradient-bg text-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">用数据说话</h2>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              已有数千家企业选择我们的AI数字营销平台，取得了显著的营销效果提升
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">10,000+</div>
              <div className="text-lg opacity-90">活跃用户</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">500%</div>
              <div className="text-lg opacity-90">平均ROI提升</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">90%</div>
              <div className="text-lg opacity-90">时间节省</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">99.9%</div>
              <div className="text-lg opacity-90">系统可用性</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA区域 */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold mb-6">准备开始您的AI营销之旅？</h2>
            <p className="text-xl text-gray-600 mb-8">
              立即注册，免费试用30天，体验AI驱动的数字营销平台带来的强大功能
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="lg" className="btn-animate btn-glow">
                  <Sparkles className="w-5 h-5 mr-2" />
                  立即免费试用
                </Button>
              </Link>
              <Link href="/contact">
                <Button size="lg" variant="outline" className="btn-animate">
                  联系销售团队
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">AI数字营销平台</span>
              </div>
              <p className="text-gray-400">
                基于AI技术的智能数字营销解决方案，助力企业实现营销效果最大化。
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">产品功能</h3>
              <ul className="space-y-2 text-gray-400">
                <li>AI内容生成</li>
                <li>用户画像分析</li>
                <li>营销自动化</li>
                <li>数据分析报告</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">解决方案</h3>
              <ul className="space-y-2 text-gray-400">
                <li>中小企业营销</li>
                <li>电商营销</li>
                <li>B2B营销</li>
                <li>品牌营销</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">联系我们</h3>
              <ul className="space-y-2 text-gray-400">
                <li>客服热线：400-123-4567</li>
                <li>邮箱：<EMAIL></li>
                <li>地址：北京市朝阳区xxx大厦</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 AI数字营销平台. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
