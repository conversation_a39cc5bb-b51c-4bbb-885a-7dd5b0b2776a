import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'AI数字营销平台',
    template: '%s | AI数字营销平台',
  },
  description: '基于AI技术的智能数字营销解决方案，提供内容生成、用户画像分析、营销自动化等功能',
  keywords: [
    'AI营销',
    '数字营销',
    '内容生成',
    '用户画像',
    '营销自动化',
    '数据分析',
  ],
  authors: [
    {
      name: 'AI数字营销团队',
    },
  ],
  creator: 'AI数字营销团队',
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: 'https://ai-marketing.example.com',
    title: 'AI数字营销平台',
    description: '基于AI技术的智能数字营销解决方案',
    siteName: 'AI数字营销平台',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'AI数字营销平台',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI数字营销平台',
    description: '基于AI技术的智能数字营销解决方案',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
