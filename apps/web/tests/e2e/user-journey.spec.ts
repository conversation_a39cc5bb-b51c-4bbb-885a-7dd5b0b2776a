// 端到端测试 - 用户完整流程测试
// 测试用户从注册到使用平台核心功能的完整流程

import { test, expect, Page } from '@playwright/test'

// 测试数据
const testUser = {
  email: `test-${Date.now()}@example.com`,
  password: 'Test123456!',
  firstName: '测试',
  lastName: '用户',
  company: '测试公司'
}

/**
 * 用户注册流程测试
 */
test.describe('用户注册流程', () => {
  test('用户可以成功注册账户', async ({ page }) => {
    // 访问注册页面
    await page.goto('/auth/register')
    
    // 验证页面标题
    await expect(page).toHaveTitle(/注册/)
    
    // 填写注册表单
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.fill('[data-testid="confirm-password-input"]', testUser.password)
    await page.fill('[data-testid="first-name-input"]', testUser.firstName)
    await page.fill('[data-testid="last-name-input"]', testUser.lastName)
    
    // 同意服务条款
    await page.check('[data-testid="terms-checkbox"]')
    
    // 提交注册
    await page.click('[data-testid="register-button"]')
    
    // 验证注册成功
    await expect(page).toHaveURL(/\/dashboard/)
    await expect(page.locator('[data-testid="welcome-message"]')).toBeVisible()
  })

  test('注册表单验证正常工作', async ({ page }) => {
    await page.goto('/auth/register')
    
    // 测试邮箱验证
    await page.fill('[data-testid="email-input"]', 'invalid-email')
    await page.blur('[data-testid="email-input"]')
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible()
    
    // 测试密码强度验证
    await page.fill('[data-testid="password-input"]', '123')
    await page.blur('[data-testid="password-input"]')
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible()
    
    // 测试密码确认验证
    await page.fill('[data-testid="password-input"]', 'Test123456!')
    await page.fill('[data-testid="confirm-password-input"]', 'different')
    await page.blur('[data-testid="confirm-password-input"]')
    await expect(page.locator('[data-testid="confirm-password-error"]')).toBeVisible()
  })
})

/**
 * 用户登录流程测试
 */
test.describe('用户登录流程', () => {
  test.beforeEach(async ({ page }) => {
    // 先注册用户
    await registerUser(page, testUser)
    // 登出
    await page.click('[data-testid="user-menu"]')
    await page.click('[data-testid="logout-button"]')
  })

  test('用户可以成功登录', async ({ page }) => {
    await page.goto('/auth/login')
    
    // 填写登录表单
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', testUser.password)
    
    // 提交登录
    await page.click('[data-testid="login-button"]')
    
    // 验证登录成功
    await expect(page).toHaveURL(/\/dashboard/)
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
  })

  test('错误凭据显示错误信息', async ({ page }) => {
    await page.goto('/auth/login')
    
    // 使用错误密码
    await page.fill('[data-testid="email-input"]', testUser.email)
    await page.fill('[data-testid="password-input"]', 'wrongpassword')
    await page.click('[data-testid="login-button"]')
    
    // 验证错误信息
    await expect(page.locator('[data-testid="login-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="login-error"]')).toContainText('凭据无效')
  })
})

/**
 * AI内容生成流程测试
 */
test.describe('AI内容生成流程', () => {
  test.beforeEach(async ({ page }) => {
    await registerUser(page, testUser)
  })

  test('用户可以生成AI文本内容', async ({ page }) => {
    // 导航到AI生成页面
    await page.click('[data-testid="nav-ai-generation"]')
    await expect(page).toHaveURL(/\/ai\/generate/)
    
    // 选择文本生成
    await page.click('[data-testid="text-generation-tab"]')
    
    // 填写生成参数
    await page.fill('[data-testid="prompt-input"]', '写一篇关于AI营销的文章')
    await page.selectOption('[data-testid="content-type-select"]', 'article')
    await page.selectOption('[data-testid="tone-select"]', 'professional')
    
    // 开始生成
    await page.click('[data-testid="generate-button"]')
    
    // 等待生成完成
    await expect(page.locator('[data-testid="generation-loading"]')).toBeVisible()
    await expect(page.locator('[data-testid="generation-result"]')).toBeVisible({ timeout: 30000 })
    
    // 验证结果
    await expect(page.locator('[data-testid="generated-content"]')).not.toBeEmpty()
    
    // 保存生成结果
    await page.click('[data-testid="save-generation-button"]')
    await expect(page.locator('[data-testid="save-success-message"]')).toBeVisible()
  })

  test('用户可以查看生成历史', async ({ page }) => {
    // 先生成一些内容
    await generateTestContent(page)
    
    // 导航到历史页面
    await page.click('[data-testid="nav-ai-history"]')
    await expect(page).toHaveURL(/\/ai\/history/)
    
    // 验证历史记录显示
    await expect(page.locator('[data-testid="history-item"]')).toHaveCount.greaterThan(0)
    
    // 测试筛选功能
    await page.selectOption('[data-testid="type-filter"]', 'TEXT_GENERATION')
    await expect(page.locator('[data-testid="history-item"]')).toBeVisible()
    
    // 测试收藏功能
    await page.click('[data-testid="favorite-button"]:first-child')
    await expect(page.locator('[data-testid="favorite-icon"]:first-child')).toBeVisible()
  })
})

/**
 * 营销活动管理流程测试
 */
test.describe('营销活动管理流程', () => {
  test.beforeEach(async ({ page }) => {
    await registerUser(page, testUser)
  })

  test('用户可以创建营销活动', async ({ page }) => {
    // 导航到营销活动页面
    await page.click('[data-testid="nav-campaigns"]')
    await expect(page).toHaveURL(/\/campaigns/)
    
    // 创建新活动
    await page.click('[data-testid="create-campaign-button"]')
    
    // 填写活动信息
    await page.fill('[data-testid="campaign-name-input"]', '测试营销活动')
    await page.fill('[data-testid="campaign-description-input"]', '这是一个测试活动')
    await page.selectOption('[data-testid="campaign-type-select"]', 'email')
    
    // 设置目标受众
    await page.click('[data-testid="audience-tab"]')
    await page.fill('[data-testid="audience-name-input"]', '测试受众')
    
    // 设置预算
    await page.click('[data-testid="budget-tab"]')
    await page.fill('[data-testid="budget-amount-input"]', '1000')
    
    // 保存活动
    await page.click('[data-testid="save-campaign-button"]')
    
    // 验证活动创建成功
    await expect(page.locator('[data-testid="campaign-success-message"]')).toBeVisible()
    await expect(page).toHaveURL(/\/campaigns\/\w+/)
  })

  test('用户可以查看活动列表和详情', async ({ page }) => {
    // 先创建一个活动
    await createTestCampaign(page)
    
    // 返回活动列表
    await page.click('[data-testid="nav-campaigns"]')
    
    // 验证活动显示在列表中
    await expect(page.locator('[data-testid="campaign-item"]')).toHaveCount.greaterThan(0)
    
    // 点击查看详情
    await page.click('[data-testid="campaign-item"]:first-child')
    
    // 验证详情页面
    await expect(page.locator('[data-testid="campaign-details"]')).toBeVisible()
    await expect(page.locator('[data-testid="campaign-stats"]')).toBeVisible()
  })
})

/**
 * 订阅管理流程测试
 */
test.describe('订阅管理流程', () => {
  test.beforeEach(async ({ page }) => {
    await registerUser(page, testUser)
  })

  test('用户可以查看订阅计划', async ({ page }) => {
    // 导航到订阅页面
    await page.click('[data-testid="nav-subscription"]')
    await expect(page).toHaveURL(/\/subscription/)
    
    // 验证计划显示
    await expect(page.locator('[data-testid="plan-card"]')).toHaveCount.greaterThan(0)
    
    // 验证当前计划显示
    await expect(page.locator('[data-testid="current-plan"]')).toBeVisible()
    
    // 验证使用情况显示
    await page.click('[data-testid="usage-tab"]')
    await expect(page.locator('[data-testid="usage-chart"]')).toBeVisible()
  })

  test('用户可以升级订阅计划', async ({ page }) => {
    await page.goto('/subscription')
    
    // 选择升级计划
    await page.click('[data-testid="plans-tab"]')
    await page.click('[data-testid="upgrade-button"]:first-child')
    
    // 验证支付页面
    await expect(page.locator('[data-testid="payment-form"]')).toBeVisible()
    
    // 注意：这里不实际完成支付，只测试到支付页面
    await expect(page.locator('[data-testid="stripe-elements"]')).toBeVisible()
  })
})

/**
 * 响应式设计测试
 */
test.describe('响应式设计测试', () => {
  test.beforeEach(async ({ page }) => {
    await registerUser(page, testUser)
  })

  test('移动端界面正常显示', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })
    
    // 测试主要页面
    await page.goto('/dashboard')
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible()
    
    // 测试移动端导航
    await page.click('[data-testid="mobile-menu-button"]')
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible()
    
    // 测试AI生成页面
    await page.goto('/ai/generate')
    await expect(page.locator('[data-testid="generation-form"]')).toBeVisible()
  })

  test('平板端界面正常显示', async ({ page }) => {
    // 设置平板端视口
    await page.setViewportSize({ width: 768, height: 1024 })
    
    await page.goto('/dashboard')
    await expect(page.locator('[data-testid="dashboard-grid"]')).toBeVisible()
    
    // 验证侧边栏在平板端的显示
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
  })
})

/**
 * 辅助函数
 */
async function registerUser(page: Page, user: typeof testUser) {
  await page.goto('/auth/register')
  await page.fill('[data-testid="email-input"]', user.email)
  await page.fill('[data-testid="password-input"]', user.password)
  await page.fill('[data-testid="confirm-password-input"]', user.password)
  await page.fill('[data-testid="first-name-input"]', user.firstName)
  await page.fill('[data-testid="last-name-input"]', user.lastName)
  await page.check('[data-testid="terms-checkbox"]')
  await page.click('[data-testid="register-button"]')
  await expect(page).toHaveURL(/\/dashboard/)
}

async function generateTestContent(page: Page) {
  await page.goto('/ai/generate')
  await page.click('[data-testid="text-generation-tab"]')
  await page.fill('[data-testid="prompt-input"]', '测试内容生成')
  await page.click('[data-testid="generate-button"]')
  await expect(page.locator('[data-testid="generation-result"]')).toBeVisible({ timeout: 30000 })
  await page.click('[data-testid="save-generation-button"]')
}

async function createTestCampaign(page: Page) {
  await page.goto('/campaigns')
  await page.click('[data-testid="create-campaign-button"]')
  await page.fill('[data-testid="campaign-name-input"]', '测试活动')
  await page.fill('[data-testid="campaign-description-input"]', '测试描述')
  await page.selectOption('[data-testid="campaign-type-select"]', 'email')
  await page.click('[data-testid="save-campaign-button"]')
  await expect(page.locator('[data-testid="campaign-success-message"]')).toBeVisible()
}
