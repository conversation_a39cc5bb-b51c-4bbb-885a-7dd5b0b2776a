import Fastify from 'fastify'
import { config } from './config'
import { logger } from './utils/logger'
import { registerPlugins } from './plugins'
import { registerRoutes } from './routes'
import { prisma } from './utils/database'

/**
 * 创建Fastify应用实例
 * 配置日志记录器和基础设置
 */
const app = Fastify({
  logger: logger,
  // 信任代理，用于获取真实IP地址
  trustProxy: true,
  // 请求体大小限制
  bodyLimit: 10 * 1024 * 1024, // 10MB
  // 请求超时时间
  connectionTimeout: 30000, // 30秒
  // 保持连接超时时间
  keepAliveTimeout: 5000, // 5秒
})

/**
 * 应用启动函数
 * 注册插件、路由并启动服务器
 */
async function start() {
  try {
    // 注册插件
    await registerPlugins(app)
    
    // 注册路由
    await registerRoutes(app)
    
    // 测试数据库连接
    await prisma.$connect()
    logger.info('数据库连接成功')
    
    // 启动服务器
    const address = await app.listen({
      port: config.port,
      host: config.host,
    })
    
    logger.info(`🚀 服务器启动成功，监听地址: ${address}`)
    logger.info(`📚 API文档地址: http://${config.host}:${config.port}/docs`)
    logger.info(`🔍 健康检查地址: http://${config.host}:${config.port}/health`)
    
  } catch (error) {
    logger.error('服务器启动失败:', error)
    process.exit(1)
  }
}

/**
 * 优雅关闭处理
 * 处理SIGTERM和SIGINT信号
 */
async function gracefulShutdown(signal: string) {
  logger.info(`收到${signal}信号，开始优雅关闭服务器...`)
  
  try {
    // 关闭Fastify服务器
    await app.close()
    logger.info('Fastify服务器已关闭')
    
    // 关闭数据库连接
    await prisma.$disconnect()
    logger.info('数据库连接已关闭')
    
    logger.info('服务器优雅关闭完成')
    process.exit(0)
  } catch (error) {
    logger.error('服务器关闭过程中发生错误:', error)
    process.exit(1)
  }
}

// 注册信号处理器
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
process.on('SIGINT', () => gracefulShutdown('SIGINT'))

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error)
  process.exit(1)
})

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', { reason, promise })
  process.exit(1)
})

// 启动应用
start()
