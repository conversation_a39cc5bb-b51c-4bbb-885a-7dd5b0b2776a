// 订阅管理服务
// 处理订阅计划、用户订阅状态、升级降级等功能

import { prisma } from '../utils/database'
import { logger } from '../utils/logger'
import { ApiError, ErrorCodes } from '../utils/response'
import { PaymentService } from './payment.service'

/**
 * 订阅计划配置
 */
export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
  limits: {
    aiGenerations: number
    emailSends: number
    campaigns: number
    automations: number
    reports: number
    storage: number // GB
  }
  stripePriceId: string
  isPopular?: boolean
  isActive: boolean
}

/**
 * 订阅状态
 */
export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  CANCELED = 'CANCELED',
  PAST_DUE = 'PAST_DUE',
  UNPAID = 'UNPAID',
  TRIALING = 'TRIALING'
}

/**
 * 订阅升级/降级请求
 */
export interface ChangeSubscriptionRequest {
  userId: string
  newPlanId: string
  paymentMethodId?: string
}

/**
 * 订阅管理服务类
 */
export class SubscriptionService {
  /**
   * 预定义的订阅计划
   */
  private static readonly PLANS: SubscriptionPlan[] = [
    {
      id: 'free',
      name: '免费版',
      description: '适合个人用户和小团队',
      price: 0,
      currency: 'CNY',
      interval: 'month',
      features: [
        '基础AI文案生成',
        '简单邮件营销',
        '基础数据分析',
        '社区支持'
      ],
      limits: {
        aiGenerations: 10,
        emailSends: 100,
        campaigns: 1,
        automations: 1,
        reports: 3,
        storage: 1
      },
      stripePriceId: '',
      isActive: true
    },
    {
      id: 'basic',
      name: '基础版',
      description: '适合小型企业',
      price: 99,
      currency: 'CNY',
      interval: 'month',
      features: [
        '无限AI文案生成',
        '高级邮件营销',
        '营销自动化',
        '详细数据分析',
        '邮件支持'
      ],
      limits: {
        aiGenerations: 1000,
        emailSends: 5000,
        campaigns: 10,
        automations: 5,
        reports: 20,
        storage: 10
      },
      stripePriceId: 'price_basic_monthly',
      isActive: true
    },
    {
      id: 'pro',
      name: '专业版',
      description: '适合成长型企业',
      price: 299,
      currency: 'CNY',
      interval: 'month',
      features: [
        '无限AI内容生成',
        '高级营销自动化',
        '深度数据分析',
        '自定义报告',
        '优先支持',
        'API访问'
      ],
      limits: {
        aiGenerations: -1, // 无限制
        emailSends: 50000,
        campaigns: 100,
        automations: 50,
        reports: -1,
        storage: 100
      },
      stripePriceId: 'price_pro_monthly',
      isPopular: true,
      isActive: true
    },
    {
      id: 'enterprise',
      name: '企业版',
      description: '适合大型企业',
      price: 999,
      currency: 'CNY',
      interval: 'month',
      features: [
        '所有专业版功能',
        '白标解决方案',
        '专属客户经理',
        '定制开发',
        '私有部署',
        'SLA保障'
      ],
      limits: {
        aiGenerations: -1,
        emailSends: -1,
        campaigns: -1,
        automations: -1,
        reports: -1,
        storage: -1
      },
      stripePriceId: 'price_enterprise_monthly',
      isActive: true
    }
  ]

  /**
   * 获取所有订阅计划
   */
  static getPlans(): SubscriptionPlan[] {
    return this.PLANS.filter(plan => plan.isActive)
  }

  /**
   * 根据ID获取订阅计划
   */
  static getPlanById(planId: string): SubscriptionPlan | null {
    return this.PLANS.find(plan => plan.id === planId && plan.isActive) || null
  }

  /**
   * 获取用户当前订阅
   */
  static async getUserSubscription(userId: string): Promise<{
    subscription: any
    plan: SubscriptionPlan
    usage: any
  } | null> {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId }
      })

      if (!subscription) {
        // 返回免费计划
        const freePlan = this.getPlanById('free')!
        return {
          subscription: null,
          plan: freePlan,
          usage: await this.getUserUsage(userId)
        }
      }

      const plan = this.getPlanById(subscription.planId)
      if (!plan) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '订阅计划不存在')
      }

      const usage = await this.getUserUsage(userId)

      return {
        subscription,
        plan,
        usage
      }
    } catch (error) {
      logger.error('获取用户订阅失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取订阅信息失败')
    }
  }

  /**
   * 创建订阅
   */
  static async createSubscription(
    userId: string,
    planId: string,
    paymentMethodId: string,
    trialDays?: number
  ): Promise<any> {
    try {
      const plan = this.getPlanById(planId)
      if (!plan) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '订阅计划不存在')
      }

      if (plan.id === 'free') {
        throw new ApiError(ErrorCodes.BAD_REQUEST, '免费计划无需创建订阅')
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId }
      })

      if (!user) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '用户不存在')
      }

      // 创建或获取Stripe客户
      const customer = await PaymentService.createOrGetCustomer(
        userId,
        user.email,
        `${user.firstName} ${user.lastName}`
      )

      // 添加支付方法
      await PaymentService.attachPaymentMethod(paymentMethodId, customer.id)

      // 创建Stripe订阅
      const stripeSubscription = await PaymentService.createSubscription({
        customerId: customer.id,
        priceId: plan.stripePriceId,
        paymentMethodId,
        trialDays
      })

      // 保存订阅信息到数据库
      const subscription = await prisma.subscription.upsert({
        where: { userId },
        update: {
          planId: plan.id,
          status: stripeSubscription.status.toUpperCase() as SubscriptionStatus,
          currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
          currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
          trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
          trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
          metadata: {
            stripeSubscriptionId: stripeSubscription.id,
            stripeCustomerId: customer.id
          }
        },
        create: {
          userId,
          planId: plan.id,
          status: stripeSubscription.status.toUpperCase() as SubscriptionStatus,
          currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
          currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
          trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
          trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
          metadata: {
            stripeSubscriptionId: stripeSubscription.id,
            stripeCustomerId: customer.id
          }
        }
      })

      logger.info('创建订阅成功', {
        userId,
        planId,
        subscriptionId: subscription.id
      })

      return {
        subscription,
        plan,
        stripeSubscription
      }
    } catch (error) {
      logger.error('创建订阅失败', {
        userId,
        planId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '创建订阅失败')
    }
  }

  /**
   * 更改订阅计划
   */
  static async changeSubscription(
    request: ChangeSubscriptionRequest
  ): Promise<any> {
    try {
      const { userId, newPlanId, paymentMethodId } = request

      const newPlan = this.getPlanById(newPlanId)
      if (!newPlan) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '新订阅计划不存在')
      }

      // 获取当前订阅
      const currentSubscription = await prisma.subscription.findUnique({
        where: { userId }
      })

      if (!currentSubscription) {
        // 如果没有订阅，创建新订阅
        if (newPlan.id === 'free') {
          throw new ApiError(ErrorCodes.BAD_REQUEST, '已经是免费计划')
        }

        if (!paymentMethodId) {
          throw new ApiError(ErrorCodes.BAD_REQUEST, '创建付费订阅需要支付方法')
        }

        return await this.createSubscription(userId, newPlanId, paymentMethodId)
      }

      // 如果要降级到免费计划
      if (newPlan.id === 'free') {
        return await this.cancelSubscription(userId, false)
      }

      // 更新Stripe订阅
      const stripeSubscriptionId = currentSubscription.metadata?.stripeSubscriptionId as string
      if (!stripeSubscriptionId) {
        throw new ApiError(ErrorCodes.INTERNAL_ERROR, 'Stripe订阅ID不存在')
      }

      const updatedStripeSubscription = await PaymentService.updateSubscription(
        stripeSubscriptionId,
        newPlan.stripePriceId
      )

      // 更新数据库中的订阅信息
      const updatedSubscription = await prisma.subscription.update({
        where: { userId },
        data: {
          planId: newPlan.id,
          status: updatedStripeSubscription.status.toUpperCase() as SubscriptionStatus,
          currentPeriodStart: new Date(updatedStripeSubscription.current_period_start * 1000),
          currentPeriodEnd: new Date(updatedStripeSubscription.current_period_end * 1000),
          cancelAtPeriodEnd: updatedStripeSubscription.cancel_at_period_end
        }
      })

      logger.info('更改订阅成功', {
        userId,
        oldPlanId: currentSubscription.planId,
        newPlanId
      })

      return {
        subscription: updatedSubscription,
        plan: newPlan,
        stripeSubscription: updatedStripeSubscription
      }
    } catch (error) {
      logger.error('更改订阅失败', {
        userId: request.userId,
        newPlanId: request.newPlanId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '更改订阅失败')
    }
  }

  /**
   * 取消订阅
   */
  static async cancelSubscription(
    userId: string,
    immediately: boolean = false
  ): Promise<any> {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId }
      })

      if (!subscription) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '订阅不存在')
      }

      const stripeSubscriptionId = subscription.metadata?.stripeSubscriptionId as string
      if (stripeSubscriptionId) {
        // 取消Stripe订阅
        await PaymentService.cancelSubscription(stripeSubscriptionId, immediately)
      }

      // 更新数据库
      const updatedSubscription = await prisma.subscription.update({
        where: { userId },
        data: {
          status: immediately ? SubscriptionStatus.CANCELED : subscription.status,
          cancelAtPeriodEnd: !immediately
        }
      })

      logger.info('取消订阅成功', {
        userId,
        immediately,
        subscriptionId: subscription.id
      })

      return {
        subscription: updatedSubscription,
        plan: this.getPlanById('free')!
      }
    } catch (error) {
      logger.error('取消订阅失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '取消订阅失败')
    }
  }

  /**
   * 恢复订阅
   */
  static async resumeSubscription(userId: string): Promise<any> {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId }
      })

      if (!subscription) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '订阅不存在')
      }

      if (!subscription.cancelAtPeriodEnd) {
        throw new ApiError(ErrorCodes.BAD_REQUEST, '订阅未设置为取消')
      }

      const stripeSubscriptionId = subscription.metadata?.stripeSubscriptionId as string
      if (stripeSubscriptionId) {
        // 恢复Stripe订阅
        await PaymentService.resumeSubscription(stripeSubscriptionId)
      }

      // 更新数据库
      const updatedSubscription = await prisma.subscription.update({
        where: { userId },
        data: {
          cancelAtPeriodEnd: false
        }
      })

      logger.info('恢复订阅成功', {
        userId,
        subscriptionId: subscription.id
      })

      const plan = this.getPlanById(subscription.planId)!

      return {
        subscription: updatedSubscription,
        plan
      }
    } catch (error) {
      logger.error('恢复订阅失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '恢复订阅失败')
    }
  }

  /**
   * 检查用户是否有权限使用某个功能
   */
  static async checkFeatureAccess(
    userId: string,
    feature: keyof SubscriptionPlan['limits']
  ): Promise<boolean> {
    try {
      const userSubscription = await this.getUserSubscription(userId)
      if (!userSubscription) {
        return false
      }

      const { plan, usage } = userSubscription
      const limit = plan.limits[feature]

      // -1 表示无限制
      if (limit === -1) {
        return true
      }

      // 检查当前使用量
      const currentUsage = usage[feature] || 0
      return currentUsage < limit
    } catch (error) {
      logger.error('检查功能权限失败', {
        userId,
        feature,
        error: error instanceof Error ? error.message : String(error)
      })
      return false
    }
  }

  /**
   * 获取用户使用情况
   */
  static async getUserUsage(userId: string): Promise<any> {
    try {
      const now = new Date()
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

      // 获取本月的使用统计
      const [
        aiGenerations,
        emailSends,
        campaigns,
        automations,
        reports
      ] = await Promise.all([
        // AI生成次数
        prisma.aIGenerationHistory.count({
          where: {
            userId,
            createdAt: { gte: startOfMonth },
            deletedAt: null
          }
        }),
        // 邮件发送数量
        prisma.emailSend.count({
          where: {
            userId,
            createdAt: { gte: startOfMonth }
          }
        }),
        // 活动数量
        prisma.campaign.count({
          where: {
            userId,
            createdAt: { gte: startOfMonth },
            deletedAt: null
          }
        }),
        // 自动化数量
        prisma.automationWorkflow.count({
          where: {
            userId,
            createdAt: { gte: startOfMonth },
            deletedAt: null
          }
        }),
        // 报告数量
        prisma.report.count({
          where: {
            userId,
            createdAt: { gte: startOfMonth }
          }
        })
      ])

      return {
        aiGenerations,
        emailSends,
        campaigns,
        automations,
        reports,
        storage: 0 // TODO: 实现存储使用量计算
      }
    } catch (error) {
      logger.error('获取用户使用情况失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      return {
        aiGenerations: 0,
        emailSends: 0,
        campaigns: 0,
        automations: 0,
        reports: 0,
        storage: 0
      }
    }
  }

  /**
   * 获取订阅统计信息
   */
  static async getSubscriptionStats(): Promise<any> {
    try {
      const stats = await prisma.subscription.groupBy({
        by: ['planId', 'status'],
        _count: {
          id: true
        }
      })

      const totalRevenue = await prisma.subscription.findMany({
        where: {
          status: SubscriptionStatus.ACTIVE
        },
        select: {
          planId: true
        }
      })

      // 计算月收入
      const monthlyRevenue = totalRevenue.reduce((total, sub) => {
        const plan = this.getPlanById(sub.planId)
        return total + (plan?.price || 0)
      }, 0)

      return {
        stats,
        monthlyRevenue,
        totalActiveSubscriptions: totalRevenue.length
      }
    } catch (error) {
      logger.error('获取订阅统计失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取统计信息失败')
    }
  }
}
