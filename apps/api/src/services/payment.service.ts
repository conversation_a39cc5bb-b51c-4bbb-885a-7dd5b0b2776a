// Stripe支付服务
// 处理支付、订阅、账单等相关功能

import Stripe from 'stripe'
import { prisma } from '../utils/database'
import { logger } from '../utils/logger'
import { ApiError, ErrorCodes } from '../utils/response'

/**
 * 支付方法类型
 */
export enum PaymentMethodType {
  CARD = 'card',
  ALIPAY = 'alipay',
  WECHAT_PAY = 'wechat_pay'
}

/**
 * 订阅计划类型
 */
export enum SubscriptionPlan {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

/**
 * 支付状态
 */
export enum PaymentStatus {
  PENDING = 'pending',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  CANCELED = 'canceled',
  REFUNDED = 'refunded'
}

/**
 * 创建支付意图请求
 */
export interface CreatePaymentIntentRequest {
  amount: number
  currency: string
  paymentMethodType: PaymentMethodType
  customerId?: string
  metadata?: Record<string, string>
}

/**
 * 创建订阅请求
 */
export interface CreateSubscriptionRequest {
  customerId: string
  priceId: string
  paymentMethodId: string
  trialDays?: number
}

/**
 * Stripe支付服务类
 */
export class PaymentService {
  private static stripe: Stripe

  /**
   * 初始化Stripe客户端
   */
  static initialize() {
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY环境变量未设置')
    }

    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16',
      typescript: true
    })

    logger.info('Stripe支付服务初始化完成')
  }

  /**
   * 创建或获取Stripe客户
   */
  static async createOrGetCustomer(
    userId: string,
    email: string,
    name?: string
  ): Promise<Stripe.Customer> {
    try {
      // 检查用户是否已有Stripe客户ID
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { stripeCustomerId: true }
      })

      if (user?.stripeCustomerId) {
        // 获取现有客户
        const customer = await this.stripe.customers.retrieve(
          user.stripeCustomerId
        ) as Stripe.Customer

        if (!customer.deleted) {
          return customer
        }
      }

      // 创建新客户
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          userId
        }
      })

      // 更新用户记录
      await prisma.user.update({
        where: { id: userId },
        data: { stripeCustomerId: customer.id }
      })

      logger.info('创建Stripe客户成功', {
        userId,
        customerId: customer.id
      })

      return customer
    } catch (error) {
      logger.error('创建Stripe客户失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建客户失败')
    }
  }

  /**
   * 创建支付意图
   */
  static async createPaymentIntent(
    request: CreatePaymentIntentRequest
  ): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(request.amount * 100), // 转换为分
        currency: request.currency,
        customer: request.customerId,
        payment_method_types: [request.paymentMethodType],
        metadata: request.metadata || {},
        automatic_payment_methods: {
          enabled: true
        }
      })

      logger.info('创建支付意图成功', {
        paymentIntentId: paymentIntent.id,
        amount: request.amount,
        currency: request.currency
      })

      return paymentIntent
    } catch (error) {
      logger.error('创建支付意图失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建支付意图失败')
    }
  }

  /**
   * 确认支付
   */
  static async confirmPayment(
    paymentIntentId: string,
    paymentMethodId: string
  ): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(
        paymentIntentId,
        {
          payment_method: paymentMethodId
        }
      )

      logger.info('确认支付成功', {
        paymentIntentId,
        status: paymentIntent.status
      })

      return paymentIntent
    } catch (error) {
      logger.error('确认支付失败', {
        paymentIntentId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '确认支付失败')
    }
  }

  /**
   * 创建订阅
   */
  static async createSubscription(
    request: CreateSubscriptionRequest
  ): Promise<Stripe.Subscription> {
    try {
      const subscriptionData: Stripe.SubscriptionCreateParams = {
        customer: request.customerId,
        items: [{ price: request.priceId }],
        default_payment_method: request.paymentMethodId,
        expand: ['latest_invoice.payment_intent']
      }

      // 添加试用期
      if (request.trialDays && request.trialDays > 0) {
        subscriptionData.trial_period_days = request.trialDays
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionData)

      logger.info('创建订阅成功', {
        subscriptionId: subscription.id,
        customerId: request.customerId,
        priceId: request.priceId
      })

      return subscription
    } catch (error) {
      logger.error('创建订阅失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建订阅失败')
    }
  }

  /**
   * 更新订阅
   */
  static async updateSubscription(
    subscriptionId: string,
    priceId: string
  ): Promise<Stripe.Subscription> {
    try {
      // 获取当前订阅
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId)

      // 更新订阅项目
      const updatedSubscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          items: [
            {
              id: subscription.items.data[0].id,
              price: priceId
            }
          ],
          proration_behavior: 'create_prorations'
        }
      )

      logger.info('更新订阅成功', {
        subscriptionId,
        newPriceId: priceId
      })

      return updatedSubscription
    } catch (error) {
      logger.error('更新订阅失败', {
        subscriptionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '更新订阅失败')
    }
  }

  /**
   * 取消订阅
   */
  static async cancelSubscription(
    subscriptionId: string,
    immediately: boolean = false
  ): Promise<Stripe.Subscription> {
    try {
      let subscription: Stripe.Subscription

      if (immediately) {
        // 立即取消
        subscription = await this.stripe.subscriptions.cancel(subscriptionId)
      } else {
        // 在当前计费周期结束时取消
        subscription = await this.stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true
        })
      }

      logger.info('取消订阅成功', {
        subscriptionId,
        immediately,
        status: subscription.status
      })

      return subscription
    } catch (error) {
      logger.error('取消订阅失败', {
        subscriptionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '取消订阅失败')
    }
  }

  /**
   * 恢复订阅
   */
  static async resumeSubscription(
    subscriptionId: string
  ): Promise<Stripe.Subscription> {
    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false
      })

      logger.info('恢复订阅成功', {
        subscriptionId,
        status: subscription.status
      })

      return subscription
    } catch (error) {
      logger.error('恢复订阅失败', {
        subscriptionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '恢复订阅失败')
    }
  }

  /**
   * 获取客户的支付方法
   */
  static async getPaymentMethods(
    customerId: string,
    type: string = 'card'
  ): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: type as Stripe.PaymentMethodListParams.Type
      })

      return paymentMethods.data
    } catch (error) {
      logger.error('获取支付方法失败', {
        customerId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取支付方法失败')
    }
  }

  /**
   * 添加支付方法
   */
  static async attachPaymentMethod(
    paymentMethodId: string,
    customerId: string
  ): Promise<Stripe.PaymentMethod> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.attach(
        paymentMethodId,
        { customer: customerId }
      )

      logger.info('添加支付方法成功', {
        paymentMethodId,
        customerId
      })

      return paymentMethod
    } catch (error) {
      logger.error('添加支付方法失败', {
        paymentMethodId,
        customerId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '添加支付方法失败')
    }
  }

  /**
   * 删除支付方法
   */
  static async detachPaymentMethod(
    paymentMethodId: string
  ): Promise<Stripe.PaymentMethod> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.detach(
        paymentMethodId
      )

      logger.info('删除支付方法成功', {
        paymentMethodId
      })

      return paymentMethod
    } catch (error) {
      logger.error('删除支付方法失败', {
        paymentMethodId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '删除支付方法失败')
    }
  }

  /**
   * 获取发票
   */
  static async getInvoice(invoiceId: string): Promise<Stripe.Invoice> {
    try {
      const invoice = await this.stripe.invoices.retrieve(invoiceId)
      return invoice
    } catch (error) {
      logger.error('获取发票失败', {
        invoiceId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取发票失败')
    }
  }

  /**
   * 获取客户的发票列表
   */
  static async getCustomerInvoices(
    customerId: string,
    limit: number = 10
  ): Promise<Stripe.Invoice[]> {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit
      })

      return invoices.data
    } catch (error) {
      logger.error('获取客户发票失败', {
        customerId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取发票列表失败')
    }
  }

  /**
   * 创建退款
   */
  static async createRefund(
    paymentIntentId: string,
    amount?: number,
    reason?: string
  ): Promise<Stripe.Refund> {
    try {
      const refundData: Stripe.RefundCreateParams = {
        payment_intent: paymentIntentId
      }

      if (amount) {
        refundData.amount = Math.round(amount * 100) // 转换为分
      }

      if (reason) {
        refundData.reason = reason as Stripe.RefundCreateParams.Reason
      }

      const refund = await this.stripe.refunds.create(refundData)

      logger.info('创建退款成功', {
        refundId: refund.id,
        paymentIntentId,
        amount: refund.amount
      })

      return refund
    } catch (error) {
      logger.error('创建退款失败', {
        paymentIntentId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建退款失败')
    }
  }

  /**
   * 处理Webhook事件
   */
  static async handleWebhook(
    payload: string,
    signature: string
  ): Promise<void> {
    try {
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET
      if (!webhookSecret) {
        throw new Error('STRIPE_WEBHOOK_SECRET环境变量未设置')
      }

      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret
      )

      logger.info('收到Stripe Webhook事件', {
        type: event.type,
        id: event.id
      })

      // 处理不同类型的事件
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent)
          break

        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent)
          break

        case 'customer.subscription.created':
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
          break

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
          break

        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
          break

        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
          break

        default:
          logger.info('未处理的Webhook事件类型', { type: event.type })
      }
    } catch (error) {
      logger.error('处理Webhook失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 处理支付成功事件
   */
  private static async handlePaymentSucceeded(
    paymentIntent: Stripe.PaymentIntent
  ): Promise<void> {
    try {
      // 更新数据库中的支付记录
      // 这里可以根据实际需求实现具体逻辑
      logger.info('支付成功', {
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount
      })
    } catch (error) {
      logger.error('处理支付成功事件失败', { error })
    }
  }

  /**
   * 处理支付失败事件
   */
  private static async handlePaymentFailed(
    paymentIntent: Stripe.PaymentIntent
  ): Promise<void> {
    try {
      logger.info('支付失败', {
        paymentIntentId: paymentIntent.id,
        lastPaymentError: paymentIntent.last_payment_error
      })
    } catch (error) {
      logger.error('处理支付失败事件失败', { error })
    }
  }

  /**
   * 处理订阅更新事件
   */
  private static async handleSubscriptionUpdated(
    subscription: Stripe.Subscription
  ): Promise<void> {
    try {
      const customerId = subscription.customer as string
      
      // 根据customer ID找到用户
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: customerId }
      })

      if (user) {
        // 更新用户的订阅信息
        await prisma.subscription.upsert({
          where: { userId: user.id },
          update: {
            status: subscription.status.toUpperCase() as any,
            currentPeriodStart: new Date(subscription.current_period_start * 1000),
            currentPeriodEnd: new Date(subscription.current_period_end * 1000),
            cancelAtPeriodEnd: subscription.cancel_at_period_end
          },
          create: {
            userId: user.id,
            planId: subscription.items.data[0].price.id,
            status: subscription.status.toUpperCase() as any,
            currentPeriodStart: new Date(subscription.current_period_start * 1000),
            currentPeriodEnd: new Date(subscription.current_period_end * 1000),
            cancelAtPeriodEnd: subscription.cancel_at_period_end
          }
        })

        logger.info('更新订阅信息成功', {
          userId: user.id,
          subscriptionId: subscription.id,
          status: subscription.status
        })
      }
    } catch (error) {
      logger.error('处理订阅更新事件失败', { error })
    }
  }

  /**
   * 处理订阅删除事件
   */
  private static async handleSubscriptionDeleted(
    subscription: Stripe.Subscription
  ): Promise<void> {
    try {
      const customerId = subscription.customer as string
      
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: customerId }
      })

      if (user) {
        await prisma.subscription.update({
          where: { userId: user.id },
          data: {
            status: 'CANCELED'
          }
        })

        logger.info('订阅已取消', {
          userId: user.id,
          subscriptionId: subscription.id
        })
      }
    } catch (error) {
      logger.error('处理订阅删除事件失败', { error })
    }
  }

  /**
   * 处理发票支付成功事件
   */
  private static async handleInvoicePaymentSucceeded(
    invoice: Stripe.Invoice
  ): Promise<void> {
    try {
      logger.info('发票支付成功', {
        invoiceId: invoice.id,
        amount: invoice.amount_paid
      })
    } catch (error) {
      logger.error('处理发票支付成功事件失败', { error })
    }
  }

  /**
   * 处理发票支付失败事件
   */
  private static async handleInvoicePaymentFailed(
    invoice: Stripe.Invoice
  ): Promise<void> {
    try {
      logger.info('发票支付失败', {
        invoiceId: invoice.id,
        amount: invoice.amount_due
      })
    } catch (error) {
      logger.error('处理发票支付失败事件失败', { error })
    }
  }
}

// 初始化Stripe服务
PaymentService.initialize()
