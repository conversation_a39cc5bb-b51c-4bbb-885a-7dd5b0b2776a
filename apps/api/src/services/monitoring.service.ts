// 性能监控服务
// 提供系统性能监控、指标收集、告警等功能

import { logger } from '../utils/logger'
import { redis } from '../utils/redis'
import { prisma } from '../utils/database'
import os from 'os'
import process from 'process'

/**
 * 性能指标类型
 */
export interface PerformanceMetrics {
  timestamp: Date
  system: {
    cpuUsage: number
    memoryUsage: {
      used: number
      total: number
      percentage: number
    }
    diskUsage: {
      used: number
      total: number
      percentage: number
    }
    loadAverage: number[]
  }
  application: {
    uptime: number
    activeConnections: number
    requestsPerMinute: number
    responseTime: {
      avg: number
      p95: number
      p99: number
    }
    errorRate: number
  }
  database: {
    connectionCount: number
    queryTime: {
      avg: number
      slow: number
    }
    cacheHitRate: number
  }
}

/**
 * 告警规则
 */
export interface AlertRule {
  id: string
  name: string
  metric: string
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte'
  threshold: number
  duration: number // 持续时间（秒）
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  channels: string[] // 告警渠道
}

/**
 * 性能监控服务类
 */
export class MonitoringService {
  private static metrics: Map<string, number[]> = new Map()
  private static alertRules: AlertRule[] = []
  private static isCollecting = false

  /**
   * 初始化监控服务
   */
  static initialize() {
    this.loadAlertRules()
    this.startMetricsCollection()
    logger.info('性能监控服务初始化完成')
  }

  /**
   * 开始指标收集
   */
  static startMetricsCollection() {
    if (this.isCollecting) return

    this.isCollecting = true

    // 每30秒收集一次指标
    setInterval(async () => {
      try {
        await this.collectMetrics()
      } catch (error) {
        logger.error('收集性能指标失败', { error })
      }
    }, 30000)

    // 每分钟检查告警
    setInterval(async () => {
      try {
        await this.checkAlerts()
      } catch (error) {
        logger.error('检查告警失败', { error })
      }
    }, 60000)

    logger.info('开始性能指标收集')
  }

  /**
   * 收集性能指标
   */
  static async collectMetrics(): Promise<PerformanceMetrics> {
    const timestamp = new Date()

    // 系统指标
    const cpuUsage = await this.getCPUUsage()
    const memoryUsage = this.getMemoryUsage()
    const diskUsage = await this.getDiskUsage()
    const loadAverage = os.loadavg()

    // 应用指标
    const uptime = process.uptime()
    const activeConnections = await this.getActiveConnections()
    const requestsPerMinute = await this.getRequestsPerMinute()
    const responseTime = await this.getResponseTime()
    const errorRate = await this.getErrorRate()

    // 数据库指标
    const connectionCount = await this.getDatabaseConnections()
    const queryTime = await this.getQueryTime()
    const cacheHitRate = await this.getCacheHitRate()

    const metrics: PerformanceMetrics = {
      timestamp,
      system: {
        cpuUsage,
        memoryUsage,
        diskUsage,
        loadAverage
      },
      application: {
        uptime,
        activeConnections,
        requestsPerMinute,
        responseTime,
        errorRate
      },
      database: {
        connectionCount,
        queryTime,
        cacheHitRate
      }
    }

    // 存储指标到Redis
    await this.storeMetrics(metrics)

    // 记录关键指标
    this.recordMetric('cpu_usage', cpuUsage)
    this.recordMetric('memory_usage', memoryUsage.percentage)
    this.recordMetric('response_time_avg', responseTime.avg)
    this.recordMetric('error_rate', errorRate)

    return metrics
  }

  /**
   * 获取CPU使用率
   */
  private static async getCPUUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage()
      const startTime = process.hrtime()

      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage)
        const endTime = process.hrtime(startTime)

        const totalTime = endTime[0] * 1000000 + endTime[1] / 1000
        const cpuTime = (endUsage.user + endUsage.system)
        const cpuUsage = (cpuTime / totalTime) * 100

        resolve(Math.round(cpuUsage * 100) / 100)
      }, 100)
    })
  }

  /**
   * 获取内存使用情况
   */
  private static getMemoryUsage() {
    const used = process.memoryUsage().heapUsed
    const total = os.totalmem()
    const percentage = (used / total) * 100

    return {
      used: Math.round(used / 1024 / 1024), // MB
      total: Math.round(total / 1024 / 1024), // MB
      percentage: Math.round(percentage * 100) / 100
    }
  }

  /**
   * 获取磁盘使用情况
   */
  private static async getDiskUsage() {
    // 简化实现，实际应用中可以使用更精确的方法
    return {
      used: 0,
      total: 0,
      percentage: 0
    }
  }

  /**
   * 获取活跃连接数
   */
  private static async getActiveConnections(): Promise<number> {
    try {
      const connections = await redis.get('metrics:active_connections')
      return parseInt(connections || '0')
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取每分钟请求数
   */
  private static async getRequestsPerMinute(): Promise<number> {
    try {
      const requests = await redis.get('metrics:requests_per_minute')
      return parseInt(requests || '0')
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取响应时间统计
   */
  private static async getResponseTime(): Promise<{
    avg: number
    p95: number
    p99: number
  }> {
    try {
      const responseTimes = this.metrics.get('response_times') || []
      if (responseTimes.length === 0) {
        return { avg: 0, p95: 0, p99: 0 }
      }

      const sorted = responseTimes.sort((a, b) => a - b)
      const avg = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      const p95Index = Math.floor(sorted.length * 0.95)
      const p99Index = Math.floor(sorted.length * 0.99)

      return {
        avg: Math.round(avg * 100) / 100,
        p95: sorted[p95Index] || 0,
        p99: sorted[p99Index] || 0
      }
    } catch (error) {
      return { avg: 0, p95: 0, p99: 0 }
    }
  }

  /**
   * 获取错误率
   */
  private static async getErrorRate(): Promise<number> {
    try {
      const totalRequests = await redis.get('metrics:total_requests')
      const errorRequests = await redis.get('metrics:error_requests')
      
      const total = parseInt(totalRequests || '0')
      const errors = parseInt(errorRequests || '0')
      
      if (total === 0) return 0
      
      return Math.round((errors / total) * 10000) / 100 // 保留两位小数
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取数据库连接数
   */
  private static async getDatabaseConnections(): Promise<number> {
    try {
      // 这里需要根据实际的数据库连接池实现
      return 10 // 示例值
    } catch (error) {
      return 0
    }
  }

  /**
   * 获取查询时间统计
   */
  private static async getQueryTime(): Promise<{
    avg: number
    slow: number
  }> {
    try {
      const queryTimes = this.metrics.get('query_times') || []
      if (queryTimes.length === 0) {
        return { avg: 0, slow: 0 }
      }

      const avg = queryTimes.reduce((sum, time) => sum + time, 0) / queryTimes.length
      const slow = queryTimes.filter(time => time > 1000).length // 超过1秒的查询

      return {
        avg: Math.round(avg * 100) / 100,
        slow
      }
    } catch (error) {
      return { avg: 0, slow: 0 }
    }
  }

  /**
   * 获取缓存命中率
   */
  private static async getCacheHitRate(): Promise<number> {
    try {
      const hits = await redis.get('metrics:cache_hits')
      const misses = await redis.get('metrics:cache_misses')
      
      const totalHits = parseInt(hits || '0')
      const totalMisses = parseInt(misses || '0')
      const total = totalHits + totalMisses
      
      if (total === 0) return 0
      
      return Math.round((totalHits / total) * 10000) / 100
    } catch (error) {
      return 0
    }
  }

  /**
   * 存储指标到Redis
   */
  private static async storeMetrics(metrics: PerformanceMetrics) {
    try {
      const key = `metrics:${Date.now()}`
      await redis.setex(key, 86400, JSON.stringify(metrics)) // 保存24小时
      
      // 保存到时间序列
      const timeSeriesKey = 'metrics:timeseries'
      await redis.zadd(timeSeriesKey, Date.now(), key)
      
      // 清理旧数据（保留7天）
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000)
      await redis.zremrangebyscore(timeSeriesKey, 0, sevenDaysAgo)
    } catch (error) {
      logger.error('存储性能指标失败', { error })
    }
  }

  /**
   * 记录单个指标
   */
  private static recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    const values = this.metrics.get(name)!
    values.push(value)
    
    // 只保留最近100个值
    if (values.length > 100) {
      values.shift()
    }
  }

  /**
   * 记录请求指标
   */
  static recordRequest(responseTime: number, isError: boolean = false) {
    this.recordMetric('response_times', responseTime)
    
    // 更新Redis计数器
    redis.incr('metrics:total_requests')
    if (isError) {
      redis.incr('metrics:error_requests')
    }
    
    // 设置过期时间（1小时）
    redis.expire('metrics:total_requests', 3600)
    redis.expire('metrics:error_requests', 3600)
  }

  /**
   * 记录数据库查询指标
   */
  static recordDatabaseQuery(queryTime: number) {
    this.recordMetric('query_times', queryTime)
  }

  /**
   * 记录缓存指标
   */
  static recordCacheHit(isHit: boolean) {
    if (isHit) {
      redis.incr('metrics:cache_hits')
    } else {
      redis.incr('metrics:cache_misses')
    }
    
    // 设置过期时间（1小时）
    redis.expire('metrics:cache_hits', 3600)
    redis.expire('metrics:cache_misses', 3600)
  }

  /**
   * 获取历史指标
   */
  static async getHistoricalMetrics(
    startTime: Date,
    endTime: Date
  ): Promise<PerformanceMetrics[]> {
    try {
      const start = startTime.getTime()
      const end = endTime.getTime()
      
      const keys = await redis.zrangebyscore('metrics:timeseries', start, end)
      const metrics: PerformanceMetrics[] = []
      
      for (const key of keys) {
        const data = await redis.get(key)
        if (data) {
          metrics.push(JSON.parse(data))
        }
      }
      
      return metrics.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
    } catch (error) {
      logger.error('获取历史指标失败', { error })
      return []
    }
  }

  /**
   * 加载告警规则
   */
  private static loadAlertRules() {
    this.alertRules = [
      {
        id: 'high_cpu',
        name: 'CPU使用率过高',
        metric: 'cpu_usage',
        operator: 'gt',
        threshold: 80,
        duration: 300, // 5分钟
        severity: 'high',
        enabled: true,
        channels: ['email', 'webhook']
      },
      {
        id: 'high_memory',
        name: '内存使用率过高',
        metric: 'memory_usage',
        operator: 'gt',
        threshold: 85,
        duration: 300,
        severity: 'high',
        enabled: true,
        channels: ['email', 'webhook']
      },
      {
        id: 'high_response_time',
        name: '响应时间过长',
        metric: 'response_time_avg',
        operator: 'gt',
        threshold: 2000, // 2秒
        duration: 180, // 3分钟
        severity: 'medium',
        enabled: true,
        channels: ['email']
      },
      {
        id: 'high_error_rate',
        name: '错误率过高',
        metric: 'error_rate',
        operator: 'gt',
        threshold: 5, // 5%
        duration: 120, // 2分钟
        severity: 'critical',
        enabled: true,
        channels: ['email', 'webhook', 'sms']
      }
    ]
  }

  /**
   * 检查告警
   */
  private static async checkAlerts() {
    for (const rule of this.alertRules) {
      if (!rule.enabled) continue

      try {
        await this.evaluateAlertRule(rule)
      } catch (error) {
        logger.error('评估告警规则失败', {
          ruleId: rule.id,
          error
        })
      }
    }
  }

  /**
   * 评估告警规则
   */
  private static async evaluateAlertRule(rule: AlertRule) {
    const values = this.metrics.get(rule.metric) || []
    if (values.length === 0) return

    const currentValue = values[values.length - 1]
    const isTriggered = this.evaluateCondition(currentValue, rule.operator, rule.threshold)

    if (isTriggered) {
      const alertKey = `alert:${rule.id}`
      const existingAlert = await redis.get(alertKey)

      if (!existingAlert) {
        // 新告警
        await redis.setex(alertKey, rule.duration, JSON.stringify({
          startTime: Date.now(),
          value: currentValue
        }))
      } else {
        // 检查是否达到持续时间
        const alertData = JSON.parse(existingAlert)
        const duration = (Date.now() - alertData.startTime) / 1000

        if (duration >= rule.duration) {
          await this.triggerAlert(rule, currentValue, duration)
          // 重置告警状态，避免重复发送
          await redis.del(alertKey)
        }
      }
    } else {
      // 清除告警状态
      await redis.del(`alert:${rule.id}`)
    }
  }

  /**
   * 评估条件
   */
  private static evaluateCondition(
    value: number,
    operator: string,
    threshold: number
  ): boolean {
    switch (operator) {
      case 'gt': return value > threshold
      case 'gte': return value >= threshold
      case 'lt': return value < threshold
      case 'lte': return value <= threshold
      case 'eq': return value === threshold
      default: return false
    }
  }

  /**
   * 触发告警
   */
  private static async triggerAlert(
    rule: AlertRule,
    value: number,
    duration: number
  ) {
    const alert = {
      id: `alert_${Date.now()}`,
      rule: rule.name,
      metric: rule.metric,
      value,
      threshold: rule.threshold,
      severity: rule.severity,
      duration,
      timestamp: new Date()
    }

    logger.warn('触发告警', alert)

    // 发送告警通知
    for (const channel of rule.channels) {
      try {
        await this.sendAlert(channel, alert)
      } catch (error) {
        logger.error('发送告警失败', {
          channel,
          alertId: alert.id,
          error
        })
      }
    }
  }

  /**
   * 发送告警
   */
  private static async sendAlert(channel: string, alert: any) {
    switch (channel) {
      case 'email':
        // 发送邮件告警
        break
      case 'webhook':
        // 发送Webhook告警
        break
      case 'sms':
        // 发送短信告警
        break
      default:
        logger.warn('未知的告警渠道', { channel })
    }
  }

  /**
   * 获取当前系统状态
   */
  static async getSystemStatus(): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    metrics: PerformanceMetrics
    alerts: any[]
  }> {
    try {
      const metrics = await this.collectMetrics()
      const activeAlerts = await this.getActiveAlerts()

      let status: 'healthy' | 'warning' | 'critical' = 'healthy'

      // 根据告警确定系统状态
      if (activeAlerts.some(alert => alert.severity === 'critical')) {
        status = 'critical'
      } else if (activeAlerts.length > 0) {
        status = 'warning'
      }

      return {
        status,
        metrics,
        alerts: activeAlerts
      }
    } catch (error) {
      logger.error('获取系统状态失败', { error })
      throw error
    }
  }

  /**
   * 获取活跃告警
   */
  private static async getActiveAlerts(): Promise<any[]> {
    const alerts: any[] = []
    
    for (const rule of this.alertRules) {
      const alertKey = `alert:${rule.id}`
      const alertData = await redis.get(alertKey)
      
      if (alertData) {
        alerts.push({
          rule: rule.name,
          severity: rule.severity,
          ...JSON.parse(alertData)
        })
      }
    }
    
    return alerts
  }
}
