// 用户资料管理服务
// 处理用户资料查看、更新、头像上传等功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { DataSanitizer, validateData, ValidationRules } from '../utils/validation'
import { logger } from '../utils/logger'
import { emailService } from './email.service'
import { z } from 'zod'
import bcrypt from 'bcrypt'
import { config } from '../config'

/**
 * 用户资料更新接口
 */
export interface UpdateUserProfileData {
  firstName?: string
  lastName?: string
  phone?: string
  avatar?: string
  timezone?: string
  language?: string
  bio?: string
  company?: string
  website?: string
}

/**
 * 用户偏好设置接口
 */
export interface UserPreferences {
  emailNotifications?: boolean
  smsNotifications?: boolean
  marketingEmails?: boolean
  theme?: 'light' | 'dark' | 'auto'
  language?: string
  timezone?: string
}

/**
 * 用户统计信息接口
 */
export interface UserStats {
  totalCampaigns: number
  activeCampaigns: number
  totalAIGenerations: number
  totalSpent: number
  joinedDays: number
  lastLoginDays: number
}

/**
 * 用户服务类
 */
export class UserService {
  /**
   * 获取用户资料
   */
  static async getUserProfile(userId: string) {
    logger.debug('获取用户资料', { userId })

    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          phone: true,
          role: true,
          status: true,
          emailVerified: true,
          bio: true,
          company: true,
          website: true,
          timezone: true,
          language: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
          // 关联数据
          _count: {
            select: {
              campaigns: true,
              aiGenerationHistory: true
            }
          }
        }
      })

      if (!user) {
        throw new ApiError(ErrorCodes.USER_NOT_FOUND, '用户不存在')
      }

      // 获取用户统计信息
      const stats = await this.getUserStats(userId)

      // 获取用户偏好设置
      const preferences = await this.getUserPreferences(userId)

      logger.debug('用户资料获取成功', { userId })

      return {
        user: {
          ...user,
          fullName: `${user.firstName} ${user.lastName}`.trim()
        },
        stats,
        preferences
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('获取用户资料失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取用户资料失败')
    }
  }

  /**
   * 更新用户资料
   */
  static async updateUserProfile(userId: string, updateData: UpdateUserProfileData) {
    logger.info('更新用户资料', { userId })

    try {
      // 验证输入数据
      const validationSchema = z.object({
        firstName: ValidationRules.name.optional(),
        lastName: ValidationRules.name.optional(),
        phone: ValidationRules.phone.optional(),
        avatar: ValidationRules.url.optional(),
        timezone: z.string().max(50).optional(),
        language: z.string().max(10).optional(),
        bio: z.string().max(500, '个人简介不能超过500个字符').optional(),
        company: z.string().max(100, '公司名称不能超过100个字符').optional(),
        website: ValidationRules.url.optional()
      })

      const validatedData = validateData(validationSchema, updateData)

      // 清理数据
      const cleanData: any = {}
      
      if (validatedData.firstName) {
        cleanData.firstName = DataSanitizer.sanitizeText(validatedData.firstName)
      }
      
      if (validatedData.lastName) {
        cleanData.lastName = DataSanitizer.sanitizeText(validatedData.lastName)
      }
      
      if (validatedData.phone) {
        cleanData.phone = DataSanitizer.sanitizePhone(validatedData.phone)
      }
      
      if (validatedData.avatar) {
        cleanData.avatar = DataSanitizer.sanitizeUrl(validatedData.avatar)
      }
      
      if (validatedData.bio) {
        cleanData.bio = DataSanitizer.sanitizeText(validatedData.bio)
      }
      
      if (validatedData.company) {
        cleanData.company = DataSanitizer.sanitizeText(validatedData.company)
      }
      
      if (validatedData.website) {
        cleanData.website = DataSanitizer.sanitizeUrl(validatedData.website)
      }
      
      if (validatedData.timezone) {
        cleanData.timezone = validatedData.timezone
      }
      
      if (validatedData.language) {
        cleanData.language = validatedData.language
      }

      // 更新用户资料
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...cleanData,
          updatedAt: new Date()
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          phone: true,
          bio: true,
          company: true,
          website: true,
          timezone: true,
          language: true,
          updatedAt: true
        }
      })

      // 清除用户缓存
      await this.clearUserCache(userId)

      logger.info('用户资料更新成功', { userId })

      return {
        user: {
          ...updatedUser,
          fullName: `${updatedUser.firstName} ${updatedUser.lastName}`.trim()
        },
        message: '资料更新成功'
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('更新用户资料失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '更新用户资料失败')
    }
  }

  /**
   * 更新用户偏好设置
   */
  static async updateUserPreferences(userId: string, preferences: UserPreferences) {
    logger.info('更新用户偏好设置', { userId })

    try {
      // 验证偏好设置
      const validationSchema = z.object({
        emailNotifications: z.boolean().optional(),
        smsNotifications: z.boolean().optional(),
        marketingEmails: z.boolean().optional(),
        theme: z.enum(['light', 'dark', 'auto']).optional(),
        language: z.string().max(10).optional(),
        timezone: z.string().max(50).optional()
      })

      const validatedPreferences = validateData(validationSchema, preferences)

      // 保存到Redis
      const cacheKey = `user_preferences:${userId}`
      await redis.set(cacheKey, validatedPreferences, 86400) // 24小时缓存

      // 如果有语言或时区变更，同时更新到数据库
      if (validatedPreferences.language || validatedPreferences.timezone) {
        const updateData: any = {}
        if (validatedPreferences.language) {
          updateData.language = validatedPreferences.language
        }
        if (validatedPreferences.timezone) {
          updateData.timezone = validatedPreferences.timezone
        }

        await prisma.user.update({
          where: { id: userId },
          data: updateData
        })
      }

      logger.info('用户偏好设置更新成功', { userId })

      return {
        preferences: validatedPreferences,
        message: '偏好设置更新成功'
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('更新用户偏好设置失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '更新偏好设置失败')
    }
  }

  /**
   * 获取用户偏好设置
   */
  static async getUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      // 先从缓存获取
      const cacheKey = `user_preferences:${userId}`
      const cachedPreferences = await redis.get<UserPreferences>(cacheKey)
      
      if (cachedPreferences) {
        return cachedPreferences
      }

      // 从数据库获取基本设置
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          language: true,
          timezone: true
        }
      })

      // 默认偏好设置
      const defaultPreferences: UserPreferences = {
        emailNotifications: true,
        smsNotifications: false,
        marketingEmails: true,
        theme: 'light',
        language: user?.language || 'zh-CN',
        timezone: user?.timezone || 'Asia/Shanghai'
      }

      // 缓存默认设置
      await redis.set(cacheKey, defaultPreferences, 86400)

      return defaultPreferences
    } catch (error) {
      logger.error('获取用户偏好设置失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      
      // 返回默认设置
      return {
        emailNotifications: true,
        smsNotifications: false,
        marketingEmails: true,
        theme: 'light',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai'
      }
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(userId: string): Promise<UserStats> {
    try {
      // 先从缓存获取
      const cacheKey = `user_stats:${userId}`
      const cachedStats = await redis.get<UserStats>(cacheKey)
      
      if (cachedStats) {
        return cachedStats
      }

      // 从数据库计算统计信息
      const [
        totalCampaigns,
        activeCampaigns,
        totalAIGenerations,
        user
      ] = await Promise.all([
        prisma.campaign.count({
          where: { userId }
        }),
        prisma.campaign.count({
          where: { 
            userId,
            status: { in: ['RUNNING', 'SCHEDULED'] }
          }
        }),
        prisma.aIGenerationHistory.count({
          where: { userId }
        }),
        prisma.user.findUnique({
          where: { id: userId },
          select: {
            createdAt: true,
            lastLoginAt: true
          }
        })
      ])

      // 计算加入天数和最后登录天数
      const now = new Date()
      const joinedDays = user ? Math.floor((now.getTime() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24)) : 0
      const lastLoginDays = user?.lastLoginAt 
        ? Math.floor((now.getTime() - user.lastLoginAt.getTime()) / (1000 * 60 * 60 * 24))
        : 0

      const stats: UserStats = {
        totalCampaigns,
        activeCampaigns,
        totalAIGenerations,
        totalSpent: 0, // TODO: 从支付记录计算
        joinedDays,
        lastLoginDays
      }

      // 缓存统计信息（5分钟）
      await redis.set(cacheKey, stats, 300)

      return stats
    } catch (error) {
      logger.error('获取用户统计信息失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      
      // 返回默认统计
      return {
        totalCampaigns: 0,
        activeCampaigns: 0,
        totalAIGenerations: 0,
        totalSpent: 0,
        joinedDays: 0,
        lastLoginDays: 0
      }
    }
  }

  /**
   * 删除用户账户
   */
  static async deleteUserAccount(userId: string, password: string) {
    logger.info('删除用户账户', { userId })

    try {
      // 获取用户信息验证密码
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          password: true,
          status: true
        }
      })

      if (!user) {
        throw new ApiError(ErrorCodes.USER_NOT_FOUND, '用户不存在')
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.password)
      if (!isPasswordValid) {
        throw new ApiError(ErrorCodes.INVALID_PASSWORD, '密码错误')
      }

      // 软删除用户（标记为已删除状态）
      await prisma.user.update({
        where: { id: userId },
        data: {
          status: 'DELETED',
          email: `deleted_${Date.now()}_${user.email}`, // 避免邮箱冲突
          deletedAt: new Date()
        }
      })

      // 清除所有相关缓存
      await this.clearUserCache(userId)
      await redis.del(`user_preferences:${userId}`)
      await redis.del(`user_stats:${userId}`)

      // 发送账户删除确认邮件
      try {
        await emailService.sendAccountDeletionConfirmation(user.email, user.firstName)
      } catch (emailError) {
        logger.warn('发送账户删除确认邮件失败', {
          userId,
          error: emailError instanceof Error ? emailError.message : String(emailError)
        })
      }

      logger.info('用户账户删除成功', { userId })

      return { message: '账户已成功删除' }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('删除用户账户失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '删除账户失败')
    }
  }

  /**
   * 清除用户缓存
   */
  private static async clearUserCache(userId: string) {
    try {
      await redis.delPattern(`user:${userId}:*`)
      await redis.del(`session:*:${userId}`)
    } catch (error) {
      logger.warn('清除用户缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 获取用户列表（管理员功能）
   */
  static async getUserList(options: {
    page: number
    limit: number
    search?: string
    role?: string
    status?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }) {
    logger.debug('获取用户列表', options)

    try {
      const { page, limit, search, role, status, sortBy = 'createdAt', sortOrder = 'desc' } = options

      // 构建查询条件
      const where: any = {}
      
      if (search) {
        where.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } }
        ]
      }
      
      if (role) {
        where.role = role
      }
      
      if (status) {
        where.status = status
      }

      // 计算偏移量
      const skip = (page - 1) * limit

      // 查询用户列表和总数
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            avatar: true,
            role: true,
            status: true,
            emailVerified: true,
            lastLoginAt: true,
            createdAt: true,
            _count: {
              select: {
                campaigns: true,
                aiGenerationHistory: true
              }
            }
          },
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder
          }
        }),
        prisma.user.count({ where })
      ])

      const totalPages = Math.ceil(total / limit)

      return {
        users: users.map(user => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`.trim()
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      logger.error('获取用户列表失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取用户列表失败')
    }
  }
}
