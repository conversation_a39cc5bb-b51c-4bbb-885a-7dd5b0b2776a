// 报告生成系统服务
// 提供自动化报告生成、模板管理、定时发送等功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { emailService } from './email.service'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { DataAnalysisService } from './data-analysis.service'
import { AnalyticsService } from './analytics.service'
import PDFDocument from 'pdfkit'
import ExcelJS from 'exceljs'
import fs from 'fs/promises'
import path from 'path'

/**
 * 报告类型
 */
export enum ReportType {
  ANALYTICS_SUMMARY = 'ANALYTICS_SUMMARY',
  CAMPAIGN_PERFORMANCE = 'CAMPAIGN_PERFORMANCE',
  USER_BEHAVIOR = 'USER_BEHAVIOR',
  REVENUE_ANALYSIS = 'REVENUE_ANALYSIS',
  COHORT_ANALYSIS = 'COHORT_ANALYSIS',
  FUNNEL_ANALYSIS = 'FUNNEL_ANALYSIS',
  CUSTOM = 'CUSTOM'
}

/**
 * 报告格式
 */
export enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  HTML = 'HTML',
  JSON = 'JSON'
}

/**
 * 报告频率
 */
export enum ReportFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ON_DEMAND = 'ON_DEMAND'
}

/**
 * 报告模板接口
 */
export interface ReportTemplate {
  id: string
  name: string
  description: string
  type: ReportType
  format: ReportFormat
  sections: {
    id: string
    name: string
    type: 'chart' | 'table' | 'metric' | 'text'
    config: {
      dataSource: string
      visualization?: string
      filters?: any
      aggregation?: string
    }
  }[]
  styling: {
    theme: string
    colors: string[]
    fonts: string[]
    layout: string
  }
  isActive: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

/**
 * 报告生成请求接口
 */
export interface GenerateReportRequest {
  templateId?: string
  type: ReportType
  format: ReportFormat
  dateRange: {
    startDate: Date
    endDate: Date
  }
  filters?: {
    campaigns?: string[]
    segments?: string[]
    channels?: string[]
    customFilters?: any
  }
  recipients?: {
    email: string
    name?: string
  }[]
  customSections?: any[]
}

/**
 * 定时报告配置接口
 */
export interface ScheduledReportConfig {
  templateId: string
  frequency: ReportFrequency
  recipients: string[]
  filters?: any
  isActive: boolean
  nextRunAt: Date
}

/**
 * 报告生成服务类
 */
export class ReportService {
  /**
   * 生成报告
   */
  static async generateReport(
    request: GenerateReportRequest,
    userId: string
  ): Promise<{
    reportId: string
    filePath: string
    downloadUrl: string
  }> {
    logger.info('开始生成报告', {
      userId,
      type: request.type,
      format: request.format
    })

    try {
      // 获取或创建报告模板
      let template: ReportTemplate
      if (request.templateId) {
        template = await this.getReportTemplate(request.templateId, userId)
      } else {
        template = this.getDefaultTemplate(request.type, request.format)
      }

      // 收集报告数据
      const reportData = await this.collectReportData(
        template,
        request,
        userId
      )

      // 生成报告文件
      const filePath = await this.generateReportFile(
        template,
        reportData,
        request.format,
        userId
      )

      // 保存报告记录
      const report = await prisma.report.create({
        data: {
          userId,
          templateId: request.templateId,
          type: request.type,
          format: request.format,
          filePath,
          dateRange: request.dateRange,
          filters: request.filters || {},
          status: 'COMPLETED',
          generatedAt: new Date(),
          metadata: {
            dataPoints: reportData.summary?.totalDataPoints || 0,
            sections: template.sections.length
          }
        }
      })

      // 发送邮件（如果有收件人）
      if (request.recipients && request.recipients.length > 0) {
        await this.sendReportByEmail(report.id, request.recipients)
      }

      const downloadUrl = `/api/reports/${report.id}/download`

      logger.info('报告生成完成', {
        userId,
        reportId: report.id,
        filePath
      })

      return {
        reportId: report.id,
        filePath,
        downloadUrl
      }
    } catch (error) {
      logger.error('报告生成失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '报告生成失败')
    }
  }

  /**
   * 创建报告模板
   */
  static async createReportTemplate(
    template: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string
  ): Promise<ReportTemplate> {
    logger.info('创建报告模板', { userId, templateName: template.name })

    try {
      const createdTemplate = await prisma.reportTemplate.create({
        data: {
          userId,
          name: template.name,
          description: template.description,
          type: template.type,
          format: template.format,
          sections: template.sections,
          styling: template.styling,
          isActive: template.isActive,
          createdBy: userId
        }
      })

      logger.info('报告模板创建成功', {
        userId,
        templateId: createdTemplate.id
      })

      return createdTemplate as ReportTemplate
    } catch (error) {
      logger.error('创建报告模板失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建报告模板失败')
    }
  }

  /**
   * 配置定时报告
   */
  static async scheduleReport(
    config: ScheduledReportConfig,
    userId: string
  ): Promise<string> {
    logger.info('配置定时报告', { userId, templateId: config.templateId })

    try {
      const scheduledReport = await prisma.scheduledReport.create({
        data: {
          userId,
          templateId: config.templateId,
          frequency: config.frequency,
          recipients: config.recipients,
          filters: config.filters || {},
          isActive: config.isActive,
          nextRunAt: config.nextRunAt
        }
      })

      // 注册定时任务
      await this.registerScheduledTask(scheduledReport.id, config)

      logger.info('定时报告配置成功', {
        userId,
        scheduledReportId: scheduledReport.id
      })

      return scheduledReport.id
    } catch (error) {
      logger.error('配置定时报告失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '配置定时报告失败')
    }
  }

  /**
   * 获取用户报告列表
   */
  static async getUserReports(
    userId: string,
    options: {
      page?: number
      limit?: number
      type?: ReportType
      format?: ReportFormat
      startDate?: Date
      endDate?: Date
    } = {}
  ): Promise<{
    reports: any[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    try {
      const {
        page = 1,
        limit = 20,
        type,
        format,
        startDate,
        endDate
      } = options

      const where: any = { userId }

      if (type) where.type = type
      if (format) where.format = format
      if (startDate || endDate) {
        where.generatedAt = {}
        if (startDate) where.generatedAt.gte = startDate
        if (endDate) where.generatedAt.lte = endDate
      }

      const skip = (page - 1) * limit

      const [reports, total] = await Promise.all([
        prisma.report.findMany({
          where,
          skip,
          take: limit,
          orderBy: { generatedAt: 'desc' },
          include: {
            template: {
              select: {
                name: true,
                description: true
              }
            }
          }
        }),
        prisma.report.count({ where })
      ])

      const totalPages = Math.ceil(total / limit)

      return {
        reports,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      logger.error('获取用户报告列表失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取报告列表失败')
    }
  }

  /**
   * 收集报告数据
   */
  private static async collectReportData(
    template: ReportTemplate,
    request: GenerateReportRequest,
    userId: string
  ): Promise<any> {
    const reportData: any = {
      metadata: {
        generatedAt: new Date(),
        dateRange: request.dateRange,
        template: template.name
      },
      sections: []
    }

    for (const section of template.sections) {
      try {
        let sectionData: any = {}

        switch (section.config.dataSource) {
          case 'analytics':
            sectionData = await this.getAnalyticsData(
              userId,
              request.dateRange,
              section.config
            )
            break
          case 'campaigns':
            sectionData = await this.getCampaignData(
              userId,
              request.dateRange,
              request.filters?.campaigns
            )
            break
          case 'users':
            sectionData = await this.getUserData(
              userId,
              request.dateRange,
              section.config
            )
            break
          case 'revenue':
            sectionData = await this.getRevenueData(
              userId,
              request.dateRange,
              section.config
            )
            break
          default:
            logger.warn('未知的数据源', { dataSource: section.config.dataSource })
            sectionData = { error: '未知的数据源' }
        }

        reportData.sections.push({
          id: section.id,
          name: section.name,
          type: section.type,
          data: sectionData
        })
      } catch (error) {
        logger.warn('收集报告数据失败', {
          sectionId: section.id,
          error: error instanceof Error ? error.message : String(error)
        })
        
        reportData.sections.push({
          id: section.id,
          name: section.name,
          type: section.type,
          data: { error: '数据收集失败' }
        })
      }
    }

    // 计算汇总统计
    reportData.summary = this.calculateReportSummary(reportData.sections)

    return reportData
  }

  /**
   * 生成报告文件
   */
  private static async generateReportFile(
    template: ReportTemplate,
    reportData: any,
    format: ReportFormat,
    userId: string
  ): Promise<string> {
    const fileName = `report_${Date.now()}_${userId}.${format.toLowerCase()}`
    const filePath = path.join(process.cwd(), 'temp', 'reports', fileName)

    // 确保目录存在
    await fs.mkdir(path.dirname(filePath), { recursive: true })

    switch (format) {
      case ReportFormat.PDF:
        await this.generatePDFReport(template, reportData, filePath)
        break
      case ReportFormat.EXCEL:
        await this.generateExcelReport(template, reportData, filePath)
        break
      case ReportFormat.HTML:
        await this.generateHTMLReport(template, reportData, filePath)
        break
      case ReportFormat.JSON:
        await this.generateJSONReport(template, reportData, filePath)
        break
      default:
        throw new Error(`不支持的报告格式: ${format}`)
    }

    return filePath
  }

  /**
   * 生成PDF报告
   */
  private static async generatePDFReport(
    template: ReportTemplate,
    reportData: any,
    filePath: string
  ): Promise<void> {
    const doc = new PDFDocument()
    const stream = fs.createWriteStream(filePath)
    doc.pipe(stream)

    // 添加标题
    doc.fontSize(20).text(template.name, { align: 'center' })
    doc.moveDown()

    // 添加元数据
    doc.fontSize(12)
      .text(`生成时间: ${reportData.metadata.generatedAt.toLocaleString('zh-CN')}`)
      .text(`数据范围: ${reportData.metadata.dateRange.startDate.toLocaleDateString('zh-CN')} - ${reportData.metadata.dateRange.endDate.toLocaleDateString('zh-CN')}`)
    doc.moveDown()

    // 添加各个部分
    for (const section of reportData.sections) {
      doc.fontSize(16).text(section.name)
      doc.moveDown(0.5)

      if (section.data.error) {
        doc.fontSize(12).text(`错误: ${section.data.error}`)
      } else {
        switch (section.type) {
          case 'metric':
            if (section.data.value !== undefined) {
              doc.fontSize(24).text(section.data.value.toString())
              if (section.data.change) {
                doc.fontSize(12).text(`变化: ${section.data.change > 0 ? '+' : ''}${section.data.change}%`)
              }
            }
            break
          case 'table':
            if (section.data.rows && section.data.headers) {
              // 简单的表格渲染
              doc.fontSize(10)
              doc.text(section.data.headers.join(' | '))
              for (const row of section.data.rows.slice(0, 10)) { // 限制行数
                doc.text(row.join(' | '))
              }
            }
            break
          case 'text':
            doc.fontSize(12).text(section.data.content || '无内容')
            break
          default:
            doc.fontSize(12).text('图表数据 (PDF中暂不支持图表渲染)')
        }
      }

      doc.moveDown()
    }

    doc.end()

    return new Promise((resolve, reject) => {
      stream.on('finish', resolve)
      stream.on('error', reject)
    })
  }

  /**
   * 生成Excel报告
   */
  private static async generateExcelReport(
    template: ReportTemplate,
    reportData: any,
    filePath: string
  ): Promise<void> {
    const workbook = new ExcelJS.Workbook()
    
    // 创建汇总工作表
    const summarySheet = workbook.addWorksheet('汇总')
    summarySheet.addRow(['报告名称', template.name])
    summarySheet.addRow(['生成时间', reportData.metadata.generatedAt.toLocaleString('zh-CN')])
    summarySheet.addRow(['数据范围', `${reportData.metadata.dateRange.startDate.toLocaleDateString('zh-CN')} - ${reportData.metadata.dateRange.endDate.toLocaleDateString('zh-CN')}`])
    summarySheet.addRow([])

    // 为每个部分创建工作表
    for (const section of reportData.sections) {
      const sheet = workbook.addWorksheet(section.name.substring(0, 31)) // Excel工作表名称限制

      if (section.data.error) {
        sheet.addRow(['错误', section.data.error])
      } else {
        switch (section.type) {
          case 'metric':
            sheet.addRow(['指标', '值'])
            sheet.addRow([section.name, section.data.value])
            if (section.data.change) {
              sheet.addRow(['变化百分比', `${section.data.change}%`])
            }
            break
          case 'table':
            if (section.data.headers && section.data.rows) {
              sheet.addRow(section.data.headers)
              for (const row of section.data.rows) {
                sheet.addRow(row)
              }
            }
            break
          case 'chart':
            if (section.data.data) {
              // 将图表数据转换为表格
              sheet.addRow(['时间', '值'])
              for (const point of section.data.data) {
                sheet.addRow([point.timestamp, point.value])
              }
            }
            break
          default:
            sheet.addRow(['内容', JSON.stringify(section.data)])
        }
      }
    }

    await workbook.xlsx.writeFile(filePath)
  }

  /**
   * 生成HTML报告
   */
  private static async generateHTMLReport(
    template: ReportTemplate,
    reportData: any,
    filePath: string
  ): Promise<void> {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${template.name}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { font-size: 2em; font-weight: bold; color: #2563eb; }
        .change { font-size: 1.2em; color: #059669; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .error { color: #dc2626; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${template.name}</h1>
        <p>生成时间: ${reportData.metadata.generatedAt.toLocaleString('zh-CN')}</p>
        <p>数据范围: ${reportData.metadata.dateRange.startDate.toLocaleDateString('zh-CN')} - ${reportData.metadata.dateRange.endDate.toLocaleDateString('zh-CN')}</p>
    </div>
    
    ${reportData.sections.map((section: any) => `
        <div class="section">
            <h2>${section.name}</h2>
            ${section.data.error ? 
                `<p class="error">错误: ${section.data.error}</p>` :
                this.renderSectionHTML(section)
            }
        </div>
    `).join('')}
</body>
</html>`

    await fs.writeFile(filePath, html, 'utf-8')
  }

  /**
   * 生成JSON报告
   */
  private static async generateJSONReport(
    template: ReportTemplate,
    reportData: any,
    filePath: string
  ): Promise<void> {
    const jsonReport = {
      template: {
        id: template.id,
        name: template.name,
        type: template.type
      },
      ...reportData
    }

    await fs.writeFile(filePath, JSON.stringify(jsonReport, null, 2), 'utf-8')
  }

  /**
   * 渲染HTML部分
   */
  private static renderSectionHTML(section: any): string {
    switch (section.type) {
      case 'metric':
        return `
          <div class="metric">${section.data.value || 'N/A'}</div>
          ${section.data.change ? `<div class="change">变化: ${section.data.change > 0 ? '+' : ''}${section.data.change}%</div>` : ''}
        `
      case 'table':
        if (!section.data.headers || !section.data.rows) return '<p>无数据</p>'
        return `
          <table>
            <thead>
              <tr>${section.data.headers.map((h: string) => `<th>${h}</th>`).join('')}</tr>
            </thead>
            <tbody>
              ${section.data.rows.map((row: any[]) => 
                `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`
              ).join('')}
            </tbody>
          </table>
        `
      case 'text':
        return `<p>${section.data.content || '无内容'}</p>`
      default:
        return `<p>数据类型: ${section.type}</p>`
    }
  }

  /**
   * 获取分析数据
   */
  private static async getAnalyticsData(
    userId: string,
    dateRange: { startDate: Date; endDate: Date },
    config: any
  ): Promise<any> {
    const result = await AnalyticsService.queryAnalytics(userId, {
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      groupBy: config.groupBy || 'day',
      metrics: config.metrics || ['count', 'unique_users']
    })

    return {
      data: result.data,
      summary: result.summary,
      trends: result.trends
    }
  }

  /**
   * 获取活动数据
   */
  private static async getCampaignData(
    userId: string,
    dateRange: { startDate: Date; endDate: Date },
    campaignIds?: string[]
  ): Promise<any> {
    const where: any = {
      userId,
      createdAt: {
        gte: dateRange.startDate,
        lte: dateRange.endDate
      }
    }

    if (campaignIds && campaignIds.length > 0) {
      where.id = { in: campaignIds }
    }

    const campaigns = await prisma.campaign.findMany({
      where,
      select: {
        id: true,
        name: true,
        type: true,
        status: true,
        budget: true,
        createdAt: true
      }
    })

    return {
      headers: ['活动名称', '类型', '状态', '预算', '创建时间'],
      rows: campaigns.map(c => [
        c.name,
        c.type,
        c.status,
        c.budget?.total || 0,
        c.createdAt.toLocaleDateString('zh-CN')
      ])
    }
  }

  /**
   * 获取用户数据
   */
  private static async getUserData(
    userId: string,
    dateRange: { startDate: Date; endDate: Date },
    config: any
  ): Promise<any> {
    const userCount = await prisma.user.count({
      where: {
        createdAt: {
          gte: dateRange.startDate,
          lte: dateRange.endDate
        }
      }
    })

    return {
      value: userCount,
      change: 0 // 需要计算与上期的变化
    }
  }

  /**
   * 获取收入数据
   */
  private static async getRevenueData(
    userId: string,
    dateRange: { startDate: Date; endDate: Date },
    config: any
  ): Promise<any> {
    // 这里需要根据实际的收入数据表结构来实现
    return {
      value: 0,
      change: 0
    }
  }

  /**
   * 计算报告汇总
   */
  private static calculateReportSummary(sections: any[]): any {
    return {
      totalSections: sections.length,
      successfulSections: sections.filter(s => !s.data.error).length,
      totalDataPoints: sections.reduce((sum, s) => {
        if (s.data.data && Array.isArray(s.data.data)) {
          return sum + s.data.data.length
        }
        return sum
      }, 0)
    }
  }

  /**
   * 通过邮件发送报告
   */
  private static async sendReportByEmail(
    reportId: string,
    recipients: { email: string; name?: string }[]
  ): Promise<void> {
    try {
      const report = await prisma.report.findUnique({
        where: { id: reportId },
        include: { template: true }
      })

      if (!report) {
        throw new Error('报告不存在')
      }

      for (const recipient of recipients) {
        await emailService.sendEmail(
          recipient.email,
          `数据报告: ${report.template?.name || report.type}`,
          `
            <h2>您的数据报告已生成</h2>
            <p>报告类型: ${report.type}</p>
            <p>生成时间: ${report.generatedAt.toLocaleString('zh-CN')}</p>
            <p>请点击下方链接下载报告:</p>
            <a href="${process.env.APP_URL}/api/reports/${reportId}/download">下载报告</a>
          `
        )
      }
    } catch (error) {
      logger.error('发送报告邮件失败', {
        reportId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 获取报告模板
   */
  private static async getReportTemplate(
    templateId: string,
    userId: string
  ): Promise<ReportTemplate> {
    const template = await prisma.reportTemplate.findFirst({
      where: {
        id: templateId,
        userId,
        isActive: true
      }
    })

    if (!template) {
      throw new ApiError(ErrorCodes.NOT_FOUND, '报告模板不存在')
    }

    return template as ReportTemplate
  }

  /**
   * 获取默认模板
   */
  private static getDefaultTemplate(type: ReportType, format: ReportFormat): ReportTemplate {
    const defaultTemplates: Record<ReportType, any> = {
      [ReportType.ANALYTICS_SUMMARY]: {
        name: '分析汇总报告',
        sections: [
          {
            id: 'overview',
            name: '概览',
            type: 'metric',
            config: { dataSource: 'analytics', metrics: ['count', 'unique_users'] }
          },
          {
            id: 'trends',
            name: '趋势分析',
            type: 'chart',
            config: { dataSource: 'analytics', groupBy: 'day' }
          }
        ]
      },
      [ReportType.CAMPAIGN_PERFORMANCE]: {
        name: '活动效果报告',
        sections: [
          {
            id: 'campaigns',
            name: '活动列表',
            type: 'table',
            config: { dataSource: 'campaigns' }
          }
        ]
      },
      [ReportType.USER_BEHAVIOR]: {
        name: '用户行为报告',
        sections: [
          {
            id: 'users',
            name: '用户统计',
            type: 'metric',
            config: { dataSource: 'users' }
          }
        ]
      },
      [ReportType.REVENUE_ANALYSIS]: {
        name: '收入分析报告',
        sections: [
          {
            id: 'revenue',
            name: '收入统计',
            type: 'metric',
            config: { dataSource: 'revenue' }
          }
        ]
      },
      [ReportType.COHORT_ANALYSIS]: {
        name: '队列分析报告',
        sections: []
      },
      [ReportType.FUNNEL_ANALYSIS]: {
        name: '漏斗分析报告',
        sections: []
      },
      [ReportType.CUSTOM]: {
        name: '自定义报告',
        sections: []
      }
    }

    const template = defaultTemplates[type]
    
    return {
      id: `default_${type}`,
      name: template.name,
      description: `默认的${template.name}`,
      type,
      format,
      sections: template.sections,
      styling: {
        theme: 'default',
        colors: ['#2563eb', '#059669', '#dc2626'],
        fonts: ['Arial', 'sans-serif'],
        layout: 'standard'
      },
      isActive: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }

  /**
   * 注册定时任务
   */
  private static async registerScheduledTask(
    scheduledReportId: string,
    config: ScheduledReportConfig
  ): Promise<void> {
    // 在实际应用中，这里应该注册到任务队列系统
    // 例如：Bull Queue、Agenda.js等
    logger.info('注册定时报告任务', {
      scheduledReportId,
      frequency: config.frequency,
      nextRunAt: config.nextRunAt
    })
  }
}
