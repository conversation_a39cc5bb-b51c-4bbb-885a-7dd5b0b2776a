// OpenAI API服务
// 提供OpenAI API集成，包括文本生成、图像生成、错误处理和重试机制

import OpenAI from 'openai'
import { config } from '../config'
import { logger } from '../utils/logger'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'

/**
 * OpenAI文本生成请求接口
 */
export interface TextGenerationRequest {
  prompt: string
  model?: string
  maxTokens?: number
  temperature?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  systemMessage?: string
}

/**
 * OpenAI图像生成请求接口
 */
export interface ImageGenerationRequest {
  prompt: string
  model?: string
  size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792'
  quality?: 'standard' | 'hd'
  style?: 'vivid' | 'natural'
  n?: number
}

/**
 * OpenAI API响应接口
 */
export interface OpenAIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model?: string
  requestId?: string
}

/**
 * OpenAI服务类
 */
export class OpenAIService {
  private client: OpenAI
  private rateLimitCache = new Map<string, number>()

  constructor() {
    this.client = new OpenAI({
      apiKey: config.openai.apiKey,
      baseURL: config.openai.baseUrl,
      timeout: config.openai.timeout,
      maxRetries: config.openai.maxRetries
    })
  }

  /**
   * 生成文本内容
   */
  async generateText(request: TextGenerationRequest, userId?: string): Promise<OpenAIResponse<string>> {
    const requestId = this.generateRequestId()
    
    logger.info('OpenAI文本生成请求开始', {
      requestId,
      userId,
      model: request.model || config.openai.defaultTextModel,
      promptLength: request.prompt.length
    })

    try {
      // 检查速率限制
      await this.checkRateLimit(userId, 'text')

      // 构建消息
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = []
      
      if (request.systemMessage) {
        messages.push({
          role: 'system',
          content: request.systemMessage
        })
      }
      
      messages.push({
        role: 'user',
        content: request.prompt
      })

      // 调用OpenAI API
      const response = await this.client.chat.completions.create({
        model: request.model || config.openai.defaultTextModel,
        messages,
        max_tokens: request.maxTokens || 2000,
        temperature: request.temperature ?? 0.7,
        top_p: request.topP ?? 1,
        frequency_penalty: request.frequencyPenalty ?? 0,
        presence_penalty: request.presencePenalty ?? 0,
        stream: false
      })

      const generatedText = response.choices[0]?.message?.content || ''
      
      // 记录使用量
      await this.recordUsage(userId, 'text', response.usage)

      // 更新速率限制
      await this.updateRateLimit(userId, 'text')

      logger.info('OpenAI文本生成请求成功', {
        requestId,
        userId,
        model: response.model,
        usage: response.usage,
        outputLength: generatedText.length
      })

      return {
        success: true,
        data: generatedText,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens
        } : undefined,
        model: response.model,
        requestId
      }
    } catch (error) {
      logger.error('OpenAI文本生成请求失败', {
        requestId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })

      return this.handleError(error, requestId)
    }
  }

  /**
   * 生成图像内容
   */
  async generateImage(request: ImageGenerationRequest, userId?: string): Promise<OpenAIResponse<string[]>> {
    const requestId = this.generateRequestId()
    
    logger.info('OpenAI图像生成请求开始', {
      requestId,
      userId,
      model: request.model || config.openai.defaultImageModel,
      promptLength: request.prompt.length,
      size: request.size || '1024x1024'
    })

    try {
      // 检查速率限制
      await this.checkRateLimit(userId, 'image')

      // 调用OpenAI API
      const response = await this.client.images.generate({
        model: request.model || config.openai.defaultImageModel,
        prompt: request.prompt,
        size: request.size || '1024x1024',
        quality: request.quality || 'standard',
        style: request.style || 'vivid',
        n: request.n || 1,
        response_format: 'url'
      })

      const imageUrls = response.data.map(item => item.url).filter(Boolean) as string[]

      // 记录使用量
      await this.recordUsage(userId, 'image', { 
        prompt_tokens: 0, 
        completion_tokens: 0, 
        total_tokens: imageUrls.length 
      })

      // 更新速率限制
      await this.updateRateLimit(userId, 'image')

      logger.info('OpenAI图像生成请求成功', {
        requestId,
        userId,
        imageCount: imageUrls.length,
        size: request.size || '1024x1024'
      })

      return {
        success: true,
        data: imageUrls,
        model: request.model || config.openai.defaultImageModel,
        requestId
      }
    } catch (error) {
      logger.error('OpenAI图像生成请求失败', {
        requestId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })

      return this.handleError(error, requestId)
    }
  }

  /**
   * 检查内容是否违规
   */
  async moderateContent(content: string): Promise<OpenAIResponse<boolean>> {
    const requestId = this.generateRequestId()
    
    logger.debug('OpenAI内容审核请求开始', {
      requestId,
      contentLength: content.length
    })

    try {
      const response = await this.client.moderations.create({
        input: content
      })

      const flagged = response.results[0]?.flagged || false
      const categories = response.results[0]?.categories || {}

      logger.debug('OpenAI内容审核请求完成', {
        requestId,
        flagged,
        categories
      })

      return {
        success: true,
        data: flagged,
        requestId
      }
    } catch (error) {
      logger.error('OpenAI内容审核请求失败', {
        requestId,
        error: error instanceof Error ? error.message : String(error)
      })

      return this.handleError(error, requestId)
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(): Promise<OpenAIResponse<string[]>> {
    const requestId = this.generateRequestId()
    
    try {
      // 先从缓存获取
      const cacheKey = 'openai_models'
      const cachedModels = await redis.get<string[]>(cacheKey)
      
      if (cachedModels) {
        return {
          success: true,
          data: cachedModels,
          requestId
        }
      }

      // 从API获取
      const response = await this.client.models.list()
      const models = response.data
        .filter(model => model.id.includes('gpt') || model.id.includes('dall-e'))
        .map(model => model.id)
        .sort()

      // 缓存结果（1小时）
      await redis.set(cacheKey, models, 3600)

      logger.info('获取OpenAI模型列表成功', {
        requestId,
        modelCount: models.length
      })

      return {
        success: true,
        data: models,
        requestId
      }
    } catch (error) {
      logger.error('获取OpenAI模型列表失败', {
        requestId,
        error: error instanceof Error ? error.message : String(error)
      })

      return this.handleError(error, requestId)
    }
  }

  /**
   * 检查速率限制
   */
  private async checkRateLimit(userId: string | undefined, type: 'text' | 'image'): Promise<void> {
    if (!userId) return

    const key = `openai_rate_limit:${userId}:${type}`
    const current = await redis.get(key)
    
    const limits = {
      text: config.openai.rateLimits.textPerHour,
      image: config.openai.rateLimits.imagePerHour
    }

    if (current && parseInt(current) >= limits[type]) {
      throw new ApiError(
        ErrorCodes.TOO_MANY_REQUESTS,
        `${type === 'text' ? '文本' : '图像'}生成请求过于频繁，请稍后再试`
      )
    }
  }

  /**
   * 更新速率限制
   */
  private async updateRateLimit(userId: string | undefined, type: 'text' | 'image'): Promise<void> {
    if (!userId) return

    const key = `openai_rate_limit:${userId}:${type}`
    await redis.incr(key)
    await redis.expire(key, 3600) // 1小时过期
  }

  /**
   * 记录API使用量
   */
  private async recordUsage(
    userId: string | undefined, 
    type: 'text' | 'image', 
    usage: any
  ): Promise<void> {
    if (!userId) return

    try {
      const usageData = {
        userId,
        type,
        usage,
        timestamp: new Date().toISOString()
      }

      // 记录到Redis列表（用于统计）
      await redis.lpush('openai_usage_log', JSON.stringify(usageData))
      await redis.ltrim('openai_usage_log', 0, 9999) // 保持最近10000条记录

      // 更新用户总使用量
      const dailyKey = `openai_usage:${userId}:${new Date().toISOString().split('T')[0]}`
      await redis.hincrby(dailyKey, `${type}_requests`, 1)
      
      if (usage.total_tokens) {
        await redis.hincrby(dailyKey, `${type}_tokens`, usage.total_tokens)
      }
      
      await redis.expire(dailyKey, 86400 * 7) // 7天过期
    } catch (error) {
      logger.warn('记录OpenAI使用量失败', {
        userId,
        type,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 处理API错误
   */
  private handleError(error: any, requestId: string): OpenAIResponse {
    let errorMessage = 'OpenAI API请求失败'
    let errorCode = ErrorCodes.EXTERNAL_SERVICE_ERROR

    if (error instanceof OpenAI.APIError) {
      switch (error.status) {
        case 400:
          errorMessage = '请求参数无效'
          errorCode = ErrorCodes.VALIDATION_ERROR
          break
        case 401:
          errorMessage = 'API密钥无效'
          errorCode = ErrorCodes.UNAUTHORIZED
          break
        case 403:
          errorMessage = '访问被拒绝'
          errorCode = ErrorCodes.FORBIDDEN
          break
        case 429:
          errorMessage = '请求过于频繁，请稍后再试'
          errorCode = ErrorCodes.TOO_MANY_REQUESTS
          break
        case 500:
        case 502:
        case 503:
          errorMessage = 'OpenAI服务暂时不可用'
          break
        default:
          errorMessage = error.message || 'OpenAI API请求失败'
      }
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请重试'
    } else if (error.code === 'ENOTFOUND') {
      errorMessage = '网络连接失败'
    }

    return {
      success: false,
      error: errorMessage,
      requestId
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `openai_${Date.now()}_${Math.random().toString(36).substring(2)}`
  }

  /**
   * 获取用户使用统计
   */
  async getUserUsageStats(userId: string, days: number = 7): Promise<any> {
    try {
      const stats: any = {
        totalRequests: 0,
        totalTokens: 0,
        textRequests: 0,
        textTokens: 0,
        imageRequests: 0,
        imageTokens: 0,
        dailyStats: []
      }

      for (let i = 0; i < days; i++) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateKey = date.toISOString().split('T')[0]
        const key = `openai_usage:${userId}:${dateKey}`
        
        const dayStats = await redis.hgetall(key)
        
        const textRequests = parseInt(dayStats.text_requests || '0')
        const textTokens = parseInt(dayStats.text_tokens || '0')
        const imageRequests = parseInt(dayStats.image_requests || '0')
        const imageTokens = parseInt(dayStats.image_tokens || '0')

        stats.totalRequests += textRequests + imageRequests
        stats.totalTokens += textTokens + imageTokens
        stats.textRequests += textRequests
        stats.textTokens += textTokens
        stats.imageRequests += imageRequests
        stats.imageTokens += imageTokens

        stats.dailyStats.push({
          date: dateKey,
          textRequests,
          textTokens,
          imageRequests,
          imageTokens,
          totalRequests: textRequests + imageRequests,
          totalTokens: textTokens + imageTokens
        })
      }

      stats.dailyStats.reverse() // 按时间正序排列

      return stats
    } catch (error) {
      logger.error('获取用户OpenAI使用统计失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取使用统计失败')
    }
  }
}

// 创建全局OpenAI服务实例
export const openaiService = new OpenAIService()
