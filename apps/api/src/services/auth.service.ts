// 用户认证服务
// 处理用户注册、登录、登出、密码重置等认证相关业务逻辑

import bcrypt from 'bcrypt'
import crypto from 'crypto'
import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { generateTokens, createSession, destroySession } from '../middleware/auth'
import { ApiError, ErrorCodes } from '../utils/response'
import { DataSanitizer, validateData, CommonSchemas, ValidationRules } from '../utils/validation'
import { z } from 'zod'
import jwt from 'jsonwebtoken'
import { logger } from '../utils/logger'
import { emailService } from './email.service'
import { config } from '../config'

/**
 * 用户注册接口
 */
export interface RegisterUserData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone?: string
}

/**
 * 用户登录接口
 */
export interface LoginUserData {
  email: string
  password: string
  rememberMe?: boolean
}

/**
 * 密码重置接口
 */
export interface ResetPasswordData {
  token: string
  newPassword: string
}

/**
 * 认证服务类
 */
export class AuthService {
  /**
   * 用户注册
   */
  static async register(userData: RegisterUserData) {
    logger.info('用户注册开始', { email: userData.email })

    // 验证输入数据
    const validatedData = validateData(CommonSchemas.userRegister, userData)

    // 清理邮箱
    const email = DataSanitizer.sanitizeEmail(validatedData.email)

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      logger.warn('用户注册失败：邮箱已存在', { email })
      throw new ApiError(ErrorCodes.USER_ALREADY_EXISTS, '该邮箱已被注册')
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(validatedData.password, config.security.bcryptRounds)

    // 生成邮箱验证令牌
    const emailVerificationToken = crypto.randomBytes(32).toString('hex')

    try {
      // 创建用户
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName: DataSanitizer.sanitizeText(validatedData.firstName),
          lastName: DataSanitizer.sanitizeText(validatedData.lastName),
          phone: validatedData.phone ? DataSanitizer.sanitizePhone(validatedData.phone) : null,
          role: 'USER',
          status: 'ACTIVE',
          emailVerified: false,
          emailVerificationToken,
          timezone: 'Asia/Shanghai',
          language: 'zh-CN'
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true,
          emailVerified: true,
          createdAt: true
        }
      })

      // 发送邮箱验证邮件
      await this.sendEmailVerification(user.email, emailVerificationToken)

      // 记录注册事件
      await this.recordAuthEvent('register', user.id, {
        email: user.email,
        success: true
      })

      logger.info('用户注册成功', {
        userId: user.id,
        email: user.email
      })

      return {
        user,
        message: '注册成功，请检查邮箱并验证邮箱地址'
      }
    } catch (error) {
      logger.error('用户注册失败', {
        email,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '注册失败，请稍后重试')
    }
  }

  /**
   * 用户登录
   */
  static async login(loginData: LoginUserData) {
    logger.info('用户登录开始', { email: loginData.email })

    // 验证输入数据
    const validatedData = validateData(CommonSchemas.userLogin, loginData)

    // 清理邮箱
    const email = DataSanitizer.sanitizeEmail(validatedData.email)

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        emailVerified: true,
        loginAttempts: true,
        lockedUntil: true
      }
    })

    if (!user) {
      await this.recordAuthEvent('login_failed', null, {
        email,
        reason: 'user_not_found'
      })
      throw new ApiError(ErrorCodes.INVALID_CREDENTIALS, '邮箱或密码错误')
    }

    // 检查账户是否被锁定
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      const lockTimeRemaining = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 1000 / 60)
      await this.recordAuthEvent('login_failed', user.id, {
        email,
        reason: 'account_locked',
        lockTimeRemaining
      })
      throw new ApiError(
        ErrorCodes.ACCOUNT_SUSPENDED,
        `账户已被锁定，请在 ${lockTimeRemaining} 分钟后重试`
      )
    }

    // 检查用户状态
    if (user.status !== 'ACTIVE') {
      await this.recordAuthEvent('login_failed', user.id, {
        email,
        reason: 'account_inactive',
        status: user.status
      })
      throw new ApiError(ErrorCodes.ACCOUNT_SUSPENDED, '账户已被暂停')
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(validatedData.password, user.password)

    if (!isPasswordValid) {
      // 增加登录失败次数
      await this.handleFailedLogin(user.id, email)
      throw new ApiError(ErrorCodes.INVALID_CREDENTIALS, '邮箱或密码错误')
    }

    // 重置登录失败次数
    if (user.loginAttempts > 0) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          loginAttempts: 0,
          lockedUntil: null
        }
      })
    }

    // 生成JWT令牌
    const tokens = generateTokens({
      id: user.id,
      email: user.email,
      role: user.role
    })

    // 创建会话
    await createSession(user.id, tokens.sessionId, {
      userAgent: '', // 这里应该从请求中获取
      ip: '', // 这里应该从请求中获取
      rememberMe: validatedData.rememberMe || false
    })

    // 更新最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLoginAt: new Date(),
        lastLoginIp: '' // 这里应该从请求中获取
      }
    })

    // 记录登录事件
    await this.recordAuthEvent('login_success', user.id, {
      email: user.email,
      sessionId: tokens.sessionId
    })

    logger.info('用户登录成功', {
      userId: user.id,
      email: user.email,
      sessionId: tokens.sessionId
    })

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        emailVerified: user.emailVerified
      },
      tokens: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken
      },
      sessionId: tokens.sessionId
    }
  }

  /**
   * 用户登出
   */
  static async logout(sessionId: string, userId: string) {
    logger.info('用户登出开始', { userId, sessionId })

    try {
      // 销毁会话
      await destroySession(sessionId)

      // 记录登出事件
      await this.recordAuthEvent('logout', userId, {
        sessionId
      })

      logger.info('用户登出成功', { userId, sessionId })

      return { message: '登出成功' }
    } catch (error) {
      logger.error('用户登出失败', {
        userId,
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '登出失败')
    }
  }

  /**
   * 刷新令牌
   */
  static async refreshToken(refreshToken: string) {
    logger.info('刷新令牌开始')

    try {
      // 验证刷新令牌
      const payload = jwt.verify(refreshToken, config.jwt.refreshSecret) as any

      if (payload.type !== 'refresh') {
        throw new ApiError(ErrorCodes.TOKEN_INVALID, '无效的刷新令牌')
      }

      // 检查会话是否有效
      const sessionKey = `session:${payload.sessionId}`
      const sessionData = await redis.get(sessionKey)

      if (!sessionData) {
        throw new ApiError(ErrorCodes.TOKEN_INVALID, '会话已失效')
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          role: true,
          status: true
        }
      })

      if (!user || user.status !== 'ACTIVE') {
        throw new ApiError(ErrorCodes.USER_NOT_FOUND, '用户不存在或已被禁用')
      }

      // 生成新的访问令牌
      const newTokens = generateTokens({
        id: user.id,
        email: user.email,
        role: user.role
      })

      logger.info('令牌刷新成功', {
        userId: user.id,
        oldSessionId: payload.sessionId,
        newSessionId: newTokens.sessionId
      })

      return {
        accessToken: newTokens.accessToken,
        refreshToken: newTokens.refreshToken
      }
    } catch (error) {
      logger.error('令牌刷新失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      
      if (error instanceof ApiError) {
        throw error
      }
      
      throw new ApiError(ErrorCodes.TOKEN_INVALID, '令牌刷新失败')
    }
  }

  /**
   * 发送密码重置邮件
   */
  static async sendPasswordResetEmail(email: string) {
    logger.info('发送密码重置邮件', { email })

    // 清理邮箱
    const cleanEmail = DataSanitizer.sanitizeEmail(email)

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email: cleanEmail },
      select: { id: true, email: true, firstName: true }
    })

    if (!user) {
      // 为了安全，即使用户不存在也返回成功消息
      return { message: '如果该邮箱已注册，您将收到密码重置邮件' }
    }

    // 生成重置令牌
    const resetToken = crypto.randomBytes(32).toString('hex')
    const resetTokenExpiry = new Date(Date.now() + 3600000) // 1小时后过期

    // 保存重置令牌
    await prisma.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: resetToken,
        passwordResetExpiry: resetTokenExpiry
      }
    })

    // 发送重置邮件
    await emailService.sendPasswordResetEmail(user.email, user.firstName, resetToken)

    // 记录事件
    await this.recordAuthEvent('password_reset_requested', user.id, {
      email: user.email
    })

    logger.info('密码重置邮件发送成功', {
      userId: user.id,
      email: user.email
    })

    return { message: '密码重置邮件已发送，请检查您的邮箱' }
  }

  /**
   * 重置密码
   */
  static async resetPassword(resetData: ResetPasswordData) {
    logger.info('密码重置开始')

    const { token, newPassword } = resetData

    // 验证新密码
    validateData(z.object({ password: ValidationRules.password }), { password: newPassword })

    // 查找用户
    const user = await prisma.user.findFirst({
      where: {
        passwordResetToken: token,
        passwordResetExpiry: {
          gt: new Date()
        }
      },
      select: {
        id: true,
        email: true
      }
    })

    if (!user) {
      throw new ApiError(ErrorCodes.TOKEN_INVALID, '重置令牌无效或已过期')
    }

    // 加密新密码
    const hashedPassword = await bcrypt.hash(newPassword, config.security.bcryptRounds)

    // 更新密码并清除重置令牌
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpiry: null,
        passwordChangedAt: new Date()
      }
    })

    // 记录事件
    await this.recordAuthEvent('password_reset_completed', user.id, {
      email: user.email
    })

    logger.info('密码重置成功', {
      userId: user.id,
      email: user.email
    })

    return { message: '密码重置成功，请使用新密码登录' }
  }

  /**
   * 处理登录失败
   */
  private static async handleFailedLogin(userId: string, email: string) {
    const maxAttempts = 5
    const lockDuration = 30 * 60 * 1000 // 30分钟

    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        loginAttempts: {
          increment: 1
        }
      },
      select: {
        loginAttempts: true
      }
    })

    // 记录失败事件
    await this.recordAuthEvent('login_failed', userId, {
      email,
      reason: 'invalid_password',
      attempts: user.loginAttempts
    })

    // 如果达到最大尝试次数，锁定账户
    if (user.loginAttempts >= maxAttempts) {
      await prisma.user.update({
        where: { id: userId },
        data: {
          lockedUntil: new Date(Date.now() + lockDuration)
        }
      })

      logger.warn('账户因多次登录失败被锁定', {
        userId,
        email,
        attempts: user.loginAttempts
      })
    }
  }

  /**
   * 发送邮箱验证邮件
   */
  private static async sendEmailVerification(email: string, token: string) {
    try {
      await emailService.sendEmailVerification(email, token)
    } catch (error) {
      logger.error('发送邮箱验证邮件失败', {
        email,
        error: error instanceof Error ? error.message : String(error)
      })
      // 不抛出错误，因为用户注册已经成功
    }
  }

  /**
   * 记录认证事件
   */
  private static async recordAuthEvent(
    event: string,
    userId: string | null,
    metadata: any
  ) {
    try {
      await redis.lpush('auth_events', JSON.stringify({
        event,
        userId,
        metadata,
        timestamp: new Date().toISOString()
      }))

      // 保持最近1000条记录
      await redis.ltrim('auth_events', 0, 999)
    } catch (error) {
      logger.error('记录认证事件失败', {
        event,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }
}
