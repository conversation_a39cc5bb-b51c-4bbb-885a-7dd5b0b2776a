// 数据分析服务
// 提供数据收集、存储、分析和报告功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'

/**
 * 事件类型
 */
export enum EventType {
  PAGE_VIEW = 'PAGE_VIEW',
  BUTTON_CLICK = 'BUTTON_CLICK',
  FORM_SUBMIT = 'FORM_SUBMIT',
  EMAIL_OPEN = 'EMAIL_OPEN',
  EMAIL_CLICK = 'EMAIL_CLICK',
  PURCHASE = 'PURCHASE',
  SIGNUP = 'SIGNUP',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  SEARCH = 'SEARCH',
  DOWNLOAD = 'DOWNLOAD',
  SHARE = 'SHARE',
  CUSTOM = 'CUSTOM'
}

/**
 * 数据源类型
 */
export enum DataSourceType {
  WEB_ANALYTICS = 'WEB_ANALYTICS',
  EMAIL_MARKETING = 'EMAIL_MARKETING',
  SOCIAL_MEDIA = 'SOCIAL_MEDIA',
  ADVERTISING = 'ADVERTISING',
  CRM = 'CRM',
  ECOMMERCE = 'ECOMMERCE',
  MOBILE_APP = 'MOBILE_APP',
  CUSTOM_API = 'CUSTOM_API'
}

/**
 * 事件数据接口
 */
export interface EventData {
  type: EventType
  userId?: string
  sessionId?: string
  timestamp: Date
  properties: Record<string, any>
  context: {
    userAgent?: string
    ip?: string
    referrer?: string
    url?: string
    device?: {
      type: 'desktop' | 'mobile' | 'tablet'
      os: string
      browser: string
    }
    location?: {
      country?: string
      region?: string
      city?: string
    }
  }
  source: DataSourceType
  campaignId?: string
  metadata?: Record<string, any>
}

/**
 * 分析查询选项
 */
export interface AnalyticsQueryOptions {
  startDate: Date
  endDate: Date
  userId?: string
  eventTypes?: EventType[]
  sources?: DataSourceType[]
  campaignId?: string
  groupBy?: 'hour' | 'day' | 'week' | 'month'
  metrics?: string[]
  filters?: {
    field: string
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
    value: any
  }[]
  limit?: number
  offset?: number
}

/**
 * 分析结果接口
 */
export interface AnalyticsResult {
  data: {
    timestamp: string
    metrics: Record<string, number>
  }[]
  summary: {
    totalEvents: number
    uniqueUsers: number
    totalSessions: number
    averageSessionDuration: number
    bounceRate: number
    conversionRate: number
  }
  trends: {
    metric: string
    change: number
    changePercent: number
    trend: 'up' | 'down' | 'stable'
  }[]
}

/**
 * 数据分析服务类
 */
export class AnalyticsService {
  /**
   * 记录事件
   */
  static async trackEvent(eventData: EventData): Promise<void> {
    logger.debug('记录分析事件', {
      type: eventData.type,
      userId: eventData.userId,
      source: eventData.source
    })

    try {
      // 数据验证和清理
      const cleanEventData = this.validateAndCleanEventData(eventData)

      // 存储到数据库
      await prisma.analyticsEvent.create({
        data: {
          type: cleanEventData.type,
          userId: cleanEventData.userId,
          sessionId: cleanEventData.sessionId,
          timestamp: cleanEventData.timestamp,
          properties: cleanEventData.properties,
          context: cleanEventData.context,
          source: cleanEventData.source,
          campaignId: cleanEventData.campaignId,
          metadata: cleanEventData.metadata
        }
      })

      // 实时数据缓存更新
      await this.updateRealTimeMetrics(cleanEventData)

      // 异步处理数据聚合
      await this.processEventAggregation(cleanEventData)

      logger.debug('分析事件记录成功', {
        type: eventData.type,
        userId: eventData.userId
      })
    } catch (error) {
      logger.error('记录分析事件失败', {
        type: eventData.type,
        userId: eventData.userId,
        error: error instanceof Error ? error.message : String(error)
      })
      // 不抛出错误，避免影响主业务流程
    }
  }

  /**
   * 批量记录事件
   */
  static async trackEvents(events: EventData[]): Promise<void> {
    logger.debug('批量记录分析事件', { count: events.length })

    try {
      // 验证和清理数据
      const cleanEvents = events.map(event => this.validateAndCleanEventData(event))

      // 批量插入数据库
      await prisma.analyticsEvent.createMany({
        data: cleanEvents.map(event => ({
          type: event.type,
          userId: event.userId,
          sessionId: event.sessionId,
          timestamp: event.timestamp,
          properties: event.properties,
          context: event.context,
          source: event.source,
          campaignId: event.campaignId,
          metadata: event.metadata
        }))
      })

      // 批量更新实时指标
      for (const event of cleanEvents) {
        await this.updateRealTimeMetrics(event)
        await this.processEventAggregation(event)
      }

      logger.debug('批量分析事件记录成功', { count: events.length })
    } catch (error) {
      logger.error('批量记录分析事件失败', {
        count: events.length,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 查询分析数据
   */
  static async queryAnalytics(
    userId: string,
    options: AnalyticsQueryOptions
  ): Promise<AnalyticsResult> {
    logger.info('查询分析数据', { userId, options })

    try {
      // 构建查询条件
      const where = this.buildWhereClause(userId, options)

      // 查询事件数据
      const events = await prisma.analyticsEvent.findMany({
        where,
        orderBy: { timestamp: 'asc' },
        take: options.limit || 10000,
        skip: options.offset || 0
      })

      // 处理数据聚合
      const aggregatedData = this.aggregateEventData(events, options)

      // 计算汇总指标
      const summary = await this.calculateSummaryMetrics(userId, options)

      // 计算趋势
      const trends = await this.calculateTrends(userId, options)

      logger.info('分析数据查询成功', {
        userId,
        eventCount: events.length,
        dataPoints: aggregatedData.length
      })

      return {
        data: aggregatedData,
        summary,
        trends
      }
    } catch (error) {
      logger.error('查询分析数据失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '查询分析数据失败')
    }
  }

  /**
   * 获取实时指标
   */
  static async getRealTimeMetrics(userId: string): Promise<Record<string, number>> {
    try {
      const cacheKey = `realtime_metrics:${userId}`
      const metrics = await redis.hgetall(cacheKey)

      // 转换为数字类型
      const result: Record<string, number> = {}
      for (const [key, value] of Object.entries(metrics)) {
        result[key] = parseInt(value) || 0
      }

      return result
    } catch (error) {
      logger.error('获取实时指标失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      return {}
    }
  }

  /**
   * 获取用户行为路径
   */
  static async getUserJourney(
    userId: string,
    sessionId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    events: any[]
    sessions: any[]
    conversionFunnel: any[]
  }> {
    try {
      const where: any = { userId }

      if (sessionId) where.sessionId = sessionId
      if (startDate || endDate) {
        where.timestamp = {}
        if (startDate) where.timestamp.gte = startDate
        if (endDate) where.timestamp.lte = endDate
      }

      // 获取用户事件
      const events = await prisma.analyticsEvent.findMany({
        where,
        orderBy: { timestamp: 'asc' },
        take: 1000
      })

      // 分析会话
      const sessions = this.analyzeUserSessions(events)

      // 构建转化漏斗
      const conversionFunnel = this.buildConversionFunnel(events)

      return {
        events,
        sessions,
        conversionFunnel
      }
    } catch (error) {
      logger.error('获取用户行为路径失败', {
        userId,
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取用户行为路径失败')
    }
  }

  /**
   * 获取热力图数据
   */
  static async getHeatmapData(
    userId: string,
    url: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    clicks: { x: number; y: number; count: number }[]
    scrollDepth: { depth: number; count: number }[]
    timeOnPage: { duration: number; count: number }[]
  }> {
    try {
      // 查询点击事件
      const clickEvents = await prisma.analyticsEvent.findMany({
        where: {
          userId,
          type: EventType.BUTTON_CLICK,
          'context.url': url,
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        }
      })

      // 处理点击热力图数据
      const clicks = this.processClickHeatmap(clickEvents)

      // 查询页面浏览事件
      const pageViews = await prisma.analyticsEvent.findMany({
        where: {
          userId,
          type: EventType.PAGE_VIEW,
          'context.url': url,
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        }
      })

      // 处理滚动深度和停留时间
      const scrollDepth = this.processScrollDepth(pageViews)
      const timeOnPage = this.processTimeOnPage(pageViews)

      return {
        clicks,
        scrollDepth,
        timeOnPage
      }
    } catch (error) {
      logger.error('获取热力图数据失败', {
        userId,
        url,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取热力图数据失败')
    }
  }

  /**
   * 验证和清理事件数据
   */
  private static validateAndCleanEventData(eventData: EventData): EventData {
    return {
      type: eventData.type,
      userId: eventData.userId,
      sessionId: eventData.sessionId || this.generateSessionId(),
      timestamp: eventData.timestamp || new Date(),
      properties: eventData.properties || {},
      context: {
        ...eventData.context,
        userAgent: eventData.context.userAgent?.substring(0, 500),
        ip: this.anonymizeIP(eventData.context.ip),
        referrer: eventData.context.referrer?.substring(0, 500),
        url: eventData.context.url?.substring(0, 500)
      },
      source: eventData.source,
      campaignId: eventData.campaignId,
      metadata: eventData.metadata || {}
    }
  }

  /**
   * 更新实时指标
   */
  private static async updateRealTimeMetrics(eventData: EventData): Promise<void> {
    try {
      const cacheKey = `realtime_metrics:${eventData.userId || 'anonymous'}`
      const today = new Date().toISOString().split('T')[0]
      const hourKey = `${today}:${new Date().getHours()}`

      // 更新各种指标
      await redis.hincrby(cacheKey, 'total_events', 1)
      await redis.hincrby(cacheKey, `events_${eventData.type.toLowerCase()}`, 1)
      await redis.hincrby(cacheKey, `hourly_${hourKey}`, 1)

      // 设置过期时间（7天）
      await redis.expire(cacheKey, 604800)

      // 更新全局实时指标
      const globalKey = 'realtime_metrics:global'
      await redis.hincrby(globalKey, 'total_events', 1)
      await redis.hincrby(globalKey, `events_${eventData.type.toLowerCase()}`, 1)
      await redis.expire(globalKey, 604800)
    } catch (error) {
      logger.warn('更新实时指标失败', {
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 处理事件聚合
   */
  private static async processEventAggregation(eventData: EventData): Promise<void> {
    try {
      // 这里可以实现更复杂的数据聚合逻辑
      // 例如：计算转化率、用户留存率等
      
      // 示例：更新用户活跃度
      if (eventData.userId) {
        const userActivityKey = `user_activity:${eventData.userId}`
        const today = new Date().toISOString().split('T')[0]
        
        await redis.sadd(userActivityKey, today)
        await redis.expire(userActivityKey, 2592000) // 30天过期
      }
    } catch (error) {
      logger.warn('处理事件聚合失败', {
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 构建查询条件
   */
  private static buildWhereClause(userId: string, options: AnalyticsQueryOptions): any {
    const where: any = {
      timestamp: {
        gte: options.startDate,
        lte: options.endDate
      }
    }

    if (userId !== 'all') {
      where.userId = userId
    }

    if (options.eventTypes && options.eventTypes.length > 0) {
      where.type = { in: options.eventTypes }
    }

    if (options.sources && options.sources.length > 0) {
      where.source = { in: options.sources }
    }

    if (options.campaignId) {
      where.campaignId = options.campaignId
    }

    // 处理自定义过滤器
    if (options.filters && options.filters.length > 0) {
      for (const filter of options.filters) {
        switch (filter.operator) {
          case 'equals':
            where[filter.field] = filter.value
            break
          case 'not_equals':
            where[filter.field] = { not: filter.value }
            break
          case 'contains':
            where[filter.field] = { contains: filter.value }
            break
          case 'greater_than':
            where[filter.field] = { gt: filter.value }
            break
          case 'less_than':
            where[filter.field] = { lt: filter.value }
            break
        }
      }
    }

    return where
  }

  /**
   * 聚合事件数据
   */
  private static aggregateEventData(events: any[], options: AnalyticsQueryOptions): any[] {
    const groupBy = options.groupBy || 'day'
    const metrics = options.metrics || ['count', 'unique_users']

    // 按时间分组
    const grouped = new Map<string, any[]>()

    for (const event of events) {
      const timestamp = new Date(event.timestamp)
      let key: string

      switch (groupBy) {
        case 'hour':
          key = timestamp.toISOString().substring(0, 13) + ':00:00.000Z'
          break
        case 'day':
          key = timestamp.toISOString().substring(0, 10) + 'T00:00:00.000Z'
          break
        case 'week':
          const weekStart = new Date(timestamp)
          weekStart.setDate(timestamp.getDate() - timestamp.getDay())
          key = weekStart.toISOString().substring(0, 10) + 'T00:00:00.000Z'
          break
        case 'month':
          key = timestamp.toISOString().substring(0, 7) + '-01T00:00:00.000Z'
          break
        default:
          key = timestamp.toISOString().substring(0, 10) + 'T00:00:00.000Z'
      }

      if (!grouped.has(key)) {
        grouped.set(key, [])
      }
      grouped.get(key)!.push(event)
    }

    // 计算指标
    const result: any[] = []
    for (const [timestamp, groupEvents] of grouped) {
      const metricsData: Record<string, number> = {}

      for (const metric of metrics) {
        switch (metric) {
          case 'count':
            metricsData.count = groupEvents.length
            break
          case 'unique_users':
            metricsData.unique_users = new Set(
              groupEvents.map(e => e.userId).filter(Boolean)
            ).size
            break
          case 'sessions':
            metricsData.sessions = new Set(
              groupEvents.map(e => e.sessionId).filter(Boolean)
            ).size
            break
          case 'page_views':
            metricsData.page_views = groupEvents.filter(
              e => e.type === EventType.PAGE_VIEW
            ).length
            break
          case 'conversions':
            metricsData.conversions = groupEvents.filter(
              e => e.type === EventType.PURCHASE || e.type === EventType.SIGNUP
            ).length
            break
        }
      }

      result.push({
        timestamp,
        metrics: metricsData
      })
    }

    return result.sort((a, b) => a.timestamp.localeCompare(b.timestamp))
  }

  /**
   * 计算汇总指标
   */
  private static async calculateSummaryMetrics(
    userId: string,
    options: AnalyticsQueryOptions
  ): Promise<any> {
    const where = this.buildWhereClause(userId, options)

    const [
      totalEvents,
      uniqueUsers,
      totalSessions,
      pageViews,
      conversions
    ] = await Promise.all([
      prisma.analyticsEvent.count({ where }),
      prisma.analyticsEvent.findMany({
        where,
        select: { userId: true },
        distinct: ['userId']
      }).then(users => users.filter(u => u.userId).length),
      prisma.analyticsEvent.findMany({
        where,
        select: { sessionId: true },
        distinct: ['sessionId']
      }).then(sessions => sessions.filter(s => s.sessionId).length),
      prisma.analyticsEvent.count({
        where: { ...where, type: EventType.PAGE_VIEW }
      }),
      prisma.analyticsEvent.count({
        where: { 
          ...where, 
          type: { in: [EventType.PURCHASE, EventType.SIGNUP] }
        }
      })
    ])

    // 计算派生指标
    const bounceRate = totalSessions > 0 ? 
      ((totalSessions - (pageViews - totalSessions)) / totalSessions) * 100 : 0
    const conversionRate = uniqueUsers > 0 ? (conversions / uniqueUsers) * 100 : 0
    const averageSessionDuration = 0 // 需要更复杂的计算

    return {
      totalEvents,
      uniqueUsers,
      totalSessions,
      averageSessionDuration,
      bounceRate: Math.round(bounceRate * 100) / 100,
      conversionRate: Math.round(conversionRate * 100) / 100
    }
  }

  /**
   * 计算趋势
   */
  private static async calculateTrends(
    userId: string,
    options: AnalyticsQueryOptions
  ): Promise<any[]> {
    // 计算当前周期和上一周期的数据对比
    const currentPeriod = options
    const periodDuration = options.endDate.getTime() - options.startDate.getTime()
    const previousPeriod = {
      ...options,
      startDate: new Date(options.startDate.getTime() - periodDuration),
      endDate: new Date(options.endDate.getTime() - periodDuration)
    }

    const [currentMetrics, previousMetrics] = await Promise.all([
      this.calculateSummaryMetrics(userId, currentPeriod),
      this.calculateSummaryMetrics(userId, previousPeriod)
    ])

    const trends = []
    const metricsToCompare = ['totalEvents', 'uniqueUsers', 'conversionRate']

    for (const metric of metricsToCompare) {
      const current = currentMetrics[metric] || 0
      const previous = previousMetrics[metric] || 0
      const change = current - previous
      const changePercent = previous > 0 ? (change / previous) * 100 : 0

      trends.push({
        metric,
        change,
        changePercent: Math.round(changePercent * 100) / 100,
        trend: change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
      })
    }

    return trends
  }

  /**
   * 分析用户会话
   */
  private static analyzeUserSessions(events: any[]): any[] {
    const sessions = new Map<string, any>()

    for (const event of events) {
      if (!event.sessionId) continue

      if (!sessions.has(event.sessionId)) {
        sessions.set(event.sessionId, {
          sessionId: event.sessionId,
          startTime: event.timestamp,
          endTime: event.timestamp,
          events: [],
          pageViews: 0,
          duration: 0
        })
      }

      const session = sessions.get(event.sessionId)!
      session.events.push(event)
      session.endTime = event.timestamp

      if (event.type === EventType.PAGE_VIEW) {
        session.pageViews++
      }
    }

    // 计算会话时长
    for (const session of sessions.values()) {
      session.duration = new Date(session.endTime).getTime() - 
                        new Date(session.startTime).getTime()
    }

    return Array.from(sessions.values())
  }

  /**
   * 构建转化漏斗
   */
  private static buildConversionFunnel(events: any[]): any[] {
    const funnelSteps = [
      { name: '页面访问', type: EventType.PAGE_VIEW },
      { name: '注册', type: EventType.SIGNUP },
      { name: '购买', type: EventType.PURCHASE }
    ]

    const funnel = []
    let previousCount = events.length

    for (const step of funnelSteps) {
      const stepEvents = events.filter(e => e.type === step.type)
      const count = stepEvents.length
      const conversionRate = previousCount > 0 ? (count / previousCount) * 100 : 0

      funnel.push({
        name: step.name,
        count,
        conversionRate: Math.round(conversionRate * 100) / 100
      })

      previousCount = count
    }

    return funnel
  }

  /**
   * 处理点击热力图
   */
  private static processClickHeatmap(events: any[]): any[] {
    const clickMap = new Map<string, number>()

    for (const event of events) {
      const x = event.properties?.x || 0
      const y = event.properties?.y || 0
      const key = `${x},${y}`

      clickMap.set(key, (clickMap.get(key) || 0) + 1)
    }

    return Array.from(clickMap.entries()).map(([key, count]) => {
      const [x, y] = key.split(',').map(Number)
      return { x, y, count }
    })
  }

  /**
   * 处理滚动深度
   */
  private static processScrollDepth(events: any[]): any[] {
    const depthMap = new Map<number, number>()

    for (const event of events) {
      const depth = Math.round((event.properties?.scrollDepth || 0) / 10) * 10
      depthMap.set(depth, (depthMap.get(depth) || 0) + 1)
    }

    return Array.from(depthMap.entries())
      .map(([depth, count]) => ({ depth, count }))
      .sort((a, b) => a.depth - b.depth)
  }

  /**
   * 处理页面停留时间
   */
  private static processTimeOnPage(events: any[]): any[] {
    const timeMap = new Map<number, number>()

    for (const event of events) {
      const duration = Math.round((event.properties?.timeOnPage || 0) / 30) * 30
      timeMap.set(duration, (timeMap.get(duration) || 0) + 1)
    }

    return Array.from(timeMap.entries())
      .map(([duration, count]) => ({ duration, count }))
      .sort((a, b) => a.duration - b.duration)
  }

  /**
   * 生成会话ID
   */
  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`
  }

  /**
   * 匿名化IP地址
   */
  private static anonymizeIP(ip?: string): string | undefined {
    if (!ip) return undefined
    
    // 简单的IP匿名化：将最后一段替换为0
    const parts = ip.split('.')
    if (parts.length === 4) {
      parts[3] = '0'
      return parts.join('.')
    }
    
    return ip
  }
}
