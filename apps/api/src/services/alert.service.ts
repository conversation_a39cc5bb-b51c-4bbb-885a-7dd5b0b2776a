// 告警服务
// 提供多渠道告警通知、告警规则管理、故障恢复等功能

import { logger } from '../utils/logger'
import { redis } from '../utils/redis'
import { emailService } from './email.service'

/**
 * 告警级别
 */
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

/**
 * 告警渠道
 */
export enum AlertChannel {
  EMAIL = 'email',
  WEBHOOK = 'webhook',
  SMS = 'sms',
  SLACK = 'slack',
  DINGTALK = 'dingtalk'
}

/**
 * 告警规则
 */
export interface AlertRule {
  id: string
  name: string
  description: string
  level: AlertLevel
  channels: AlertChannel[]
  conditions: AlertCondition[]
  cooldown: number // 冷却时间（秒）
  enabled: boolean
  recipients: string[]
}

/**
 * 告警条件
 */
export interface AlertCondition {
  metric: string
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte' | 'ne'
  threshold: number
  duration: number // 持续时间（秒）
}

/**
 * 告警事件
 */
export interface AlertEvent {
  id: string
  ruleId: string
  level: AlertLevel
  title: string
  message: string
  details: any
  timestamp: Date
  resolved: boolean
  resolvedAt?: Date
  acknowledgedBy?: string
  acknowledgedAt?: Date
}

/**
 * 告警服务类
 */
export class AlertService {
  private static rules: Map<string, AlertRule> = new Map()
  private static activeAlerts: Map<string, AlertEvent> = new Map()

  /**
   * 初始化告警服务
   */
  static initialize() {
    this.loadDefaultRules()
    this.startAlertProcessor()
    logger.info('告警服务初始化完成')
  }

  /**
   * 加载默认告警规则
   */
  private static loadDefaultRules() {
    const defaultRules: AlertRule[] = [
      {
        id: 'high_error_rate',
        name: '错误率过高',
        description: 'API错误率超过5%',
        level: AlertLevel.ERROR,
        channels: [AlertChannel.EMAIL, AlertChannel.WEBHOOK],
        conditions: [
          {
            metric: 'error_rate',
            operator: 'gt',
            threshold: 5,
            duration: 300
          }
        ],
        cooldown: 1800, // 30分钟
        enabled: true,
        recipients: ['<EMAIL>']
      },
      {
        id: 'high_response_time',
        name: '响应时间过长',
        description: 'API平均响应时间超过2秒',
        level: AlertLevel.WARNING,
        channels: [AlertChannel.EMAIL],
        conditions: [
          {
            metric: 'avg_response_time',
            operator: 'gt',
            threshold: 2000,
            duration: 600
          }
        ],
        cooldown: 3600, // 1小时
        enabled: true,
        recipients: ['<EMAIL>']
      },
      {
        id: 'database_connection_failure',
        name: '数据库连接失败',
        description: '数据库连接失败',
        level: AlertLevel.CRITICAL,
        channels: [AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.WEBHOOK],
        conditions: [
          {
            metric: 'db_connection_errors',
            operator: 'gt',
            threshold: 0,
            duration: 60
          }
        ],
        cooldown: 300, // 5分钟
        enabled: true,
        recipients: ['<EMAIL>', '<EMAIL>']
      },
      {
        id: 'high_memory_usage',
        name: '内存使用率过高',
        description: '系统内存使用率超过90%',
        level: AlertLevel.WARNING,
        channels: [AlertChannel.EMAIL],
        conditions: [
          {
            metric: 'memory_usage_percent',
            operator: 'gt',
            threshold: 90,
            duration: 900
          }
        ],
        cooldown: 1800,
        enabled: true,
        recipients: ['<EMAIL>']
      },
      {
        id: 'disk_space_low',
        name: '磁盘空间不足',
        description: '磁盘使用率超过85%',
        level: AlertLevel.WARNING,
        channels: [AlertChannel.EMAIL, AlertChannel.WEBHOOK],
        conditions: [
          {
            metric: 'disk_usage_percent',
            operator: 'gt',
            threshold: 85,
            duration: 1800
          }
        ],
        cooldown: 3600,
        enabled: true,
        recipients: ['<EMAIL>']
      }
    ]

    defaultRules.forEach(rule => {
      this.rules.set(rule.id, rule)
    })

    logger.info('默认告警规则加载完成', { count: defaultRules.length })
  }

  /**
   * 启动告警处理器
   */
  private static startAlertProcessor() {
    // 每分钟检查一次告警条件
    setInterval(async () => {
      try {
        await this.processAlerts()
      } catch (error) {
        logger.error('告警处理失败', { error })
      }
    }, 60000)

    // 每小时清理过期的告警
    setInterval(async () => {
      try {
        await this.cleanupExpiredAlerts()
      } catch (error) {
        logger.error('清理过期告警失败', { error })
      }
    }, 3600000)
  }

  /**
   * 处理告警
   */
  private static async processAlerts() {
    for (const [ruleId, rule] of this.rules) {
      if (!rule.enabled) continue

      try {
        const shouldAlert = await this.evaluateRule(rule)
        if (shouldAlert) {
          await this.triggerAlert(rule)
        }
      } catch (error) {
        logger.error('评估告警规则失败', {
          ruleId,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }
  }

  /**
   * 评估告警规则
   */
  private static async evaluateRule(rule: AlertRule): Promise<boolean> {
    for (const condition of rule.conditions) {
      const isTriggered = await this.evaluateCondition(condition)
      if (!isTriggered) {
        return false
      }
    }
    return true
  }

  /**
   * 评估单个条件
   */
  private static async evaluateCondition(condition: AlertCondition): Promise<boolean> {
    try {
      // 从Redis获取指标数据
      const metricKey = `metrics:${condition.metric}`
      const values = await redis.lrange(metricKey, 0, -1)
      
      if (values.length === 0) {
        return false
      }

      // 计算平均值
      const numericValues = values.map(v => parseFloat(v)).filter(v => !isNaN(v))
      if (numericValues.length === 0) {
        return false
      }

      const avgValue = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length

      // 评估条件
      switch (condition.operator) {
        case 'gt':
          return avgValue > condition.threshold
        case 'gte':
          return avgValue >= condition.threshold
        case 'lt':
          return avgValue < condition.threshold
        case 'lte':
          return avgValue <= condition.threshold
        case 'eq':
          return avgValue === condition.threshold
        case 'ne':
          return avgValue !== condition.threshold
        default:
          return false
      }
    } catch (error) {
      logger.error('评估告警条件失败', {
        condition,
        error: error instanceof Error ? error.message : String(error)
      })
      return false
    }
  }

  /**
   * 触发告警
   */
  private static async triggerAlert(rule: AlertRule) {
    const alertId = `${rule.id}_${Date.now()}`
    
    // 检查冷却时间
    const lastAlertKey = `alert:last:${rule.id}`
    const lastAlertTime = await redis.get(lastAlertKey)
    
    if (lastAlertTime) {
      const timeSinceLastAlert = Date.now() - parseInt(lastAlertTime)
      if (timeSinceLastAlert < rule.cooldown * 1000) {
        return // 还在冷却期内
      }
    }

    // 创建告警事件
    const alertEvent: AlertEvent = {
      id: alertId,
      ruleId: rule.id,
      level: rule.level,
      title: rule.name,
      message: rule.description,
      details: {
        rule,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date(),
      resolved: false
    }

    // 保存告警事件
    this.activeAlerts.set(alertId, alertEvent)
    await redis.setex(`alert:${alertId}`, 86400, JSON.stringify(alertEvent))

    // 发送告警通知
    for (const channel of rule.channels) {
      try {
        await this.sendAlert(channel, alertEvent, rule.recipients)
      } catch (error) {
        logger.error('发送告警失败', {
          channel,
          alertId,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    // 更新最后告警时间
    await redis.set(lastAlertKey, Date.now().toString())

    logger.warn('告警已触发', {
      alertId,
      ruleId: rule.id,
      level: rule.level,
      title: rule.name
    })
  }

  /**
   * 发送告警通知
   */
  private static async sendAlert(
    channel: AlertChannel,
    alert: AlertEvent,
    recipients: string[]
  ) {
    switch (channel) {
      case AlertChannel.EMAIL:
        await this.sendEmailAlert(alert, recipients)
        break
      case AlertChannel.WEBHOOK:
        await this.sendWebhookAlert(alert)
        break
      case AlertChannel.SMS:
        await this.sendSMSAlert(alert, recipients)
        break
      case AlertChannel.SLACK:
        await this.sendSlackAlert(alert)
        break
      case AlertChannel.DINGTALK:
        await this.sendDingTalkAlert(alert)
        break
      default:
        logger.warn('未知的告警渠道', { channel })
    }
  }

  /**
   * 发送邮件告警
   */
  private static async sendEmailAlert(alert: AlertEvent, recipients: string[]) {
    const subject = `[${alert.level.toUpperCase()}] ${alert.title}`
    const html = this.generateEmailTemplate(alert)

    for (const recipient of recipients) {
      try {
        await emailService.sendEmail(recipient, subject, html)
      } catch (error) {
        logger.error('发送邮件告警失败', {
          recipient,
          alertId: alert.id,
          error
        })
      }
    }
  }

  /**
   * 发送Webhook告警
   */
  private static async sendWebhookAlert(alert: AlertEvent) {
    const webhookUrl = process.env.ALERT_WEBHOOK_URL
    if (!webhookUrl) {
      logger.warn('未配置告警Webhook URL')
      return
    }

    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          alert_id: alert.id,
          level: alert.level,
          title: alert.title,
          message: alert.message,
          timestamp: alert.timestamp,
          details: alert.details
        })
      })

      if (!response.ok) {
        throw new Error(`Webhook响应错误: ${response.status}`)
      }
    } catch (error) {
      logger.error('发送Webhook告警失败', {
        alertId: alert.id,
        error
      })
    }
  }

  /**
   * 发送短信告警
   */
  private static async sendSMSAlert(alert: AlertEvent, recipients: string[]) {
    // TODO: 集成短信服务提供商
    logger.info('短信告警功能待实现', {
      alertId: alert.id,
      recipients
    })
  }

  /**
   * 发送Slack告警
   */
  private static async sendSlackAlert(alert: AlertEvent) {
    // TODO: 集成Slack API
    logger.info('Slack告警功能待实现', {
      alertId: alert.id
    })
  }

  /**
   * 发送钉钉告警
   */
  private static async sendDingTalkAlert(alert: AlertEvent) {
    // TODO: 集成钉钉机器人API
    logger.info('钉钉告警功能待实现', {
      alertId: alert.id
    })
  }

  /**
   * 生成邮件模板
   */
  private static generateEmailTemplate(alert: AlertEvent): string {
    const levelColor = {
      [AlertLevel.INFO]: '#17a2b8',
      [AlertLevel.WARNING]: '#ffc107',
      [AlertLevel.ERROR]: '#dc3545',
      [AlertLevel.CRITICAL]: '#6f42c1'
    }

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: ${levelColor[alert.level]}; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">${alert.level.toUpperCase()} 告警</h1>
        </div>
        
        <div style="padding: 20px; background-color: #f8f9fa;">
          <h2 style="color: #333; margin-top: 0;">${alert.title}</h2>
          <p style="color: #666; font-size: 16px;">${alert.message}</p>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">告警详情</h3>
            <ul style="color: #666;">
              <li><strong>告警ID:</strong> ${alert.id}</li>
              <li><strong>级别:</strong> ${alert.level}</li>
              <li><strong>时间:</strong> ${alert.timestamp.toLocaleString('zh-CN')}</li>
              <li><strong>状态:</strong> ${alert.resolved ? '已解决' : '未解决'}</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.APP_URL}/admin/alerts/${alert.id}" 
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              查看详情
            </a>
          </div>
        </div>
        
        <div style="background-color: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d;">
          <p>此邮件由AI数字营销平台自动发送，请勿回复。</p>
        </div>
      </div>
    `
  }

  /**
   * 确认告警
   */
  static async acknowledgeAlert(alertId: string, userId: string): Promise<void> {
    const alert = this.activeAlerts.get(alertId)
    if (!alert) {
      throw new Error('告警不存在')
    }

    alert.acknowledgedBy = userId
    alert.acknowledgedAt = new Date()

    // 更新Redis中的数据
    await redis.setex(`alert:${alertId}`, 86400, JSON.stringify(alert))

    logger.info('告警已确认', {
      alertId,
      userId
    })
  }

  /**
   * 解决告警
   */
  static async resolveAlert(alertId: string, userId: string): Promise<void> {
    const alert = this.activeAlerts.get(alertId)
    if (!alert) {
      throw new Error('告警不存在')
    }

    alert.resolved = true
    alert.resolvedAt = new Date()

    // 从活跃告警中移除
    this.activeAlerts.delete(alertId)

    // 更新Redis中的数据
    await redis.setex(`alert:resolved:${alertId}`, 86400 * 7, JSON.stringify(alert))
    await redis.del(`alert:${alertId}`)

    logger.info('告警已解决', {
      alertId,
      userId
    })
  }

  /**
   * 获取活跃告警
   */
  static getActiveAlerts(): AlertEvent[] {
    return Array.from(this.activeAlerts.values())
  }

  /**
   * 添加告警规则
   */
  static addRule(rule: AlertRule): void {
    this.rules.set(rule.id, rule)
    logger.info('告警规则已添加', { ruleId: rule.id })
  }

  /**
   * 更新告警规则
   */
  static updateRule(ruleId: string, updates: Partial<AlertRule>): void {
    const rule = this.rules.get(ruleId)
    if (!rule) {
      throw new Error('告警规则不存在')
    }

    Object.assign(rule, updates)
    this.rules.set(ruleId, rule)
    logger.info('告警规则已更新', { ruleId })
  }

  /**
   * 删除告警规则
   */
  static deleteRule(ruleId: string): void {
    this.rules.delete(ruleId)
    logger.info('告警规则已删除', { ruleId })
  }

  /**
   * 获取所有告警规则
   */
  static getRules(): AlertRule[] {
    return Array.from(this.rules.values())
  }

  /**
   * 清理过期告警
   */
  private static async cleanupExpiredAlerts() {
    const expiredTime = Date.now() - 7 * 24 * 60 * 60 * 1000 // 7天前
    
    for (const [alertId, alert] of this.activeAlerts) {
      if (alert.timestamp.getTime() < expiredTime) {
        this.activeAlerts.delete(alertId)
        await redis.del(`alert:${alertId}`)
      }
    }

    logger.info('过期告警清理完成')
  }

  /**
   * 记录指标数据
   */
  static async recordMetric(metric: string, value: number): Promise<void> {
    try {
      const metricKey = `metrics:${metric}`
      
      // 添加新值
      await redis.lpush(metricKey, value.toString())
      
      // 只保留最近100个值
      await redis.ltrim(metricKey, 0, 99)
      
      // 设置过期时间
      await redis.expire(metricKey, 3600)
    } catch (error) {
      logger.error('记录指标失败', {
        metric,
        value,
        error
      })
    }
  }
}
