// 营销自动化流程引擎服务
// 提供营销自动化工作流、触发器、条件判断等功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { DataSanitizer } from '../utils/validation'
import { EmailMarketingService } from './email-marketing.service'

/**
 * 触发器类型
 */
export enum TriggerType {
  USER_SIGNUP = 'USER_SIGNUP',
  USER_LOGIN = 'USER_LOGIN',
  EMAIL_OPENED = 'EMAIL_OPENED',
  EMAIL_CLICKED = 'EMAIL_CLICKED',
  PAGE_VISITED = 'PAGE_VISITED',
  PURCHASE_MADE = 'PURCHASE_MADE',
  CART_ABANDONED = 'CART_ABANDONED',
  TIME_DELAY = 'TIME_DELAY',
  DATE_REACHED = 'DATE_REACHED',
  CUSTOM_EVENT = 'CUSTOM_EVENT'
}

/**
 * 动作类型
 */
export enum ActionType {
  SEND_EMAIL = 'SEND_EMAIL',
  SEND_SMS = 'SEND_SMS',
  ADD_TO_LIST = 'ADD_TO_LIST',
  REMOVE_FROM_LIST = 'REMOVE_FROM_LIST',
  UPDATE_USER_FIELD = 'UPDATE_USER_FIELD',
  CREATE_TASK = 'CREATE_TASK',
  WEBHOOK = 'WEBHOOK',
  WAIT = 'WAIT',
  CONDITIONAL_SPLIT = 'CONDITIONAL_SPLIT'
}

/**
 * 条件操作符
 */
export enum ConditionOperator {
  EQUALS = 'EQUALS',
  NOT_EQUALS = 'NOT_EQUALS',
  CONTAINS = 'CONTAINS',
  NOT_CONTAINS = 'NOT_CONTAINS',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  GREATER_THAN_OR_EQUAL = 'GREATER_THAN_OR_EQUAL',
  LESS_THAN_OR_EQUAL = 'LESS_THAN_OR_EQUAL',
  IN = 'IN',
  NOT_IN = 'NOT_IN',
  IS_EMPTY = 'IS_EMPTY',
  IS_NOT_EMPTY = 'IS_NOT_EMPTY'
}

/**
 * 工作流状态
 */
export enum WorkflowStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  ARCHIVED = 'ARCHIVED'
}

/**
 * 工作流节点接口
 */
export interface WorkflowNode {
  id: string
  type: 'TRIGGER' | 'ACTION' | 'CONDITION' | 'DELAY'
  name: string
  config: {
    triggerType?: TriggerType
    actionType?: ActionType
    conditions?: {
      field: string
      operator: ConditionOperator
      value: any
    }[]
    delay?: {
      amount: number
      unit: 'MINUTES' | 'HOURS' | 'DAYS' | 'WEEKS'
    }
    emailTemplate?: string
    webhookUrl?: string
    customData?: Record<string, any>
  }
  position: { x: number; y: number }
  connections: {
    success?: string[]
    failure?: string[]
    default?: string[]
  }
}

/**
 * 工作流创建请求接口
 */
export interface CreateWorkflowRequest {
  name: string
  description?: string
  trigger: {
    type: TriggerType
    config: Record<string, any>
  }
  nodes: WorkflowNode[]
  settings: {
    isActive?: boolean
    maxExecutions?: number
    executionTimeout?: number
    retryAttempts?: number
  }
  tags?: string[]
}

/**
 * 工作流执行上下文接口
 */
export interface WorkflowExecutionContext {
  workflowId: string
  executionId: string
  userId: string
  triggerData: Record<string, any>
  userProfile: Record<string, any>
  currentNode: string
  variables: Record<string, any>
  startedAt: Date
  status: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
}

/**
 * 营销自动化服务类
 */
export class AutomationService {
  /**
   * 创建工作流
   */
  static async createWorkflow(
    request: CreateWorkflowRequest,
    userId: string
  ): Promise<any> {
    logger.info('创建自动化工作流', { userId, workflowName: request.name })

    try {
      // 数据验证和清理
      const cleanData = this.validateAndCleanWorkflowData(request)

      // 验证工作流结构
      this.validateWorkflowStructure(cleanData.nodes)

      // 创建工作流
      const workflow = await prisma.automationWorkflow.create({
        data: {
          userId,
          name: cleanData.name,
          description: cleanData.description,
          trigger: cleanData.trigger,
          nodes: cleanData.nodes,
          settings: cleanData.settings,
          status: WorkflowStatus.DRAFT,
          tags: cleanData.tags || [],
          metadata: {
            createdBy: userId,
            version: 1,
            nodeCount: cleanData.nodes.length
          }
        }
      })

      // 清除相关缓存
      await this.clearUserWorkflowCache(userId)

      logger.info('自动化工作流创建成功', {
        userId,
        workflowId: workflow.id,
        workflowName: workflow.name
      })

      return workflow
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('创建自动化工作流失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建工作流失败')
    }
  }

  /**
   * 激活工作流
   */
  static async activateWorkflow(workflowId: string, userId: string): Promise<void> {
    logger.info('激活自动化工作流', { userId, workflowId })

    try {
      // 验证工作流存在且属于用户
      const workflow = await this.getWorkflowById(workflowId, userId)

      if (workflow.status === WorkflowStatus.ACTIVE) {
        throw new ApiError(ErrorCodes.VALIDATION_ERROR, '工作流已处于激活状态')
      }

      // 再次验证工作流结构
      this.validateWorkflowStructure(workflow.nodes)

      // 更新状态
      await prisma.automationWorkflow.update({
        where: { id: workflowId },
        data: {
          status: WorkflowStatus.ACTIVE,
          activatedAt: new Date(),
          updatedAt: new Date()
        }
      })

      // 注册触发器
      await this.registerWorkflowTrigger(workflow)

      // 清除相关缓存
      await this.clearWorkflowCache(workflowId)

      logger.info('自动化工作流激活成功', { userId, workflowId })
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('激活自动化工作流失败', {
        userId,
        workflowId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '激活工作流失败')
    }
  }

  /**
   * 触发工作流执行
   */
  static async triggerWorkflow(
    workflowId: string,
    triggerData: Record<string, any>,
    userProfile: Record<string, any>
  ): Promise<string> {
    logger.info('触发工作流执行', { workflowId, triggerData })

    try {
      // 获取工作流
      const workflow = await prisma.automationWorkflow.findFirst({
        where: {
          id: workflowId,
          status: WorkflowStatus.ACTIVE
        }
      })

      if (!workflow) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '工作流不存在或未激活')
      }

      // 检查执行限制
      if (workflow.settings?.maxExecutions) {
        const executionCount = await prisma.workflowExecution.count({
          where: { workflowId }
        })

        if (executionCount >= workflow.settings.maxExecutions) {
          throw new ApiError(ErrorCodes.VALIDATION_ERROR, '工作流执行次数已达上限')
        }
      }

      // 创建执行记录
      const execution = await prisma.workflowExecution.create({
        data: {
          workflowId,
          userId: workflow.userId,
          triggerData,
          userProfile,
          status: 'RUNNING',
          startedAt: new Date(),
          currentNode: this.findTriggerNode(workflow.nodes)?.id || '',
          variables: {}
        }
      })

      // 开始执行工作流
      await this.executeWorkflow(execution.id)

      logger.info('工作流执行已启动', {
        workflowId,
        executionId: execution.id
      })

      return execution.id
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('触发工作流执行失败', {
        workflowId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '触发工作流失败')
    }
  }

  /**
   * 执行工作流
   */
  static async executeWorkflow(executionId: string): Promise<void> {
    try {
      // 获取执行上下文
      const execution = await prisma.workflowExecution.findUnique({
        where: { id: executionId },
        include: {
          workflow: true
        }
      })

      if (!execution || !execution.workflow) {
        throw new Error('执行记录不存在')
      }

      const context: WorkflowExecutionContext = {
        workflowId: execution.workflowId,
        executionId: execution.id,
        userId: execution.userId,
        triggerData: execution.triggerData as Record<string, any>,
        userProfile: execution.userProfile as Record<string, any>,
        currentNode: execution.currentNode,
        variables: execution.variables as Record<string, any>,
        startedAt: execution.startedAt,
        status: execution.status as any
      }

      // 执行当前节点
      await this.executeNode(context, execution.workflow.nodes as WorkflowNode[])
    } catch (error) {
      logger.error('执行工作流失败', {
        executionId,
        error: error instanceof Error ? error.message : String(error)
      })

      // 更新执行状态为失败
      await prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          errorMessage: error instanceof Error ? error.message : String(error)
        }
      })
    }
  }

  /**
   * 执行节点
   */
  private static async executeNode(
    context: WorkflowExecutionContext,
    nodes: WorkflowNode[]
  ): Promise<void> {
    const currentNode = nodes.find(node => node.id === context.currentNode)
    
    if (!currentNode) {
      // 工作流完成
      await prisma.workflowExecution.update({
        where: { id: context.executionId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date()
        }
      })
      return
    }

    logger.debug('执行工作流节点', {
      executionId: context.executionId,
      nodeId: currentNode.id,
      nodeType: currentNode.type
    })

    let nextNodeId: string | null = null

    try {
      switch (currentNode.type) {
        case 'TRIGGER':
          // 触发器节点，直接进入下一个节点
          nextNodeId = currentNode.connections.default?.[0] || null
          break

        case 'ACTION':
          nextNodeId = await this.executeActionNode(context, currentNode)
          break

        case 'CONDITION':
          nextNodeId = await this.executeConditionNode(context, currentNode)
          break

        case 'DELAY':
          nextNodeId = await this.executeDelayNode(context, currentNode)
          break

        default:
          throw new Error(`未知的节点类型: ${currentNode.type}`)
      }

      // 更新执行上下文
      if (nextNodeId) {
        await prisma.workflowExecution.update({
          where: { id: context.executionId },
          data: {
            currentNode: nextNodeId,
            variables: context.variables,
            updatedAt: new Date()
          }
        })

        // 继续执行下一个节点
        context.currentNode = nextNodeId
        await this.executeNode(context, nodes)
      } else {
        // 工作流完成
        await prisma.workflowExecution.update({
          where: { id: context.executionId },
          data: {
            status: 'COMPLETED',
            completedAt: new Date()
          }
        })
      }
    } catch (error) {
      logger.error('执行节点失败', {
        executionId: context.executionId,
        nodeId: currentNode.id,
        error: error instanceof Error ? error.message : String(error)
      })

      // 记录节点执行失败
      await prisma.workflowExecution.update({
        where: { id: context.executionId },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          errorMessage: error instanceof Error ? error.message : String(error)
        }
      })
    }
  }

  /**
   * 执行动作节点
   */
  private static async executeActionNode(
    context: WorkflowExecutionContext,
    node: WorkflowNode
  ): Promise<string | null> {
    const { actionType } = node.config

    switch (actionType) {
      case ActionType.SEND_EMAIL:
        await this.executeSendEmailAction(context, node)
        break

      case ActionType.ADD_TO_LIST:
        await this.executeAddToListAction(context, node)
        break

      case ActionType.UPDATE_USER_FIELD:
        await this.executeUpdateUserFieldAction(context, node)
        break

      case ActionType.WEBHOOK:
        await this.executeWebhookAction(context, node)
        break

      default:
        logger.warn('未实现的动作类型', { actionType })
    }

    return node.connections.success?.[0] || node.connections.default?.[0] || null
  }

  /**
   * 执行条件节点
   */
  private static async executeConditionNode(
    context: WorkflowExecutionContext,
    node: WorkflowNode
  ): Promise<string | null> {
    const { conditions } = node.config

    if (!conditions || conditions.length === 0) {
      return node.connections.default?.[0] || null
    }

    // 评估所有条件（AND逻辑）
    const allConditionsMet = conditions.every(condition => {
      return this.evaluateCondition(condition, context)
    })

    if (allConditionsMet) {
      return node.connections.success?.[0] || node.connections.default?.[0] || null
    } else {
      return node.connections.failure?.[0] || node.connections.default?.[0] || null
    }
  }

  /**
   * 执行延迟节点
   */
  private static async executeDelayNode(
    context: WorkflowExecutionContext,
    node: WorkflowNode
  ): Promise<string | null> {
    const { delay } = node.config

    if (!delay) {
      return node.connections.default?.[0] || null
    }

    // 计算延迟时间（毫秒）
    let delayMs = 0
    switch (delay.unit) {
      case 'MINUTES':
        delayMs = delay.amount * 60 * 1000
        break
      case 'HOURS':
        delayMs = delay.amount * 60 * 60 * 1000
        break
      case 'DAYS':
        delayMs = delay.amount * 24 * 60 * 60 * 1000
        break
      case 'WEEKS':
        delayMs = delay.amount * 7 * 24 * 60 * 60 * 1000
        break
    }

    // 安排延迟执行
    const executeAt = new Date(Date.now() + delayMs)
    
    // 更新执行状态为等待
    await prisma.workflowExecution.update({
      where: { id: context.executionId },
      data: {
        status: 'WAITING',
        scheduledAt: executeAt
      }
    })

    // 在实际应用中，这里应该使用任务队列来安排延迟执行
    // 这里简化处理，直接返回下一个节点
    return node.connections.default?.[0] || null
  }

  /**
   * 发送邮件动作
   */
  private static async executeSendEmailAction(
    context: WorkflowExecutionContext,
    node: WorkflowNode
  ): Promise<void> {
    const { emailTemplate, customData } = node.config

    if (!emailTemplate) {
      throw new Error('邮件模板未配置')
    }

    // 获取用户邮箱
    const userEmail = context.userProfile.email || context.triggerData.email

    if (!userEmail) {
      throw new Error('用户邮箱不存在')
    }

    // 发送邮件
    await EmailMarketingService.sendEmail({
      templateId: emailTemplate,
      recipients: [{
        email: userEmail,
        name: context.userProfile.name || context.userProfile.firstName,
        variables: {
          ...context.variables,
          ...customData
        }
      }]
    }, context.userId)
  }

  /**
   * 添加到列表动作
   */
  private static async executeAddToListAction(
    context: WorkflowExecutionContext,
    node: WorkflowNode
  ): Promise<void> {
    // TODO: 实现添加到邮件列表的逻辑
    logger.info('执行添加到列表动作', {
      executionId: context.executionId,
      nodeId: node.id
    })
  }

  /**
   * 更新用户字段动作
   */
  private static async executeUpdateUserFieldAction(
    context: WorkflowExecutionContext,
    node: WorkflowNode
  ): Promise<void> {
    // TODO: 实现更新用户字段的逻辑
    logger.info('执行更新用户字段动作', {
      executionId: context.executionId,
      nodeId: node.id
    })
  }

  /**
   * Webhook动作
   */
  private static async executeWebhookAction(
    context: WorkflowExecutionContext,
    node: WorkflowNode
  ): Promise<void> {
    const { webhookUrl, customData } = node.config

    if (!webhookUrl) {
      throw new Error('Webhook URL未配置')
    }

    const payload = {
      executionId: context.executionId,
      workflowId: context.workflowId,
      triggerData: context.triggerData,
      userProfile: context.userProfile,
      variables: context.variables,
      customData
    }

    // 发送Webhook请求
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    })

    if (!response.ok) {
      throw new Error(`Webhook请求失败: ${response.status} ${response.statusText}`)
    }
  }

  /**
   * 评估条件
   */
  private static evaluateCondition(
    condition: { field: string; operator: ConditionOperator; value: any },
    context: WorkflowExecutionContext
  ): boolean {
    // 获取字段值
    let fieldValue: any
    
    if (condition.field.startsWith('trigger.')) {
      fieldValue = this.getNestedValue(context.triggerData, condition.field.substring(8))
    } else if (condition.field.startsWith('user.')) {
      fieldValue = this.getNestedValue(context.userProfile, condition.field.substring(5))
    } else if (condition.field.startsWith('var.')) {
      fieldValue = this.getNestedValue(context.variables, condition.field.substring(4))
    } else {
      fieldValue = this.getNestedValue(context.userProfile, condition.field)
    }

    // 评估条件
    switch (condition.operator) {
      case ConditionOperator.EQUALS:
        return fieldValue === condition.value
      case ConditionOperator.NOT_EQUALS:
        return fieldValue !== condition.value
      case ConditionOperator.CONTAINS:
        return String(fieldValue).includes(String(condition.value))
      case ConditionOperator.NOT_CONTAINS:
        return !String(fieldValue).includes(String(condition.value))
      case ConditionOperator.GREATER_THAN:
        return Number(fieldValue) > Number(condition.value)
      case ConditionOperator.LESS_THAN:
        return Number(fieldValue) < Number(condition.value)
      case ConditionOperator.GREATER_THAN_OR_EQUAL:
        return Number(fieldValue) >= Number(condition.value)
      case ConditionOperator.LESS_THAN_OR_EQUAL:
        return Number(fieldValue) <= Number(condition.value)
      case ConditionOperator.IN:
        return Array.isArray(condition.value) && condition.value.includes(fieldValue)
      case ConditionOperator.NOT_IN:
        return Array.isArray(condition.value) && !condition.value.includes(fieldValue)
      case ConditionOperator.IS_EMPTY:
        return !fieldValue || fieldValue === '' || (Array.isArray(fieldValue) && fieldValue.length === 0)
      case ConditionOperator.IS_NOT_EMPTY:
        return fieldValue && fieldValue !== '' && (!Array.isArray(fieldValue) || fieldValue.length > 0)
      default:
        return false
    }
  }

  /**
   * 获取嵌套对象值
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * 获取工作流详情
   */
  private static async getWorkflowById(workflowId: string, userId: string): Promise<any> {
    const workflow = await prisma.automationWorkflow.findFirst({
      where: {
        id: workflowId,
        userId,
        deletedAt: null
      }
    })

    if (!workflow) {
      throw new ApiError(ErrorCodes.NOT_FOUND, '工作流不存在')
    }

    return workflow
  }

  /**
   * 验证工作流结构
   */
  private static validateWorkflowStructure(nodes: WorkflowNode[]): void {
    // 检查是否有触发器节点
    const triggerNodes = nodes.filter(node => node.type === 'TRIGGER')
    if (triggerNodes.length !== 1) {
      throw new ApiError(ErrorCodes.VALIDATION_ERROR, '工作流必须有且仅有一个触发器节点')
    }

    // 检查节点连接
    for (const node of nodes) {
      const allConnections = [
        ...(node.connections.success || []),
        ...(node.connections.failure || []),
        ...(node.connections.default || [])
      ]

      for (const connectionId of allConnections) {
        if (!nodes.find(n => n.id === connectionId)) {
          throw new ApiError(ErrorCodes.VALIDATION_ERROR, `节点连接无效: ${connectionId}`)
        }
      }
    }
  }

  /**
   * 查找触发器节点
   */
  private static findTriggerNode(nodes: WorkflowNode[]): WorkflowNode | null {
    return nodes.find(node => node.type === 'TRIGGER') || null
  }

  /**
   * 注册工作流触发器
   */
  private static async registerWorkflowTrigger(workflow: any): Promise<void> {
    // 在实际应用中，这里应该注册各种触发器监听器
    // 例如：用户注册事件、邮件打开事件等
    logger.info('注册工作流触发器', {
      workflowId: workflow.id,
      triggerType: workflow.trigger.type
    })
  }

  /**
   * 验证和清理工作流数据
   */
  private static validateAndCleanWorkflowData(data: CreateWorkflowRequest): CreateWorkflowRequest {
    return {
      name: DataSanitizer.sanitizeText(data.name),
      description: data.description ? DataSanitizer.sanitizeText(data.description) : undefined,
      trigger: data.trigger,
      nodes: data.nodes,
      settings: data.settings,
      tags: data.tags?.map(tag => DataSanitizer.sanitizeText(tag)).filter(Boolean)
    }
  }

  /**
   * 清除用户工作流缓存
   */
  private static async clearUserWorkflowCache(userId: string): Promise<void> {
    try {
      await redis.delPattern(`user_workflows:${userId}:*`)
    } catch (error) {
      logger.warn('清除用户工作流缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 清除工作流缓存
   */
  private static async clearWorkflowCache(workflowId: string): Promise<void> {
    try {
      await redis.del(`workflow:${workflowId}`)
    } catch (error) {
      logger.warn('清除工作流缓存失败', {
        workflowId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }
}
