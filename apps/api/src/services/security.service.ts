// 安全服务
// 提供数据加密、安全验证、审计日志等安全功能

import crypto from 'crypto'
import bcrypt from 'bcryptjs'
import { prisma } from '../utils/database'
import { logger } from '../utils/logger'
import { redis } from '../utils/redis'

/**
 * 审计事件类型
 */
export enum AuditEventType {
  USER_LOGIN = 'USER_LOGIN',
  USER_LOGOUT = 'USER_LOGOUT',
  USER_REGISTER = 'USER_REGISTER',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PROFILE_UPDATE = 'PROFILE_UPDATE',
  SUBSCRIPTION_CHANGE = 'SUBSCRIPTION_CHANGE',
  PAYMENT_PROCESS = 'PAYMENT_PROCESS',
  DATA_EXPORT = 'DATA_EXPORT',
  ADMIN_ACTION = 'ADMIN_ACTION',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  API_ACCESS = 'API_ACCESS',
  FILE_UPLOAD = 'FILE_UPLOAD',
  EMAIL_SEND = 'EMAIL_SEND'
}

/**
 * 审计日志条目
 */
export interface AuditLogEntry {
  id: string
  userId?: string
  sessionId?: string
  eventType: AuditEventType
  resource: string
  action: string
  details: any
  ipAddress: string
  userAgent: string
  timestamp: Date
  success: boolean
  errorMessage?: string
}

/**
 * 安全配置
 */
export interface SecurityConfig {
  encryption: {
    algorithm: string
    keyLength: number
    ivLength: number
  }
  password: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
    saltRounds: number
  }
  session: {
    maxAge: number
    secure: boolean
    httpOnly: boolean
    sameSite: 'strict' | 'lax' | 'none'
  }
  rateLimit: {
    windowMs: number
    maxRequests: number
    skipSuccessfulRequests: boolean
  }
}

/**
 * 安全服务类
 */
export class SecurityService {
  private static readonly ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || crypto.randomBytes(32)
  private static readonly config: SecurityConfig = {
    encryption: {
      algorithm: 'aes-256-gcm',
      keyLength: 32,
      ivLength: 16
    },
    password: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      saltRounds: 12
    },
    session: {
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'strict'
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      maxRequests: 100,
      skipSuccessfulRequests: false
    }
  }

  /**
   * 加密敏感数据
   */
  static encrypt(text: string): string {
    try {
      const iv = crypto.randomBytes(this.config.encryption.ivLength)
      const cipher = crypto.createCipher(this.config.encryption.algorithm, this.ENCRYPTION_KEY)
      
      let encrypted = cipher.update(text, 'utf8', 'hex')
      encrypted += cipher.final('hex')
      
      const authTag = cipher.getAuthTag()
      
      return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted
    } catch (error) {
      logger.error('数据加密失败', { error })
      throw new Error('加密失败')
    }
  }

  /**
   * 解密敏感数据
   */
  static decrypt(encryptedText: string): string {
    try {
      const parts = encryptedText.split(':')
      if (parts.length !== 3) {
        throw new Error('无效的加密格式')
      }

      const iv = Buffer.from(parts[0], 'hex')
      const authTag = Buffer.from(parts[1], 'hex')
      const encrypted = parts[2]

      const decipher = crypto.createDecipher(this.config.encryption.algorithm, this.ENCRYPTION_KEY)
      decipher.setAuthTag(authTag)

      let decrypted = decipher.update(encrypted, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return decrypted
    } catch (error) {
      logger.error('数据解密失败', { error })
      throw new Error('解密失败')
    }
  }

  /**
   * 哈希密码
   */
  static async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.config.password.saltRounds)
    } catch (error) {
      logger.error('密码哈希失败', { error })
      throw new Error('密码处理失败')
    }
  }

  /**
   * 验证密码
   */
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hashedPassword)
    } catch (error) {
      logger.error('密码验证失败', { error })
      return false
    }
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean
    errors: string[]
    score: number
  } {
    const errors: string[] = []
    let score = 0

    // 长度检查
    if (password.length < this.config.password.minLength) {
      errors.push(`密码长度至少${this.config.password.minLength}位`)
    } else {
      score += 1
    }

    // 大写字母检查
    if (this.config.password.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母')
    } else if (/[A-Z]/.test(password)) {
      score += 1
    }

    // 小写字母检查
    if (this.config.password.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母')
    } else if (/[a-z]/.test(password)) {
      score += 1
    }

    // 数字检查
    if (this.config.password.requireNumbers && !/\d/.test(password)) {
      errors.push('密码必须包含数字')
    } else if (/\d/.test(password)) {
      score += 1
    }

    // 特殊字符检查
    if (this.config.password.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符')
    } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1
    }

    // 常见密码检查
    const commonPasswords = ['123456', 'password', 'admin', 'qwerty', '123456789']
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('不能使用常见密码')
      score = 0
    }

    return {
      isValid: errors.length === 0,
      errors,
      score: Math.min(score, 5)
    }
  }

  /**
   * 生成安全令牌
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * 生成CSRF令牌
   */
  static generateCSRFToken(): string {
    return crypto.randomBytes(32).toString('base64')
  }

  /**
   * 验证CSRF令牌
   */
  static verifyCSRFToken(token: string, sessionToken: string): boolean {
    return crypto.timingSafeEqual(
      Buffer.from(token, 'base64'),
      Buffer.from(sessionToken, 'base64')
    )
  }

  /**
   * 记录审计日志
   */
  static async logAuditEvent(
    eventType: AuditEventType,
    resource: string,
    action: string,
    details: any,
    context: {
      userId?: string
      sessionId?: string
      ipAddress: string
      userAgent: string
      success: boolean
      errorMessage?: string
    }
  ): Promise<void> {
    try {
      const auditLog: Omit<AuditLogEntry, 'id'> = {
        userId: context.userId,
        sessionId: context.sessionId,
        eventType,
        resource,
        action,
        details: this.sanitizeAuditDetails(details),
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        timestamp: new Date(),
        success: context.success,
        errorMessage: context.errorMessage
      }

      // 保存到数据库
      await prisma.auditLog.create({
        data: auditLog as any
      })

      // 如果是安全违规事件，立即告警
      if (eventType === AuditEventType.SECURITY_VIOLATION) {
        await this.handleSecurityViolation(auditLog)
      }

      logger.info('审计日志记录成功', {
        eventType,
        resource,
        action,
        userId: context.userId
      })
    } catch (error) {
      logger.error('记录审计日志失败', {
        eventType,
        resource,
        action,
        error
      })
    }
  }

  /**
   * 清理审计详情中的敏感信息
   */
  private static sanitizeAuditDetails(details: any): any {
    if (!details || typeof details !== 'object') {
      return details
    }

    const sanitized = { ...details }
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'creditCard']

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]'
      }
    }

    return sanitized
  }

  /**
   * 处理安全违规事件
   */
  private static async handleSecurityViolation(auditLog: Omit<AuditLogEntry, 'id'>): Promise<void> {
    try {
      // 记录到Redis用于实时监控
      const violationKey = `security:violation:${Date.now()}`
      await redis.setex(violationKey, 3600, JSON.stringify(auditLog))

      // 如果是同一IP的多次违规，考虑临时封禁
      if (auditLog.ipAddress) {
        const violationCount = await this.getViolationCount(auditLog.ipAddress)
        if (violationCount >= 5) {
          await this.blockIP(auditLog.ipAddress, 3600) // 封禁1小时
        }
      }

      logger.warn('安全违规事件', {
        eventType: auditLog.eventType,
        ipAddress: auditLog.ipAddress,
        userId: auditLog.userId
      })
    } catch (error) {
      logger.error('处理安全违规事件失败', { error })
    }
  }

  /**
   * 获取IP违规次数
   */
  private static async getViolationCount(ipAddress: string): Promise<number> {
    try {
      const key = `security:violations:${ipAddress}`
      const count = await redis.get(key)
      return parseInt(count || '0')
    } catch (error) {
      return 0
    }
  }

  /**
   * 封禁IP地址
   */
  static async blockIP(ipAddress: string, duration: number): Promise<void> {
    try {
      const blockKey = `security:blocked:${ipAddress}`
      await redis.setex(blockKey, duration, '1')
      
      logger.warn('IP地址已封禁', {
        ipAddress,
        duration
      })
    } catch (error) {
      logger.error('封禁IP失败', { ipAddress, error })
    }
  }

  /**
   * 检查IP是否被封禁
   */
  static async isIPBlocked(ipAddress: string): Promise<boolean> {
    try {
      const blockKey = `security:blocked:${ipAddress}`
      const blocked = await redis.get(blockKey)
      return blocked === '1'
    } catch (error) {
      return false
    }
  }

  /**
   * 检查请求频率限制
   */
  static async checkRateLimit(
    identifier: string,
    maxRequests: number = this.config.rateLimit.maxRequests,
    windowMs: number = this.config.rateLimit.windowMs
  ): Promise<{
    allowed: boolean
    remaining: number
    resetTime: Date
  }> {
    try {
      const key = `rate_limit:${identifier}`
      const current = await redis.get(key)
      const requests = parseInt(current || '0')

      if (requests >= maxRequests) {
        const ttl = await redis.ttl(key)
        return {
          allowed: false,
          remaining: 0,
          resetTime: new Date(Date.now() + ttl * 1000)
        }
      }

      // 增加计数
      const newCount = await redis.incr(key)
      if (newCount === 1) {
        await redis.expire(key, Math.floor(windowMs / 1000))
      }

      return {
        allowed: true,
        remaining: maxRequests - newCount,
        resetTime: new Date(Date.now() + windowMs)
      }
    } catch (error) {
      logger.error('检查频率限制失败', { identifier, error })
      return {
        allowed: true,
        remaining: maxRequests,
        resetTime: new Date(Date.now() + windowMs)
      }
    }
  }

  /**
   * 验证输入数据
   */
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') {
      return ''
    }

    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
      .replace(/javascript:/gi, '') // 移除javascript协议
      .replace(/on\w+\s*=/gi, '') // 移除事件处理器
      .trim()
  }

  /**
   * 验证文件上传安全性
   */
  static validateFileUpload(file: {
    originalname: string
    mimetype: string
    size: number
  }): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    const maxSize = 10 * 1024 * 1024 // 10MB

    // 检查文件类型
    if (!allowedTypes.includes(file.mimetype)) {
      errors.push('不支持的文件类型')
    }

    // 检查文件大小
    if (file.size > maxSize) {
      errors.push('文件大小超过限制')
    }

    // 检查文件名
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.js', '.vbs']
    const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'))
    if (dangerousExtensions.includes(fileExtension)) {
      errors.push('危险的文件扩展名')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 获取安全配置
   */
  static getSecurityConfig(): SecurityConfig {
    return this.config
  }

  /**
   * 获取审计日志
   */
  static async getAuditLogs(
    filters: {
      userId?: string
      eventType?: AuditEventType
      startDate?: Date
      endDate?: Date
      page?: number
      limit?: number
    } = {}
  ): Promise<{
    logs: AuditLogEntry[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    try {
      const {
        userId,
        eventType,
        startDate,
        endDate,
        page = 1,
        limit = 50
      } = filters

      const where: any = {}

      if (userId) where.userId = userId
      if (eventType) where.eventType = eventType
      if (startDate || endDate) {
        where.timestamp = {}
        if (startDate) where.timestamp.gte = startDate
        if (endDate) where.timestamp.lte = endDate
      }

      const skip = (page - 1) * limit

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          skip,
          take: limit,
          orderBy: { timestamp: 'desc' }
        }),
        prisma.auditLog.count({ where })
      ])

      const totalPages = Math.ceil(total / limit)

      return {
        logs: logs as AuditLogEntry[],
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      logger.error('获取审计日志失败', { error })
      throw new Error('获取审计日志失败')
    }
  }

  /**
   * 清理过期的审计日志
   */
  static async cleanupAuditLogs(retentionDays: number = 90): Promise<void> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

      const deletedCount = await prisma.auditLog.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate
          }
        }
      })

      logger.info('清理审计日志完成', {
        deletedCount: deletedCount.count,
        cutoffDate
      })
    } catch (error) {
      logger.error('清理审计日志失败', { error })
    }
  }
}
