// 营销活动管理服务
// 提供营销活动的创建、编辑、管理和监控功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { DataSanitizer } from '../utils/validation'

/**
 * 营销活动状态
 */
export enum CampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

/**
 * 营销活动类型
 */
export enum CampaignType {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  SOCIAL_MEDIA = 'SOCIAL_MEDIA',
  DISPLAY_AD = 'DISPLAY_AD',
  SEARCH_AD = 'SEARCH_AD',
  CONTENT_MARKETING = 'CONTENT_MARKETING',
  INFLUENCER = 'INFLUENCER',
  EVENT = 'EVENT'
}

/**
 * 营销活动创建请求接口
 */
export interface CreateCampaignRequest {
  name: string
  description?: string
  type: CampaignType
  targetAudience: {
    demographics?: {
      ageRange?: string
      gender?: string
      location?: string[]
      interests?: string[]
    }
    segments?: string[]
    customFilters?: any
  }
  budget?: {
    total?: number
    daily?: number
    currency?: string
  }
  schedule: {
    startDate: Date
    endDate?: Date
    timezone?: string
  }
  content: {
    subject?: string
    message: string
    images?: string[]
    links?: {
      url: string
      text: string
    }[]
    callToAction?: {
      text: string
      url: string
    }
  }
  settings: {
    autoOptimize?: boolean
    trackingEnabled?: boolean
    abTestEnabled?: boolean
    frequencyCap?: number
  }
  tags?: string[]
}

/**
 * 营销活动更新请求接口
 */
export interface UpdateCampaignRequest {
  name?: string
  description?: string
  targetAudience?: CreateCampaignRequest['targetAudience']
  budget?: CreateCampaignRequest['budget']
  schedule?: CreateCampaignRequest['schedule']
  content?: CreateCampaignRequest['content']
  settings?: CreateCampaignRequest['settings']
  tags?: string[]
  status?: CampaignStatus
}

/**
 * 营销活动查询选项
 */
export interface CampaignQueryOptions {
  page?: number
  limit?: number
  status?: CampaignStatus[]
  type?: CampaignType[]
  search?: string
  tags?: string[]
  startDate?: Date
  endDate?: Date
  sortBy?: 'createdAt' | 'updatedAt' | 'startDate' | 'name' | 'budget'
  sortOrder?: 'asc' | 'desc'
}

/**
 * 营销活动统计接口
 */
export interface CampaignStats {
  impressions: number
  clicks: number
  conversions: number
  revenue: number
  cost: number
  ctr: number // 点击率
  cpc: number // 每次点击成本
  cpa: number // 每次转化成本
  roi: number // 投资回报率
  reach: number // 触达人数
  engagement: number // 参与度
}

/**
 * 营销活动管理服务类
 */
export class CampaignService {
  /**
   * 创建营销活动
   */
  static async createCampaign(
    request: CreateCampaignRequest,
    userId: string
  ): Promise<any> {
    logger.info('创建营销活动', { userId, campaignName: request.name })

    try {
      // 数据验证和清理
      const cleanData = this.validateAndCleanCampaignData(request)

      // 检查活动名称是否重复
      const existingCampaign = await prisma.campaign.findFirst({
        where: {
          name: cleanData.name,
          userId,
          deletedAt: null
        }
      })

      if (existingCampaign) {
        throw new ApiError(ErrorCodes.VALIDATION_ERROR, '活动名称已存在')
      }

      // 创建营销活动
      const campaign = await prisma.campaign.create({
        data: {
          userId,
          name: cleanData.name,
          description: cleanData.description,
          type: cleanData.type,
          status: CampaignStatus.DRAFT,
          targetAudience: cleanData.targetAudience,
          budget: cleanData.budget,
          schedule: cleanData.schedule,
          content: cleanData.content,
          settings: cleanData.settings,
          tags: cleanData.tags || [],
          metadata: {
            createdBy: userId,
            version: 1
          }
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // 清除相关缓存
      await this.clearUserCampaignCache(userId)

      // 记录活动日志
      await this.logCampaignActivity(campaign.id, userId, 'CREATED', '活动已创建')

      logger.info('营销活动创建成功', {
        userId,
        campaignId: campaign.id,
        campaignName: campaign.name
      })

      return campaign
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('创建营销活动失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建营销活动失败')
    }
  }

  /**
   * 更新营销活动
   */
  static async updateCampaign(
    campaignId: string,
    request: UpdateCampaignRequest,
    userId: string
  ): Promise<any> {
    logger.info('更新营销活动', { userId, campaignId })

    try {
      // 验证活动存在且属于用户
      const existingCampaign = await this.getCampaignById(campaignId, userId)

      // 检查是否可以更新
      if (existingCampaign.status === CampaignStatus.COMPLETED) {
        throw new ApiError(ErrorCodes.VALIDATION_ERROR, '已完成的活动无法修改')
      }

      // 数据验证和清理
      const cleanData = this.validateAndCleanUpdateData(request)

      // 构建更新数据
      const updateData: any = {
        updatedAt: new Date()
      }

      if (cleanData.name) updateData.name = cleanData.name
      if (cleanData.description !== undefined) updateData.description = cleanData.description
      if (cleanData.targetAudience) updateData.targetAudience = cleanData.targetAudience
      if (cleanData.budget) updateData.budget = cleanData.budget
      if (cleanData.schedule) updateData.schedule = cleanData.schedule
      if (cleanData.content) updateData.content = cleanData.content
      if (cleanData.settings) updateData.settings = cleanData.settings
      if (cleanData.tags) updateData.tags = cleanData.tags

      // 状态更新需要特殊处理
      if (cleanData.status) {
        this.validateStatusTransition(existingCampaign.status, cleanData.status)
        updateData.status = cleanData.status
        
        // 状态变更时更新相关时间戳
        if (cleanData.status === CampaignStatus.ACTIVE) {
          updateData.startedAt = new Date()
        } else if (cleanData.status === CampaignStatus.COMPLETED) {
          updateData.completedAt = new Date()
        }
      }

      // 更新版本号
      updateData.metadata = {
        ...existingCampaign.metadata,
        version: (existingCampaign.metadata?.version || 1) + 1,
        lastModifiedBy: userId
      }

      // 执行更新
      const updatedCampaign = await prisma.campaign.update({
        where: { id: campaignId },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      // 清除相关缓存
      await this.clearUserCampaignCache(userId)
      await this.clearCampaignCache(campaignId)

      // 记录活动日志
      const changes = Object.keys(cleanData).join(', ')
      await this.logCampaignActivity(
        campaignId, 
        userId, 
        'UPDATED', 
        `活动已更新: ${changes}`
      )

      logger.info('营销活动更新成功', {
        userId,
        campaignId,
        changes
      })

      return updatedCampaign
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('更新营销活动失败', {
        userId,
        campaignId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '更新营销活动失败')
    }
  }

  /**
   * 获取营销活动详情
   */
  static async getCampaignById(campaignId: string, userId: string): Promise<any> {
    try {
      // 先从缓存获取
      const cacheKey = `campaign:${campaignId}`
      const cachedCampaign = await redis.get(cacheKey)
      
      if (cachedCampaign && cachedCampaign.userId === userId) {
        return cachedCampaign
      }

      // 从数据库获取
      const campaign = await prisma.campaign.findFirst({
        where: {
          id: campaignId,
          userId,
          deletedAt: null
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      })

      if (!campaign) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '营销活动不存在')
      }

      // 缓存结果（1小时）
      await redis.set(cacheKey, campaign, 3600)

      return campaign
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('获取营销活动详情失败', {
        userId,
        campaignId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取营销活动失败')
    }
  }

  /**
   * 获取用户营销活动列表
   */
  static async getUserCampaigns(
    userId: string,
    options: CampaignQueryOptions = {}
  ): Promise<{
    campaigns: any[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    stats: {
      totalCampaigns: number
      activeCampaigns: number
      completedCampaigns: number
      totalBudget: number
    }
  }> {
    logger.debug('获取用户营销活动列表', { userId, options })

    try {
      const {
        page = 1,
        limit = 20,
        status,
        type,
        search,
        tags,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options

      // 构建查询条件
      const where: any = {
        userId,
        deletedAt: null
      }

      if (status && status.length > 0) {
        where.status = { in: status }
      }

      if (type && type.length > 0) {
        where.type = { in: type }
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (tags && tags.length > 0) {
        where.tags = { hasSome: tags }
      }

      if (startDate || endDate) {
        where.createdAt = {}
        if (startDate) where.createdAt.gte = startDate
        if (endDate) where.createdAt.lte = endDate
      }

      // 计算偏移量
      const skip = (page - 1) * limit

      // 查询活动和统计信息
      const [campaigns, total, stats] = await Promise.all([
        prisma.campaign.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder
          }
        }),
        prisma.campaign.count({ where }),
        this.getUserCampaignStats(userId)
      ])

      const totalPages = Math.ceil(total / limit)

      logger.debug('用户营销活动列表获取成功', {
        userId,
        campaignCount: campaigns.length,
        total
      })

      return {
        campaigns,
        pagination: {
          page,
          limit,
          total,
          totalPages
        },
        stats
      }
    } catch (error) {
      logger.error('获取用户营销活动列表失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取营销活动列表失败')
    }
  }

  /**
   * 删除营销活动
   */
  static async deleteCampaign(campaignId: string, userId: string): Promise<void> {
    logger.info('删除营销活动', { userId, campaignId })

    try {
      // 验证活动存在且属于用户
      const campaign = await this.getCampaignById(campaignId, userId)

      // 检查是否可以删除
      if (campaign.status === CampaignStatus.ACTIVE) {
        throw new ApiError(ErrorCodes.VALIDATION_ERROR, '活跃的活动无法删除，请先暂停')
      }

      // 软删除
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          deletedAt: new Date(),
          updatedAt: new Date()
        }
      })

      // 清除相关缓存
      await this.clearUserCampaignCache(userId)
      await this.clearCampaignCache(campaignId)

      // 记录活动日志
      await this.logCampaignActivity(campaignId, userId, 'DELETED', '活动已删除')

      logger.info('营销活动删除成功', { userId, campaignId })
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('删除营销活动失败', {
        userId,
        campaignId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '删除营销活动失败')
    }
  }

  /**
   * 获取营销活动统计数据
   */
  static async getCampaignStats(campaignId: string, userId: string): Promise<CampaignStats> {
    try {
      // 验证活动存在且属于用户
      await this.getCampaignById(campaignId, userId)

      // 从缓存获取统计数据
      const cacheKey = `campaign_stats:${campaignId}`
      const cachedStats = await redis.get<CampaignStats>(cacheKey)
      
      if (cachedStats) {
        return cachedStats
      }

      // 计算统计数据（这里是示例，实际需要根据具体的数据源计算）
      const stats: CampaignStats = {
        impressions: 0,
        clicks: 0,
        conversions: 0,
        revenue: 0,
        cost: 0,
        ctr: 0,
        cpc: 0,
        cpa: 0,
        roi: 0,
        reach: 0,
        engagement: 0
      }

      // TODO: 实现实际的统计数据计算逻辑
      // 这里需要根据具体的数据源（如Google Analytics、Facebook Ads等）来获取数据

      // 缓存统计数据（15分钟）
      await redis.set(cacheKey, stats, 900)

      return stats
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('获取营销活动统计失败', {
        userId,
        campaignId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取统计数据失败')
    }
  }

  /**
   * 验证和清理活动数据
   */
  private static validateAndCleanCampaignData(data: CreateCampaignRequest): CreateCampaignRequest {
    return {
      name: DataSanitizer.sanitizeText(data.name),
      description: data.description ? DataSanitizer.sanitizeText(data.description) : undefined,
      type: data.type,
      targetAudience: data.targetAudience,
      budget: data.budget,
      schedule: data.schedule,
      content: {
        ...data.content,
        subject: data.content.subject ? DataSanitizer.sanitizeText(data.content.subject) : undefined,
        message: DataSanitizer.sanitizeText(data.content.message)
      },
      settings: data.settings,
      tags: data.tags?.map(tag => DataSanitizer.sanitizeText(tag)).filter(Boolean)
    }
  }

  /**
   * 验证和清理更新数据
   */
  private static validateAndCleanUpdateData(data: UpdateCampaignRequest): UpdateCampaignRequest {
    const cleanData: UpdateCampaignRequest = {}

    if (data.name) cleanData.name = DataSanitizer.sanitizeText(data.name)
    if (data.description !== undefined) {
      cleanData.description = data.description ? DataSanitizer.sanitizeText(data.description) : undefined
    }
    if (data.targetAudience) cleanData.targetAudience = data.targetAudience
    if (data.budget) cleanData.budget = data.budget
    if (data.schedule) cleanData.schedule = data.schedule
    if (data.content) {
      cleanData.content = {
        ...data.content,
        subject: data.content.subject ? DataSanitizer.sanitizeText(data.content.subject) : undefined,
        message: data.content.message ? DataSanitizer.sanitizeText(data.content.message) : undefined
      }
    }
    if (data.settings) cleanData.settings = data.settings
    if (data.tags) cleanData.tags = data.tags.map(tag => DataSanitizer.sanitizeText(tag)).filter(Boolean)
    if (data.status) cleanData.status = data.status

    return cleanData
  }

  /**
   * 验证状态转换
   */
  private static validateStatusTransition(currentStatus: CampaignStatus, newStatus: CampaignStatus): void {
    const validTransitions: Record<CampaignStatus, CampaignStatus[]> = {
      [CampaignStatus.DRAFT]: [CampaignStatus.SCHEDULED, CampaignStatus.ACTIVE, CampaignStatus.CANCELLED],
      [CampaignStatus.SCHEDULED]: [CampaignStatus.ACTIVE, CampaignStatus.CANCELLED, CampaignStatus.DRAFT],
      [CampaignStatus.ACTIVE]: [CampaignStatus.PAUSED, CampaignStatus.COMPLETED, CampaignStatus.CANCELLED],
      [CampaignStatus.PAUSED]: [CampaignStatus.ACTIVE, CampaignStatus.CANCELLED],
      [CampaignStatus.COMPLETED]: [],
      [CampaignStatus.CANCELLED]: []
    }

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new ApiError(
        ErrorCodes.VALIDATION_ERROR,
        `无法从状态 ${currentStatus} 转换到 ${newStatus}`
      )
    }
  }

  /**
   * 获取用户活动统计
   */
  private static async getUserCampaignStats(userId: string): Promise<any> {
    try {
      const [totalCampaigns, activeCampaigns, completedCampaigns, budgetSum] = await Promise.all([
        prisma.campaign.count({
          where: { userId, deletedAt: null }
        }),
        prisma.campaign.count({
          where: { userId, status: CampaignStatus.ACTIVE, deletedAt: null }
        }),
        prisma.campaign.count({
          where: { userId, status: CampaignStatus.COMPLETED, deletedAt: null }
        }),
        prisma.campaign.aggregate({
          where: { userId, deletedAt: null },
          _sum: { budget: true }
        })
      ])

      return {
        totalCampaigns,
        activeCampaigns,
        completedCampaigns,
        totalBudget: budgetSum._sum.budget || 0
      }
    } catch (error) {
      logger.warn('获取用户活动统计失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      
      return {
        totalCampaigns: 0,
        activeCampaigns: 0,
        completedCampaigns: 0,
        totalBudget: 0
      }
    }
  }

  /**
   * 记录活动日志
   */
  private static async logCampaignActivity(
    campaignId: string,
    userId: string,
    action: string,
    description: string
  ): Promise<void> {
    try {
      await prisma.campaignActivity.create({
        data: {
          campaignId,
          userId,
          action,
          description,
          metadata: {
            timestamp: new Date().toISOString(),
            userAgent: 'API'
          }
        }
      })
    } catch (error) {
      logger.warn('记录活动日志失败', {
        campaignId,
        userId,
        action,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 清除用户活动缓存
   */
  private static async clearUserCampaignCache(userId: string): Promise<void> {
    try {
      await redis.delPattern(`user_campaigns:${userId}:*`)
    } catch (error) {
      logger.warn('清除用户活动缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 清除活动缓存
   */
  private static async clearCampaignCache(campaignId: string): Promise<void> {
    try {
      await redis.del(`campaign:${campaignId}`)
      await redis.del(`campaign_stats:${campaignId}`)
    } catch (error) {
      logger.warn('清除活动缓存失败', {
        campaignId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }
}
