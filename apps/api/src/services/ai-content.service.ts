// AI内容生成服务
// 提供AI文案生成、模板管理、内容优化和多语言支持

import { openaiService } from './openai.service'
import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { DataSanitizer } from '../utils/validation'

/**
 * 内容生成类型
 */
export enum ContentType {
  MARKETING_COPY = 'MARKETING_COPY',
  SOCIAL_MEDIA = 'SOCIAL_MEDIA',
  EMAIL_SUBJECT = 'EMAIL_SUBJECT',
  EMAIL_CONTENT = 'EMAIL_CONTENT',
  BLOG_POST = 'BLOG_POST',
  PRODUCT_DESCRIPTION = 'PRODUCT_DESCRIPTION',
  AD_COPY = 'AD_COPY',
  SLOGAN = 'SLOGAN',
  PRESS_RELEASE = 'PRESS_RELEASE',
  LANDING_PAGE = 'LANDING_PAGE'
}

/**
 * 内容生成请求接口
 */
export interface ContentGenerationRequest {
  type: ContentType
  prompt: string
  targetAudience?: string
  tone?: 'professional' | 'casual' | 'friendly' | 'urgent' | 'persuasive' | 'informative'
  language?: string
  keywords?: string[]
  maxLength?: number
  includeEmoji?: boolean
  includeHashtags?: boolean
  brandVoice?: string
  productInfo?: {
    name: string
    description: string
    features: string[]
    benefits: string[]
    price?: string
  }
  campaignInfo?: {
    goal: string
    budget?: string
    duration?: string
    channels: string[]
  }
}

/**
 * 内容生成响应接口
 */
export interface ContentGenerationResponse {
  content: string
  alternatives?: string[]
  metadata: {
    type: ContentType
    language: string
    wordCount: number
    characterCount: number
    estimatedReadTime: number
    seoScore?: number
    sentimentScore?: number
  }
  suggestions?: {
    improvements: string[]
    keywords: string[]
    hashtags: string[]
  }
}

/**
 * 内容模板接口
 */
export interface ContentTemplate {
  id: string
  name: string
  type: ContentType
  description: string
  systemPrompt: string
  userPromptTemplate: string
  variables: string[]
  language: string
  isActive: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

/**
 * AI内容生成服务类
 */
export class AIContentService {
  /**
   * 生成内容
   */
  static async generateContent(
    request: ContentGenerationRequest,
    userId: string
  ): Promise<ContentGenerationResponse> {
    logger.info('AI内容生成请求开始', {
      userId,
      type: request.type,
      language: request.language || 'zh-CN',
      promptLength: request.prompt.length
    })

    try {
      // 内容审核
      const moderationResult = await openaiService.moderateContent(request.prompt)
      if (moderationResult.data === true) {
        throw new ApiError(ErrorCodes.CONTENT_VIOLATION, '输入内容包含不当信息，请修改后重试')
      }

      // 获取或创建内容模板
      const template = await this.getContentTemplate(request.type, request.language || 'zh-CN')
      
      // 构建系统提示词
      const systemPrompt = this.buildSystemPrompt(template, request)
      
      // 构建用户提示词
      const userPrompt = this.buildUserPrompt(template, request)

      // 调用OpenAI生成内容
      const aiResponse = await openaiService.generateText({
        prompt: userPrompt,
        systemMessage: systemPrompt,
        temperature: 0.8,
        maxTokens: request.maxLength ? Math.min(request.maxLength * 2, 4000) : 2000
      }, userId)

      if (!aiResponse.success || !aiResponse.data) {
        throw new ApiError(ErrorCodes.AI_GENERATION_FAILED, aiResponse.error || 'AI内容生成失败')
      }

      let generatedContent = aiResponse.data.trim()

      // 内容后处理
      generatedContent = await this.postProcessContent(generatedContent, request)

      // 生成替代方案
      const alternatives = await this.generateAlternatives(request, userId, 2)

      // 分析内容
      const metadata = await this.analyzeContent(generatedContent, request)

      // 生成建议
      const suggestions = await this.generateSuggestions(generatedContent, request)

      // 保存生成历史
      await this.saveGenerationHistory(userId, request, generatedContent, aiResponse.usage)

      logger.info('AI内容生成请求成功', {
        userId,
        type: request.type,
        contentLength: generatedContent.length,
        usage: aiResponse.usage
      })

      return {
        content: generatedContent,
        alternatives,
        metadata,
        suggestions
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('AI内容生成请求失败', {
        userId,
        type: request.type,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.AI_GENERATION_FAILED, 'AI内容生成失败')
    }
  }

  /**
   * 优化现有内容
   */
  static async optimizeContent(
    content: string,
    optimizationType: 'seo' | 'readability' | 'engagement' | 'conversion',
    userId: string
  ): Promise<string> {
    logger.info('AI内容优化请求开始', {
      userId,
      optimizationType,
      contentLength: content.length
    })

    try {
      const systemPrompt = this.getOptimizationSystemPrompt(optimizationType)
      const userPrompt = `请优化以下内容：\n\n${content}`

      const aiResponse = await openaiService.generateText({
        prompt: userPrompt,
        systemMessage: systemPrompt,
        temperature: 0.7,
        maxTokens: 3000
      }, userId)

      if (!aiResponse.success || !aiResponse.data) {
        throw new ApiError(ErrorCodes.AI_GENERATION_FAILED, aiResponse.error || 'AI内容优化失败')
      }

      const optimizedContent = aiResponse.data.trim()

      logger.info('AI内容优化请求成功', {
        userId,
        optimizationType,
        originalLength: content.length,
        optimizedLength: optimizedContent.length
      })

      return optimizedContent
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('AI内容优化请求失败', {
        userId,
        optimizationType,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.AI_GENERATION_FAILED, 'AI内容优化失败')
    }
  }

  /**
   * 获取内容模板
   */
  private static async getContentTemplate(
    type: ContentType,
    language: string
  ): Promise<ContentTemplate> {
    try {
      // 先从缓存获取
      const cacheKey = `content_template:${type}:${language}`
      const cachedTemplate = await redis.get<ContentTemplate>(cacheKey)
      
      if (cachedTemplate) {
        return cachedTemplate
      }

      // 从数据库获取
      const template = await prisma.contentTemplate.findFirst({
        where: {
          type,
          language,
          isActive: true
        }
      })

      if (template) {
        // 缓存模板（1小时）
        await redis.set(cacheKey, template, 3600)
        return template as ContentTemplate
      }

      // 如果没有找到模板，使用默认模板
      const defaultTemplate = this.getDefaultTemplate(type, language)
      
      // 缓存默认模板（1小时）
      await redis.set(cacheKey, defaultTemplate, 3600)
      
      return defaultTemplate
    } catch (error) {
      logger.warn('获取内容模板失败，使用默认模板', {
        type,
        language,
        error: error instanceof Error ? error.message : String(error)
      })
      
      return this.getDefaultTemplate(type, language)
    }
  }

  /**
   * 获取默认模板
   */
  private static getDefaultTemplate(type: ContentType, language: string): ContentTemplate {
    const templates: Record<ContentType, any> = {
      [ContentType.MARKETING_COPY]: {
        systemPrompt: '你是一位专业的营销文案专家，擅长创作吸引人的营销内容。',
        userPromptTemplate: '请为以下产品/服务创作营销文案：{prompt}\n目标受众：{targetAudience}\n语调：{tone}\n关键词：{keywords}',
        variables: ['prompt', 'targetAudience', 'tone', 'keywords']
      },
      [ContentType.SOCIAL_MEDIA]: {
        systemPrompt: '你是一位社交媒体内容专家，擅长创作引人入胜的社交媒体内容。',
        userPromptTemplate: '请创作社交媒体内容：{prompt}\n平台特点：简洁有趣，适合分享\n是否包含表情符号：{includeEmoji}\n是否包含话题标签：{includeHashtags}',
        variables: ['prompt', 'includeEmoji', 'includeHashtags']
      },
      [ContentType.EMAIL_SUBJECT]: {
        systemPrompt: '你是一位邮件营销专家，擅长创作高开启率的邮件主题。',
        userPromptTemplate: '请为以下邮件内容创作主题：{prompt}\n要求：简洁有力，激发好奇心，避免垃圾邮件词汇',
        variables: ['prompt']
      },
      [ContentType.EMAIL_CONTENT]: {
        systemPrompt: '你是一位邮件营销专家，擅长创作高转化率的邮件内容。',
        userPromptTemplate: '请创作邮件内容：{prompt}\n目标受众：{targetAudience}\n语调：{tone}\n包含明确的行动号召',
        variables: ['prompt', 'targetAudience', 'tone']
      },
      [ContentType.BLOG_POST]: {
        systemPrompt: '你是一位专业的内容创作者，擅长创作有价值的博客文章。',
        userPromptTemplate: '请创作博客文章：{prompt}\n要求：结构清晰，内容有价值，SEO友好\n关键词：{keywords}',
        variables: ['prompt', 'keywords']
      },
      [ContentType.PRODUCT_DESCRIPTION]: {
        systemPrompt: '你是一位产品文案专家，擅长创作吸引人的产品描述。',
        userPromptTemplate: '请为以下产品创作描述：{prompt}\n产品特点：{features}\n产品优势：{benefits}\n价格：{price}',
        variables: ['prompt', 'features', 'benefits', 'price']
      },
      [ContentType.AD_COPY]: {
        systemPrompt: '你是一位广告文案专家，擅长创作高转化率的广告文案。',
        userPromptTemplate: '请创作广告文案：{prompt}\n目标受众：{targetAudience}\n广告目标：{goal}\n要求：简洁有力，包含行动号召',
        variables: ['prompt', 'targetAudience', 'goal']
      },
      [ContentType.SLOGAN]: {
        systemPrompt: '你是一位品牌文案专家，擅长创作朗朗上口的品牌口号。',
        userPromptTemplate: '请为以下品牌/产品创作口号：{prompt}\n要求：简洁易记，体现品牌价值',
        variables: ['prompt']
      },
      [ContentType.PRESS_RELEASE]: {
        systemPrompt: '你是一位公关专家，擅长创作专业的新闻稿。',
        userPromptTemplate: '请创作新闻稿：{prompt}\n要求：客观专业，结构完整，包含关键信息',
        variables: ['prompt']
      },
      [ContentType.LANDING_PAGE]: {
        systemPrompt: '你是一位转化率优化专家，擅长创作高转化率的着陆页内容。',
        userPromptTemplate: '请创作着陆页内容：{prompt}\n目标受众：{targetAudience}\n转化目标：{goal}\n要求：结构清晰，说服力强',
        variables: ['prompt', 'targetAudience', 'goal']
      }
    }

    const template = templates[type]
    
    return {
      id: `default_${type}_${language}`,
      name: `默认${type}模板`,
      type,
      description: `${type}的默认生成模板`,
      systemPrompt: template.systemPrompt,
      userPromptTemplate: template.userPromptTemplate,
      variables: template.variables,
      language,
      isActive: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }

  /**
   * 构建系统提示词
   */
  private static buildSystemPrompt(
    template: ContentTemplate,
    request: ContentGenerationRequest
  ): string {
    let systemPrompt = template.systemPrompt

    // 添加语言要求
    if (request.language && request.language !== 'zh-CN') {
      systemPrompt += `\n请使用${this.getLanguageName(request.language)}回复。`
    }

    // 添加品牌声音
    if (request.brandVoice) {
      systemPrompt += `\n品牌声音：${request.brandVoice}`
    }

    // 添加长度限制
    if (request.maxLength) {
      systemPrompt += `\n内容长度限制：不超过${request.maxLength}字符。`
    }

    return systemPrompt
  }

  /**
   * 构建用户提示词
   */
  private static buildUserPrompt(
    template: ContentTemplate,
    request: ContentGenerationRequest
  ): string {
    let userPrompt = template.userPromptTemplate

    // 替换变量
    const variables: Record<string, string> = {
      prompt: request.prompt,
      targetAudience: request.targetAudience || '通用受众',
      tone: this.getToneDescription(request.tone || 'professional'),
      keywords: request.keywords?.join('、') || '',
      includeEmoji: request.includeEmoji ? '是' : '否',
      includeHashtags: request.includeHashtags ? '是' : '否',
      features: request.productInfo?.features?.join('、') || '',
      benefits: request.productInfo?.benefits?.join('、') || '',
      price: request.productInfo?.price || '',
      goal: request.campaignInfo?.goal || ''
    }

    // 替换模板中的变量
    for (const [key, value] of Object.entries(variables)) {
      userPrompt = userPrompt.replace(new RegExp(`{${key}}`, 'g'), value)
    }

    return userPrompt
  }

  /**
   * 内容后处理
   */
  private static async postProcessContent(
    content: string,
    request: ContentGenerationRequest
  ): Promise<string> {
    let processedContent = content

    // 清理内容
    processedContent = DataSanitizer.sanitizeText(processedContent)

    // 长度限制
    if (request.maxLength && processedContent.length > request.maxLength) {
      processedContent = processedContent.substring(0, request.maxLength).trim()
      
      // 确保不在单词中间截断
      const lastSpace = processedContent.lastIndexOf(' ')
      if (lastSpace > processedContent.length * 0.8) {
        processedContent = processedContent.substring(0, lastSpace)
      }
      
      processedContent += '...'
    }

    return processedContent
  }

  /**
   * 生成替代方案
   */
  private static async generateAlternatives(
    request: ContentGenerationRequest,
    userId: string,
    count: number = 2
  ): Promise<string[]> {
    const alternatives: string[] = []

    try {
      for (let i = 0; i < count; i++) {
        const template = await this.getContentTemplate(request.type, request.language || 'zh-CN')
        const systemPrompt = this.buildSystemPrompt(template, request)
        const userPrompt = this.buildUserPrompt(template, request) + '\n\n请提供一个不同的创意版本。'

        const aiResponse = await openaiService.generateText({
          prompt: userPrompt,
          systemMessage: systemPrompt,
          temperature: 0.9, // 更高的创意性
          maxTokens: request.maxLength ? Math.min(request.maxLength * 2, 2000) : 1500
        }, userId)

        if (aiResponse.success && aiResponse.data) {
          const alternative = await this.postProcessContent(aiResponse.data.trim(), request)
          alternatives.push(alternative)
        }
      }
    } catch (error) {
      logger.warn('生成替代方案失败', {
        userId,
        type: request.type,
        error: error instanceof Error ? error.message : String(error)
      })
    }

    return alternatives
  }

  /**
   * 分析内容
   */
  private static async analyzeContent(
    content: string,
    request: ContentGenerationRequest
  ): Promise<ContentGenerationResponse['metadata']> {
    const wordCount = content.split(/\s+/).length
    const characterCount = content.length
    const estimatedReadTime = Math.ceil(wordCount / 200) // 假设每分钟200字

    return {
      type: request.type,
      language: request.language || 'zh-CN',
      wordCount,
      characterCount,
      estimatedReadTime,
      // TODO: 实现SEO评分和情感分析
      seoScore: undefined,
      sentimentScore: undefined
    }
  }

  /**
   * 生成建议
   */
  private static async generateSuggestions(
    content: string,
    request: ContentGenerationRequest
  ): Promise<ContentGenerationResponse['suggestions']> {
    // TODO: 实现智能建议生成
    return {
      improvements: [],
      keywords: request.keywords || [],
      hashtags: []
    }
  }

  /**
   * 保存生成历史
   */
  private static async saveGenerationHistory(
    userId: string,
    request: ContentGenerationRequest,
    content: string,
    usage: any
  ): Promise<void> {
    try {
      await prisma.aIGenerationHistory.create({
        data: {
          userId,
          type: 'TEXT_GENERATION',
          prompt: request.prompt,
          result: content,
          metadata: {
            contentType: request.type,
            language: request.language || 'zh-CN',
            tone: request.tone,
            targetAudience: request.targetAudience,
            keywords: request.keywords,
            usage
          }
        }
      })
    } catch (error) {
      logger.warn('保存AI生成历史失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 获取语言名称
   */
  private static getLanguageName(language: string): string {
    const languageNames: Record<string, string> = {
      'zh-CN': '中文',
      'en': '英语',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'it': '意大利语',
      'pt': '葡萄牙语',
      'ru': '俄语'
    }
    
    return languageNames[language] || language
  }

  /**
   * 获取语调描述
   */
  private static getToneDescription(tone: string): string {
    const toneDescriptions: Record<string, string> = {
      'professional': '专业正式',
      'casual': '轻松随意',
      'friendly': '友好亲切',
      'urgent': '紧迫有力',
      'persuasive': '说服性强',
      'informative': '信息丰富'
    }
    
    return toneDescriptions[tone] || tone
  }

  /**
   * 获取优化系统提示词
   */
  private static getOptimizationSystemPrompt(type: string): string {
    const prompts: Record<string, string> = {
      'seo': '你是一位SEO专家，请优化内容以提高搜索引擎排名，包括关键词密度、标题结构、元描述等。',
      'readability': '你是一位内容编辑专家，请优化内容的可读性，包括句子结构、段落组织、用词选择等。',
      'engagement': '你是一位内容营销专家，请优化内容以提高用户参与度，包括吸引力、互动性、情感连接等。',
      'conversion': '你是一位转化率优化专家，请优化内容以提高转化率，包括行动号召、说服力、紧迫感等。'
    }
    
    return prompts[type] || '请优化以下内容。'
  }
}
