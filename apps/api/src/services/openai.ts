import OpenAI from 'openai'
import { config } from '../config'
import { aiLogger } from '../utils/logger'
import { prisma } from '../utils/database'

/**
 * OpenAI服务类
 * 封装OpenAI API调用，提供文本生成、图像生成等功能
 */
export class OpenAIService {
  private client: OpenAI
  
  constructor() {
    this.client = new OpenAI({
      apiKey: config.openai.apiKey,
      organization: config.openai.organization,
    })
  }

  /**
   * 生成营销文案
   * @param prompt 用户提示词
   * @param options 生成选项
   */
  async generateMarketingCopy(
    prompt: string,
    options: {
      type: 'marketing_copy' | 'social_media' | 'email' | 'blog'
      tone: 'professional' | 'casual' | 'friendly' | 'persuasive'
      length: 'short' | 'medium' | 'long'
      userId: string
    }
  ): Promise<{
    content: string
    metadata: {
      model: string
      tokensUsed: number
      generationTime: number
    }
  }> {
    const startTime = Date.now()
    
    try {
      aiLogger.info('开始生成营销文案', {
        userId: options.userId,
        type: options.type,
        tone: options.tone,
        length: options.length,
      })

      // 根据类型和语调构建系统提示词
      const systemPrompt = this.buildSystemPrompt(options.type, options.tone, options.length)
      
      // 调用OpenAI API
      const completion = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: this.getMaxTokens(options.length),
        temperature: this.getTemperature(options.tone),
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      })

      const generatedContent = completion.choices[0]?.message?.content || ''
      const tokensUsed = completion.usage?.total_tokens || 0
      const generationTime = Date.now() - startTime

      // 保存生成历史到数据库
      await this.saveGenerationHistory({
        userId: options.userId,
        type: 'TEXT_GENERATION',
        prompt,
        result: generatedContent,
        metadata: {
          model: 'gpt-3.5-turbo',
          tokensUsed,
          generationTime,
          options,
        },
        status: 'COMPLETED',
      })

      aiLogger.info('营销文案生成成功', {
        userId: options.userId,
        tokensUsed,
        generationTime,
      })

      return {
        content: generatedContent,
        metadata: {
          model: 'gpt-3.5-turbo',
          tokensUsed,
          generationTime,
        },
      }

    } catch (error) {
      const generationTime = Date.now() - startTime
      
      aiLogger.error('营销文案生成失败', {
        userId: options.userId,
        error: error instanceof Error ? error.message : String(error),
        generationTime,
      })

      // 保存失败记录
      await this.saveGenerationHistory({
        userId: options.userId,
        type: 'TEXT_GENERATION',
        prompt,
        result: null,
        metadata: {
          error: error instanceof Error ? error.message : String(error),
          generationTime,
        },
        status: 'FAILED',
        errorMessage: error instanceof Error ? error.message : String(error),
      })

      throw new Error(`文案生成失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 生成图像
   * @param prompt 图像描述提示词
   * @param options 生成选项
   */
  async generateImage(
    prompt: string,
    options: {
      style: 'realistic' | 'cartoon' | 'abstract' | 'minimalist'
      size: '256x256' | '512x512' | '1024x1024'
      userId: string
    }
  ): Promise<{
    imageUrl: string
    metadata: {
      model: string
      size: string
      style: string
      generationTime: number
    }
  }> {
    const startTime = Date.now()
    
    try {
      aiLogger.info('开始生成图像', {
        userId: options.userId,
        style: options.style,
        size: options.size,
      })

      // 根据风格调整提示词
      const enhancedPrompt = this.enhanceImagePrompt(prompt, options.style)

      // 调用DALL-E API
      const response = await this.client.images.generate({
        model: 'dall-e-3',
        prompt: enhancedPrompt,
        size: options.size as '256x256' | '512x512' | '1024x1024',
        quality: 'standard',
        n: 1,
      })

      const imageUrl = response.data[0]?.url || ''
      const generationTime = Date.now() - startTime

      // 保存生成历史到数据库
      await this.saveGenerationHistory({
        userId: options.userId,
        type: 'IMAGE_GENERATION',
        prompt,
        result: imageUrl,
        metadata: {
          model: 'dall-e-3',
          size: options.size,
          style: options.style,
          generationTime,
        },
        status: 'COMPLETED',
      })

      aiLogger.info('图像生成成功', {
        userId: options.userId,
        generationTime,
      })

      return {
        imageUrl,
        metadata: {
          model: 'dall-e-3',
          size: options.size,
          style: options.style,
          generationTime,
        },
      }

    } catch (error) {
      const generationTime = Date.now() - startTime
      
      aiLogger.error('图像生成失败', {
        userId: options.userId,
        error: error instanceof Error ? error.message : String(error),
        generationTime,
      })

      // 保存失败记录
      await this.saveGenerationHistory({
        userId: options.userId,
        type: 'IMAGE_GENERATION',
        prompt,
        result: null,
        metadata: {
          error: error instanceof Error ? error.message : String(error),
          generationTime,
        },
        status: 'FAILED',
        errorMessage: error instanceof Error ? error.message : String(error),
      })

      throw new Error(`图像生成失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 内容优化
   * @param content 原始内容
   * @param options 优化选项
   */
  async optimizeContent(
    content: string,
    options: {
      type: 'grammar' | 'seo' | 'engagement' | 'clarity'
      targetAudience?: string
      userId: string
    }
  ): Promise<{
    optimizedContent: string
    suggestions: string[]
    metadata: {
      model: string
      tokensUsed: number
      generationTime: number
    }
  }> {
    const startTime = Date.now()
    
    try {
      aiLogger.info('开始内容优化', {
        userId: options.userId,
        type: options.type,
      })

      const systemPrompt = this.buildOptimizationPrompt(options.type, options.targetAudience)
      
      const completion = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          {
            role: 'user',
            content: `请优化以下内容：\n\n${content}`,
          },
        ],
        max_tokens: 2000,
        temperature: 0.3,
      })

      const result = completion.choices[0]?.message?.content || ''
      const tokensUsed = completion.usage?.total_tokens || 0
      const generationTime = Date.now() - startTime

      // 解析优化结果（假设返回格式为JSON）
      let optimizedContent = result
      let suggestions: string[] = []

      try {
        const parsed = JSON.parse(result)
        optimizedContent = parsed.optimizedContent || result
        suggestions = parsed.suggestions || []
      } catch {
        // 如果不是JSON格式，直接使用原始结果
        optimizedContent = result
      }

      // 保存生成历史
      await this.saveGenerationHistory({
        userId: options.userId,
        type: 'CONTENT_OPTIMIZATION',
        prompt: content,
        result: optimizedContent,
        metadata: {
          model: 'gpt-3.5-turbo',
          tokensUsed,
          generationTime,
          optimizationType: options.type,
          suggestions,
        },
        status: 'COMPLETED',
      })

      aiLogger.info('内容优化成功', {
        userId: options.userId,
        tokensUsed,
        generationTime,
      })

      return {
        optimizedContent,
        suggestions,
        metadata: {
          model: 'gpt-3.5-turbo',
          tokensUsed,
          generationTime,
        },
      }

    } catch (error) {
      const generationTime = Date.now() - startTime
      
      aiLogger.error('内容优化失败', {
        userId: options.userId,
        error: error instanceof Error ? error.message : String(error),
        generationTime,
      })

      throw new Error(`内容优化失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(
    type: string,
    tone: string,
    length: string
  ): string {
    const basePrompt = '你是一个专业的数字营销文案专家，擅长创作各种类型的营销内容。'
    
    const typePrompts = {
      marketing_copy: '请创作吸引人的营销文案，突出产品或服务的价值主张。',
      social_media: '请创作适合社交媒体平台的内容，要求简洁有趣，容易传播。',
      email: '请创作邮件营销内容，包括吸引人的主题行和正文。',
      blog: '请创作博客文章内容，要求信息丰富，结构清晰。',
    }

    const tonePrompts = {
      professional: '使用专业、正式的语调。',
      casual: '使用轻松、随意的语调。',
      friendly: '使用友好、亲切的语调。',
      persuasive: '使用有说服力、引人行动的语调。',
    }

    const lengthPrompts = {
      short: '内容要简洁明了，控制在100字以内。',
      medium: '内容要适中，控制在200-300字。',
      long: '内容要详细丰富，可以在500字以上。',
    }

    return `${basePrompt} ${typePrompts[type as keyof typeof typePrompts]} ${tonePrompts[tone as keyof typeof tonePrompts]} ${lengthPrompts[length as keyof typeof lengthPrompts]} 请用中文回复。`
  }

  /**
   * 增强图像提示词
   */
  private enhanceImagePrompt(prompt: string, style: string): string {
    const styleEnhancements = {
      realistic: 'photorealistic, high quality, detailed',
      cartoon: 'cartoon style, colorful, playful',
      abstract: 'abstract art, creative, artistic',
      minimalist: 'minimalist design, clean, simple',
    }

    return `${prompt}, ${styleEnhancements[style as keyof typeof styleEnhancements]}`
  }

  /**
   * 构建优化提示词
   */
  private buildOptimizationPrompt(type: string, targetAudience?: string): string {
    const basePrompt = '你是一个内容优化专家，请帮助优化以下内容。'
    
    const typePrompts = {
      grammar: '请检查并修正语法错误，提高文本的准确性和流畅性。',
      seo: '请优化内容以提高搜索引擎排名，包括关键词优化和结构改进。',
      engagement: '请优化内容以提高用户参与度，使其更加吸引人和互动性强。',
      clarity: '请优化内容的清晰度和可读性，使其更容易理解。',
    }

    const audiencePrompt = targetAudience ? `目标受众是：${targetAudience}。` : ''

    return `${basePrompt} ${typePrompts[type as keyof typeof typePrompts]} ${audiencePrompt} 请以JSON格式返回结果，包含optimizedContent和suggestions字段。`
  }

  /**
   * 获取最大token数
   */
  private getMaxTokens(length: string): number {
    const tokenLimits = {
      short: 150,
      medium: 500,
      long: 1000,
    }
    return tokenLimits[length as keyof typeof tokenLimits] || 500
  }

  /**
   * 获取温度参数
   */
  private getTemperature(tone: string): number {
    const temperatures = {
      professional: 0.3,
      casual: 0.7,
      friendly: 0.6,
      persuasive: 0.5,
    }
    return temperatures[tone as keyof typeof temperatures] || 0.5
  }

  /**
   * 保存生成历史到数据库
   */
  private async saveGenerationHistory(data: {
    userId: string
    type: 'TEXT_GENERATION' | 'IMAGE_GENERATION' | 'CONTENT_OPTIMIZATION'
    prompt: string
    result: string | null
    metadata: any
    status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
    errorMessage?: string
  }) {
    try {
      await prisma.aIGenerationHistory.create({
        data: {
          userId: data.userId,
          type: data.type,
          prompt: data.prompt,
          result: data.result,
          metadata: data.metadata,
          status: data.status,
          errorMessage: data.errorMessage,
        },
      })
    } catch (error) {
      aiLogger.error('保存生成历史失败', { error })
    }
  }

  /**
   * 获取用户的生成历史
   */
  async getGenerationHistory(
    userId: string,
    options: {
      type?: 'TEXT_GENERATION' | 'IMAGE_GENERATION' | 'CONTENT_OPTIMIZATION'
      page?: number
      limit?: number
    } = {}
  ) {
    const { type, page = 1, limit = 20 } = options
    const skip = (page - 1) * limit

    const where = {
      userId,
      ...(type && { type }),
    }

    const [items, total] = await Promise.all([
      prisma.aIGenerationHistory.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          type: true,
          prompt: true,
          result: true,
          status: true,
          createdAt: true,
          metadata: true,
        },
      }),
      prisma.aIGenerationHistory.count({ where }),
    ])

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    }
  }
}

// 创建单例实例
export const openaiService = new OpenAIService()
