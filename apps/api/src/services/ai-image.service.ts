// AI图像生成服务
// 提供AI图像生成、图片处理、存储管理和格式转换功能

import { openaiService } from './openai.service'
import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { config } from '../config'
import sharp from 'sharp'
import AWS from 'aws-sdk'
import fs from 'fs/promises'
import path from 'path'
import crypto from 'crypto'

/**
 * 图像生成请求接口
 */
export interface ImageGenerationRequest {
  prompt: string
  style?: 'photographic' | 'digital_art' | 'comic_book' | 'fantasy_art' | 'line_art' | 'anime' | 'cinematic'
  size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792'
  quality?: 'standard' | 'hd'
  count?: number
  negativePrompt?: string
  seed?: number
  aspectRatio?: '1:1' | '16:9' | '9:16' | '4:3' | '3:4'
}

/**
 * 图像处理请求接口
 */
export interface ImageProcessingRequest {
  imageUrl: string
  operations: {
    resize?: { width: number; height: number }
    crop?: { x: number; y: number; width: number; height: number }
    rotate?: number
    flip?: 'horizontal' | 'vertical'
    brightness?: number
    contrast?: number
    saturation?: number
    blur?: number
    sharpen?: number
    watermark?: {
      text: string
      position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
      opacity: number
    }
  }[]
  format?: 'jpeg' | 'png' | 'webp' | 'avif'
  quality?: number
}

/**
 * 图像生成响应接口
 */
export interface ImageGenerationResponse {
  images: {
    id: string
    url: string
    thumbnailUrl: string
    originalUrl: string
    metadata: {
      width: number
      height: number
      format: string
      size: number
      prompt: string
      style?: string
      seed?: number
    }
  }[]
  usage: {
    requestCount: number
    totalCost: number
  }
}

/**
 * AI图像生成服务类
 */
export class AIImageService {
  private s3: AWS.S3
  private uploadDir: string

  constructor() {
    // 初始化S3客户端（如果配置了）
    if (config.aws.accessKeyId) {
      this.s3 = new AWS.S3({
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
        region: config.aws.region
      })
    }

    // 本地上传目录
    this.uploadDir = path.join(process.cwd(), 'uploads', 'images')
  }

  /**
   * 生成图像
   */
  async generateImages(
    request: ImageGenerationRequest,
    userId: string
  ): Promise<ImageGenerationResponse> {
    logger.info('AI图像生成请求开始', {
      userId,
      promptLength: request.prompt.length,
      size: request.size || '1024x1024',
      count: request.count || 1
    })

    try {
      // 内容审核
      const moderationResult = await openaiService.moderateContent(request.prompt)
      if (moderationResult.data === true) {
        throw new ApiError(ErrorCodes.CONTENT_VIOLATION, '图像提示词包含不当内容，请修改后重试')
      }

      // 构建优化后的提示词
      const optimizedPrompt = await this.optimizeImagePrompt(request)

      // 调用OpenAI生成图像
      const aiResponse = await openaiService.generateImage({
        prompt: optimizedPrompt,
        size: request.size || '1024x1024',
        quality: request.quality || 'standard',
        style: this.mapStyleToOpenAI(request.style),
        n: Math.min(request.count || 1, 4) // 限制最多4张
      }, userId)

      if (!aiResponse.success || !aiResponse.data) {
        throw new ApiError(ErrorCodes.AI_GENERATION_FAILED, aiResponse.error || 'AI图像生成失败')
      }

      // 处理生成的图像
      const processedImages = await this.processGeneratedImages(
        aiResponse.data,
        request,
        userId
      )

      // 保存生成历史
      await this.saveImageGenerationHistory(userId, request, processedImages)

      logger.info('AI图像生成请求成功', {
        userId,
        imageCount: processedImages.length,
        totalSize: processedImages.reduce((sum, img) => sum + img.metadata.size, 0)
      })

      return {
        images: processedImages,
        usage: {
          requestCount: processedImages.length,
          totalCost: this.calculateCost(request, processedImages.length)
        }
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('AI图像生成请求失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.AI_GENERATION_FAILED, 'AI图像生成失败')
    }
  }

  /**
   * 处理图像
   */
  async processImage(
    request: ImageProcessingRequest,
    userId: string
  ): Promise<string> {
    logger.info('图像处理请求开始', {
      userId,
      imageUrl: request.imageUrl,
      operationCount: request.operations.length
    })

    try {
      // 下载原始图像
      const imageBuffer = await this.downloadImage(request.imageUrl)
      
      // 创建Sharp实例
      let image = sharp(imageBuffer)

      // 应用处理操作
      for (const operation of request.operations) {
        image = await this.applyImageOperation(image, operation)
      }

      // 设置输出格式和质量
      if (request.format) {
        image = image.toFormat(request.format as any, {
          quality: request.quality || 90
        })
      }

      // 获取处理后的图像缓冲区
      const processedBuffer = await image.toBuffer()

      // 上传处理后的图像
      const processedImageUrl = await this.uploadImage(
        processedBuffer,
        `processed_${Date.now()}.${request.format || 'jpeg'}`,
        userId
      )

      logger.info('图像处理请求成功', {
        userId,
        originalSize: imageBuffer.length,
        processedSize: processedBuffer.length,
        processedUrl: processedImageUrl
      })

      return processedImageUrl
    } catch (error) {
      logger.error('图像处理请求失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.IMAGE_PROCESSING_FAILED, '图像处理失败')
    }
  }

  /**
   * 优化图像提示词
   */
  private async optimizeImagePrompt(request: ImageGenerationRequest): Promise<string> {
    let prompt = request.prompt.trim()

    // 添加风格描述
    if (request.style) {
      const styleDescriptions = {
        'photographic': 'photorealistic, high quality photography',
        'digital_art': 'digital art, detailed illustration',
        'comic_book': 'comic book style, bold colors',
        'fantasy_art': 'fantasy art, magical atmosphere',
        'line_art': 'clean line art, minimalist',
        'anime': 'anime style, manga illustration',
        'cinematic': 'cinematic lighting, movie scene'
      }
      
      const styleDesc = styleDescriptions[request.style]
      if (styleDesc) {
        prompt += `, ${styleDesc}`
      }
    }

    // 添加质量提升词汇
    if (request.quality === 'hd') {
      prompt += ', high resolution, detailed, sharp focus'
    }

    // 添加负面提示词处理
    if (request.negativePrompt) {
      // OpenAI DALL-E 不直接支持负面提示词，但我们可以在提示词中明确说明要避免的内容
      prompt += `, avoiding: ${request.negativePrompt}`
    }

    // 确保提示词长度合理
    if (prompt.length > 1000) {
      prompt = prompt.substring(0, 1000).trim()
    }

    return prompt
  }

  /**
   * 映射风格到OpenAI格式
   */
  private mapStyleToOpenAI(style?: string): 'vivid' | 'natural' | undefined {
    if (!style) return undefined
    
    const styleMapping: Record<string, 'vivid' | 'natural'> = {
      'photographic': 'natural',
      'digital_art': 'vivid',
      'comic_book': 'vivid',
      'fantasy_art': 'vivid',
      'line_art': 'natural',
      'anime': 'vivid',
      'cinematic': 'natural'
    }
    
    return styleMapping[style]
  }

  /**
   * 处理生成的图像
   */
  private async processGeneratedImages(
    imageUrls: string[],
    request: ImageGenerationRequest,
    userId: string
  ): Promise<ImageGenerationResponse['images']> {
    const processedImages: ImageGenerationResponse['images'] = []

    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i]
      
      try {
        // 下载图像
        const imageBuffer = await this.downloadImage(imageUrl)
        
        // 获取图像元数据
        const metadata = await sharp(imageBuffer).metadata()
        
        // 生成唯一ID
        const imageId = crypto.randomUUID()
        
        // 生成文件名
        const fileName = `generated_${imageId}.png`
        
        // 上传原始图像
        const uploadedUrl = await this.uploadImage(imageBuffer, fileName, userId)
        
        // 生成缩略图
        const thumbnailBuffer = await sharp(imageBuffer)
          .resize(256, 256, { fit: 'cover' })
          .jpeg({ quality: 80 })
          .toBuffer()
        
        const thumbnailFileName = `thumbnail_${imageId}.jpg`
        const thumbnailUrl = await this.uploadImage(thumbnailBuffer, thumbnailFileName, userId)

        processedImages.push({
          id: imageId,
          url: uploadedUrl,
          thumbnailUrl,
          originalUrl: imageUrl,
          metadata: {
            width: metadata.width || 0,
            height: metadata.height || 0,
            format: metadata.format || 'png',
            size: imageBuffer.length,
            prompt: request.prompt,
            style: request.style,
            seed: request.seed
          }
        })
      } catch (error) {
        logger.warn('处理生成图像失败', {
          userId,
          imageUrl,
          index: i,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    return processedImages
  }

  /**
   * 下载图像
   */
  private async downloadImage(url: string): Promise<Buffer> {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const arrayBuffer = await response.arrayBuffer()
      return Buffer.from(arrayBuffer)
    } catch (error) {
      logger.error('下载图像失败', {
        url,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.IMAGE_DOWNLOAD_FAILED, '图像下载失败')
    }
  }

  /**
   * 上传图像
   */
  private async uploadImage(
    buffer: Buffer,
    fileName: string,
    userId: string
  ): Promise<string> {
    try {
      if (this.s3 && config.aws.s3Bucket) {
        // 上传到S3
        const key = `images/${userId}/${fileName}`
        
        await this.s3.upload({
          Bucket: config.aws.s3Bucket,
          Key: key,
          Body: buffer,
          ContentType: this.getContentType(fileName),
          ACL: 'public-read'
        }).promise()

        return `https://${config.aws.s3Bucket}.s3.${config.aws.region}.amazonaws.com/${key}`
      } else {
        // 本地存储
        const userDir = path.join(this.uploadDir, userId)
        await fs.mkdir(userDir, { recursive: true })
        
        const filePath = path.join(userDir, fileName)
        await fs.writeFile(filePath, buffer)
        
        return `/uploads/images/${userId}/${fileName}`
      }
    } catch (error) {
      logger.error('上传图像失败', {
        fileName,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.IMAGE_UPLOAD_FAILED, '图像上传失败')
    }
  }

  /**
   * 应用图像操作
   */
  private async applyImageOperation(
    image: sharp.Sharp,
    operation: ImageProcessingRequest['operations'][0]
  ): Promise<sharp.Sharp> {
    if (operation.resize) {
      image = image.resize(operation.resize.width, operation.resize.height)
    }

    if (operation.crop) {
      image = image.extract({
        left: operation.crop.x,
        top: operation.crop.y,
        width: operation.crop.width,
        height: operation.crop.height
      })
    }

    if (operation.rotate) {
      image = image.rotate(operation.rotate)
    }

    if (operation.flip) {
      if (operation.flip === 'horizontal') {
        image = image.flop()
      } else {
        image = image.flip()
      }
    }

    if (operation.brightness || operation.contrast || operation.saturation) {
      image = image.modulate({
        brightness: operation.brightness,
        saturation: operation.saturation
      })
      
      if (operation.contrast) {
        image = image.linear(operation.contrast, 0)
      }
    }

    if (operation.blur) {
      image = image.blur(operation.blur)
    }

    if (operation.sharpen) {
      image = image.sharpen(operation.sharpen)
    }

    if (operation.watermark) {
      // 创建文字水印
      const watermarkSvg = `
        <svg width="200" height="50">
          <text x="10" y="30" font-family="Arial" font-size="16" fill="white" opacity="${operation.watermark.opacity}">
            ${operation.watermark.text}
          </text>
        </svg>
      `
      
      const watermarkBuffer = Buffer.from(watermarkSvg)
      
      // 根据位置计算水印位置
      const { width, height } = await image.metadata()
      let gravity: sharp.Gravity = 'southeast' // 默认右下角
      
      switch (operation.watermark.position) {
        case 'top-left':
          gravity = 'northwest'
          break
        case 'top-right':
          gravity = 'northeast'
          break
        case 'bottom-left':
          gravity = 'southwest'
          break
        case 'bottom-right':
          gravity = 'southeast'
          break
        case 'center':
          gravity = 'center'
          break
      }
      
      image = image.composite([{
        input: watermarkBuffer,
        gravity,
        blend: 'over'
      }])
    }

    return image
  }

  /**
   * 获取内容类型
   */
  private getContentType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase()
    const contentTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.avif': 'image/avif',
      '.gif': 'image/gif'
    }
    
    return contentTypes[ext] || 'image/jpeg'
  }

  /**
   * 计算成本
   */
  private calculateCost(request: ImageGenerationRequest, imageCount: number): number {
    // 基础成本计算（示例）
    const baseCost = 0.02 // 每张图片基础成本
    const qualityMultiplier = request.quality === 'hd' ? 2 : 1
    const sizeMultiplier = request.size?.includes('1792') || request.size?.includes('1024') ? 1.5 : 1
    
    return imageCount * baseCost * qualityMultiplier * sizeMultiplier
  }

  /**
   * 保存图像生成历史
   */
  private async saveImageGenerationHistory(
    userId: string,
    request: ImageGenerationRequest,
    images: ImageGenerationResponse['images']
  ): Promise<void> {
    try {
      await prisma.aIGenerationHistory.create({
        data: {
          userId,
          type: 'IMAGE_GENERATION',
          prompt: request.prompt,
          result: JSON.stringify(images.map(img => ({
            id: img.id,
            url: img.url,
            metadata: img.metadata
          }))),
          metadata: {
            style: request.style,
            size: request.size,
            quality: request.quality,
            count: request.count,
            negativePrompt: request.negativePrompt,
            imageCount: images.length
          }
        }
      })
    } catch (error) {
      logger.warn('保存AI图像生成历史失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 获取用户图像生成历史
   */
  async getUserImageHistory(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    images: any[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    try {
      const skip = (page - 1) * limit

      const [history, total] = await Promise.all([
        prisma.aIGenerationHistory.findMany({
          where: {
            userId,
            type: 'IMAGE_GENERATION'
          },
          select: {
            id: true,
            prompt: true,
            result: true,
            metadata: true,
            createdAt: true
          },
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc'
          }
        }),
        prisma.aIGenerationHistory.count({
          where: {
            userId,
            type: 'IMAGE_GENERATION'
          }
        })
      ])

      const images = history.map(item => ({
        id: item.id,
        prompt: item.prompt,
        images: JSON.parse(item.result),
        metadata: item.metadata,
        createdAt: item.createdAt
      }))

      const totalPages = Math.ceil(total / limit)

      return {
        images,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      logger.error('获取用户图像生成历史失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取图像历史失败')
    }
  }
}

// 创建全局AI图像服务实例
export const aiImageService = new AIImageService()
