// 邮箱验证服务
// 处理邮箱验证、验证码发送、邮箱确认等功能

import crypto from 'crypto'
import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { emailService } from './email.service'
import { ApiError, ErrorCodes } from '../utils/response'
import { DataSanitizer } from '../utils/validation'
import { logger } from '../utils/logger'
import { config } from '../config'

/**
 * 邮箱验证服务类
 */
export class EmailVerificationService {
  /**
   * 发送邮箱验证码
   */
  static async sendVerificationCode(email: string, userId?: string) {
    logger.info('发送邮箱验证码', { email, userId })

    try {
      // 清理邮箱
      const cleanEmail = DataSanitizer.sanitizeEmail(email)

      // 检查发送频率限制
      const rateLimitKey = `email_verification_rate:${cleanEmail}`
      const recentSends = await redis.get(rateLimitKey)
      
      if (recentSends && parseInt(recentSends) >= 3) {
        throw new ApiError(
          ErrorCodes.TOO_MANY_REQUESTS,
          '验证码发送过于频繁，请稍后再试'
        )
      }

      // 生成6位数字验证码
      const verificationCode = this.generateVerificationCode()
      
      // 设置验证码过期时间（15分钟）
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000)

      // 保存验证码到Redis
      const cacheKey = `email_verification:${cleanEmail}`
      await redis.set(cacheKey, {
        code: verificationCode,
        email: cleanEmail,
        userId,
        expiresAt: expiresAt.toISOString(),
        attempts: 0
      }, 900) // 15分钟过期

      // 发送验证码邮件
      await this.sendVerificationCodeEmail(cleanEmail, verificationCode)

      // 更新发送频率限制
      await redis.incr(rateLimitKey)
      await redis.expire(rateLimitKey, 900) // 15分钟过期

      logger.info('邮箱验证码发送成功', { email: cleanEmail, userId })

      return {
        message: '验证码已发送到您的邮箱，请查收',
        expiresAt
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('发送邮箱验证码失败', {
        email,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.EMAIL_SERVICE_ERROR, '验证码发送失败')
    }
  }

  /**
   * 验证邮箱验证码
   */
  static async verifyCode(email: string, code: string, userId?: string) {
    logger.info('验证邮箱验证码', { email, userId })

    try {
      // 清理邮箱
      const cleanEmail = DataSanitizer.sanitizeEmail(email)

      // 从Redis获取验证码信息
      const cacheKey = `email_verification:${cleanEmail}`
      const verificationData = await redis.get(cacheKey)

      if (!verificationData) {
        throw new ApiError(
          ErrorCodes.TOKEN_INVALID,
          '验证码不存在或已过期'
        )
      }

      // 检查验证码是否过期
      const expiresAt = new Date(verificationData.expiresAt)
      if (new Date() > expiresAt) {
        await redis.del(cacheKey)
        throw new ApiError(
          ErrorCodes.TOKEN_EXPIRED,
          '验证码已过期'
        )
      }

      // 检查尝试次数
      if (verificationData.attempts >= 5) {
        await redis.del(cacheKey)
        throw new ApiError(
          ErrorCodes.TOO_MANY_REQUESTS,
          '验证失败次数过多，请重新获取验证码'
        )
      }

      // 验证码比较
      if (verificationData.code !== code) {
        // 增加尝试次数
        verificationData.attempts += 1
        await redis.set(cacheKey, verificationData, 900)
        
        throw new ApiError(
          ErrorCodes.TOKEN_INVALID,
          `验证码错误，还可尝试 ${5 - verificationData.attempts} 次`
        )
      }

      // 验证成功，删除验证码
      await redis.del(cacheKey)

      // 如果有用户ID，更新用户邮箱验证状态
      if (userId) {
        await this.markEmailAsVerified(userId, cleanEmail)
      }

      logger.info('邮箱验证码验证成功', { email: cleanEmail, userId })

      return {
        message: '邮箱验证成功',
        verified: true
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('验证邮箱验证码失败', {
        email,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '验证码验证失败')
    }
  }

  /**
   * 通过令牌验证邮箱
   */
  static async verifyEmailByToken(token: string) {
    logger.info('通过令牌验证邮箱', { token: token.substring(0, 8) + '...' })

    try {
      // 查找用户
      const user = await prisma.user.findFirst({
        where: {
          emailVerificationToken: token,
          emailVerified: false
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          emailVerificationToken: true
        }
      })

      if (!user) {
        throw new ApiError(
          ErrorCodes.TOKEN_INVALID,
          '验证令牌无效或已过期'
        )
      }

      // 更新用户邮箱验证状态
      await this.markEmailAsVerified(user.id, user.email)

      // 发送欢迎邮件
      try {
        await emailService.sendWelcomeEmail(user.email, user.firstName)
      } catch (emailError) {
        logger.warn('发送欢迎邮件失败', {
          userId: user.id,
          email: user.email,
          error: emailError instanceof Error ? emailError.message : String(emailError)
        })
      }

      logger.info('邮箱令牌验证成功', {
        userId: user.id,
        email: user.email
      })

      return {
        message: '邮箱验证成功',
        user: {
          id: user.id,
          email: user.email,
          emailVerified: true
        }
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('邮箱令牌验证失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '邮箱验证失败')
    }
  }

  /**
   * 重新发送邮箱验证
   */
  static async resendEmailVerification(email: string) {
    logger.info('重新发送邮箱验证', { email })

    try {
      // 清理邮箱
      const cleanEmail = DataSanitizer.sanitizeEmail(email)

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { email: cleanEmail },
        select: {
          id: true,
          email: true,
          firstName: true,
          emailVerified: true
        }
      })

      if (!user) {
        // 为了安全，即使用户不存在也返回成功消息
        return {
          message: '如果该邮箱已注册，您将收到验证邮件'
        }
      }

      if (user.emailVerified) {
        throw new ApiError(
          ErrorCodes.VALIDATION_ERROR,
          '邮箱已验证'
        )
      }

      // 生成新的验证令牌
      const newToken = crypto.randomBytes(32).toString('hex')
      
      await prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerificationToken: newToken
        }
      })

      // 发送验证邮件
      await emailService.sendEmailVerification(user.email, newToken)

      logger.info('重新发送邮箱验证成功', {
        userId: user.id,
        email: user.email
      })

      return {
        message: '验证邮件已重新发送，请检查您的邮箱'
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('重新发送邮箱验证失败', {
        email,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.EMAIL_SERVICE_ERROR, '验证邮件发送失败')
    }
  }

  /**
   * 检查邮箱验证状态
   */
  static async checkVerificationStatus(email: string) {
    logger.debug('检查邮箱验证状态', { email })

    try {
      // 清理邮箱
      const cleanEmail = DataSanitizer.sanitizeEmail(email)

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { email: cleanEmail },
        select: {
          id: true,
          email: true,
          emailVerified: true,
          emailVerifiedAt: true
        }
      })

      if (!user) {
        throw new ApiError(ErrorCodes.USER_NOT_FOUND, '用户不存在')
      }

      return {
        email: user.email,
        verified: user.emailVerified,
        verifiedAt: user.emailVerifiedAt
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('检查邮箱验证状态失败', {
        email,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '检查验证状态失败')
    }
  }

  /**
   * 生成6位数字验证码
   */
  private static generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }

  /**
   * 发送验证码邮件
   */
  private static async sendVerificationCodeEmail(email: string, code: string) {
    const subject = '您的邮箱验证码 - AI数字营销平台'
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>邮箱验证码</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4f46e5; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .code { font-size: 32px; font-weight: bold; color: #4f46e5; text-align: center; padding: 20px; background: white; border-radius: 8px; margin: 20px 0; letter-spacing: 4px; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
          .warning { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>AI数字营销平台</h1>
          </div>
          <div class="content">
            <h2>邮箱验证码</h2>
            <p>您的邮箱验证码是：</p>
            <div class="code">${code}</div>
            <div class="warning">
              <p><strong>安全提醒：</strong></p>
              <ul>
                <li>验证码有效期为15分钟</li>
                <li>请不要将验证码告诉他人</li>
                <li>如果您没有请求此验证码，请忽略此邮件</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>如果您有任何疑问，请联系我们的客服团队。</p>
            <p>© 2025 AI数字营销平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
      AI数字营销平台 - 邮箱验证码
      
      您的邮箱验证码是：${code}
      
      安全提醒：
      - 验证码有效期为15分钟
      - 请不要将验证码告诉他人
      - 如果您没有请求此验证码，请忽略此邮件
    `

    await emailService.sendEmail(email, subject, html, text)
  }

  /**
   * 标记邮箱为已验证
   */
  private static async markEmailAsVerified(userId: string, email: string) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        emailVerified: true,
        emailVerifiedAt: new Date(),
        emailVerificationToken: null
      }
    })

    // 清除相关缓存
    await redis.del(`email_verification:${email}`)
    await redis.del(`email_verification_rate:${email}`)

    logger.info('邮箱标记为已验证', { userId, email })
  }
}
