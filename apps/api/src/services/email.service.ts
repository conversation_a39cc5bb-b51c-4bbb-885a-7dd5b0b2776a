// 邮件服务
// 处理邮件发送、模板渲染、邮箱验证等功能

import nodemailer from 'nodemailer'
import { config } from '../config'
import { logger } from '../utils/logger'
import { ApiError, ErrorCodes } from '../utils/response'

/**
 * 邮件模板类型
 */
export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

/**
 * 邮件服务类
 */
export class EmailService {
  private transporter: nodemailer.Transporter

  constructor() {
    this.transporter = this.createTransporter()
  }

  /**
   * 创建邮件传输器
   */
  private createTransporter(): nodemailer.Transporter {
    if (config.email.provider === 'smtp') {
      return nodemailer.createTransporter({
        host: config.email.smtp.host,
        port: config.email.smtp.port,
        secure: config.email.smtp.secure,
        auth: {
          user: config.email.smtp.user,
          pass: config.email.smtp.pass
        },
        pool: true,
        maxConnections: 5,
        maxMessages: 100
      })
    } else if (config.email.provider === 'sendgrid') {
      return nodemailer.createTransporter({
        service: 'SendGrid',
        auth: {
          user: 'apikey',
          pass: config.email.sendgrid.apiKey
        }
      })
    } else {
      // 开发环境使用测试传输器
      return nodemailer.createTransporter({
        streamTransport: true,
        newline: 'unix',
        buffer: true
      })
    }
  }

  /**
   * 发送邮件
   */
  async sendEmail(
    to: string,
    subject: string,
    html: string,
    text?: string
  ): Promise<void> {
    try {
      const mailOptions = {
        from: config.email.from,
        to,
        subject,
        html,
        text: text || this.htmlToText(html)
      }

      const result = await this.transporter.sendMail(mailOptions)
      
      logger.info('邮件发送成功', {
        to,
        subject,
        messageId: result.messageId
      })
    } catch (error) {
      logger.error('邮件发送失败', {
        to,
        subject,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.EMAIL_SERVICE_ERROR, '邮件发送失败')
    }
  }

  /**
   * 发送邮箱验证邮件
   */
  async sendEmailVerification(email: string, token: string): Promise<void> {
    const verificationUrl = `${config.frontend.url}/verify-email?token=${token}`
    
    const template = this.getEmailVerificationTemplate(verificationUrl)
    
    await this.sendEmail(
      email,
      template.subject,
      template.html,
      template.text
    )
  }

  /**
   * 发送密码重置邮件
   */
  async sendPasswordResetEmail(
    email: string,
    firstName: string,
    token: string
  ): Promise<void> {
    const resetUrl = `${config.frontend.url}/reset-password?token=${token}`
    
    const template = this.getPasswordResetTemplate(firstName, resetUrl)
    
    await this.sendEmail(
      email,
      template.subject,
      template.html,
      template.text
    )
  }

  /**
   * 发送欢迎邮件
   */
  async sendWelcomeEmail(email: string, firstName: string): Promise<void> {
    const template = this.getWelcomeTemplate(firstName)
    
    await this.sendEmail(
      email,
      template.subject,
      template.html,
      template.text
    )
  }

  /**
   * 发送密码修改通知邮件
   */
  async sendPasswordChangeNotification(
    email: string,
    firstName: string
  ): Promise<void> {
    const template = this.getPasswordChangeNotificationTemplate(firstName)
    
    await this.sendEmail(
      email,
      template.subject,
      template.html,
      template.text
    )
  }

  /**
   * 获取邮箱验证模板
   */
  private getEmailVerificationTemplate(verificationUrl: string): EmailTemplate {
    const subject = '验证您的邮箱地址 - AI数字营销平台'
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4f46e5; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .button { display: inline-block; padding: 12px 24px; background: #4f46e5; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>AI数字营销平台</h1>
          </div>
          <div class="content">
            <h2>验证您的邮箱地址</h2>
            <p>感谢您注册AI数字营销平台！</p>
            <p>请点击下面的按钮验证您的邮箱地址：</p>
            <a href="${verificationUrl}" class="button">验证邮箱</a>
            <p>如果按钮无法点击，请复制以下链接到浏览器中打开：</p>
            <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
            <p><strong>注意：</strong>此链接将在24小时后失效。</p>
          </div>
          <div class="footer">
            <p>如果您没有注册我们的服务，请忽略此邮件。</p>
            <p>© 2025 AI数字营销平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
      AI数字营销平台 - 邮箱验证
      
      感谢您注册AI数字营销平台！
      
      请访问以下链接验证您的邮箱地址：
      ${verificationUrl}
      
      注意：此链接将在24小时后失效。
      
      如果您没有注册我们的服务，请忽略此邮件。
    `

    return { subject, html, text }
  }

  /**
   * 获取密码重置模板
   */
  private getPasswordResetTemplate(firstName: string, resetUrl: string): EmailTemplate {
    const subject = '重置您的密码 - AI数字营销平台'
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>密码重置</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .button { display: inline-block; padding: 12px 24px; background: #dc2626; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
          .warning { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>AI数字营销平台</h1>
          </div>
          <div class="content">
            <h2>重置您的密码</h2>
            <p>您好，${firstName}！</p>
            <p>我们收到了重置您账户密码的请求。</p>
            <a href="${resetUrl}" class="button">重置密码</a>
            <p>如果按钮无法点击，请复制以下链接到浏览器中打开：</p>
            <p style="word-break: break-all; color: #666;">${resetUrl}</p>
            <div class="warning">
              <p><strong>安全提醒：</strong></p>
              <ul>
                <li>此链接将在1小时后失效</li>
                <li>如果您没有请求重置密码，请忽略此邮件</li>
                <li>请不要将此链接分享给他人</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>如果您有任何疑问，请联系我们的客服团队。</p>
            <p>© 2025 AI数字营销平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
      AI数字营销平台 - 密码重置
      
      您好，${firstName}！
      
      我们收到了重置您账户密码的请求。
      
      请访问以下链接重置您的密码：
      ${resetUrl}
      
      安全提醒：
      - 此链接将在1小时后失效
      - 如果您没有请求重置密码，请忽略此邮件
      - 请不要将此链接分享给他人
      
      如果您有任何疑问，请联系我们的客服团队。
    `

    return { subject, html, text }
  }

  /**
   * 获取欢迎邮件模板
   */
  private getWelcomeTemplate(firstName: string): EmailTemplate {
    const subject = `欢迎加入AI数字营销平台，${firstName}！`
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>欢迎加入</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .button { display: inline-block; padding: 12px 24px; background: #059669; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
          .feature { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #059669; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 欢迎加入AI数字营销平台！</h1>
          </div>
          <div class="content">
            <h2>您好，${firstName}！</h2>
            <p>感谢您选择AI数字营销平台，我们很高兴您的加入！</p>
            
            <h3>您可以开始使用以下功能：</h3>
            <div class="feature">
              <h4>🤖 AI内容生成</h4>
              <p>使用先进的AI技术生成高质量的营销文案和创意内容</p>
            </div>
            <div class="feature">
              <h4>📊 数据分析</h4>
              <p>深入了解您的营销活动效果和用户行为</p>
            </div>
            <div class="feature">
              <h4>🎯 精准营销</h4>
              <p>创建和管理多渠道营销活动，提升转化率</p>
            </div>
            
            <a href="${config.frontend.url}/dashboard" class="button">开始使用</a>
            
            <p>如果您有任何问题或需要帮助，请随时联系我们的客服团队。</p>
          </div>
          <div class="footer">
            <p>祝您使用愉快！</p>
            <p>© 2025 AI数字营销平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
      欢迎加入AI数字营销平台！
      
      您好，${firstName}！
      
      感谢您选择AI数字营销平台，我们很高兴您的加入！
      
      您可以开始使用以下功能：
      
      🤖 AI内容生成
      使用先进的AI技术生成高质量的营销文案和创意内容
      
      📊 数据分析
      深入了解您的营销活动效果和用户行为
      
      🎯 精准营销
      创建和管理多渠道营销活动，提升转化率
      
      立即开始：${config.frontend.url}/dashboard
      
      如果您有任何问题或需要帮助，请随时联系我们的客服团队。
      
      祝您使用愉快！
    `

    return { subject, html, text }
  }

  /**
   * 获取密码修改通知模板
   */
  private getPasswordChangeNotificationTemplate(firstName: string): EmailTemplate {
    const subject = '密码已修改 - AI数字营销平台'
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>密码修改通知</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f59e0b; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
          .info { background: #fef3c7; border: 1px solid #fcd34d; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>AI数字营销平台</h1>
          </div>
          <div class="content">
            <h2>密码修改通知</h2>
            <p>您好，${firstName}！</p>
            <p>您的账户密码已成功修改。</p>
            <div class="info">
              <p><strong>修改时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
              <p>如果这不是您本人的操作，请立即联系我们的客服团队。</p>
            </div>
            <p>为了保护您的账户安全，建议您：</p>
            <ul>
              <li>定期更换密码</li>
              <li>使用强密码</li>
              <li>不要在多个网站使用相同密码</li>
              <li>启用两步验证（即将推出）</li>
            </ul>
          </div>
          <div class="footer">
            <p>如果您有任何疑问，请联系我们的客服团队。</p>
            <p>© 2025 AI数字营销平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `

    const text = `
      AI数字营销平台 - 密码修改通知
      
      您好，${firstName}！
      
      您的账户密码已成功修改。
      
      修改时间：${new Date().toLocaleString('zh-CN')}
      
      如果这不是您本人的操作，请立即联系我们的客服团队。
      
      为了保护您的账户安全，建议您：
      - 定期更换密码
      - 使用强密码
      - 不要在多个网站使用相同密码
      - 启用两步验证（即将推出）
    `

    return { subject, html, text }
  }

  /**
   * 将HTML转换为纯文本
   */
  private htmlToText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/\s+/g, ' ') // 合并空白字符
      .trim()
  }

  /**
   * 验证邮件传输器连接
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify()
      logger.info('邮件服务连接验证成功')
      return true
    } catch (error) {
      logger.error('邮件服务连接验证失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      return false
    }
  }
}

// 创建全局邮件服务实例
export const emailService = new EmailService()
