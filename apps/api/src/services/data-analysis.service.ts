// 数据分析引擎服务
// 提供高级数据分析、统计计算、趋势分析等功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'

/**
 * 分析类型
 */
export enum AnalysisType {
  COHORT = 'COHORT',
  FUNNEL = 'FUNNEL',
  RETENTION = 'RETENTION',
  SEGMENTATION = 'SEGMENTATION',
  ATTRIBUTION = 'ATTRIBUTION',
  LIFETIME_VALUE = 'LIFETIME_VALUE',
  CHURN_PREDICTION = 'CHURN_PREDICTION',
  ANOMALY_DETECTION = 'ANOMALY_DETECTION'
}

/**
 * 队列分析结果
 */
export interface CohortAnalysisResult {
  cohorts: {
    cohortDate: string
    cohortSize: number
    periods: {
      period: number
      users: number
      retentionRate: number
    }[]
  }[]
  summary: {
    averageRetention: number[]
    bestCohort: string
    worstCohort: string
  }
}

/**
 * 漏斗分析结果
 */
export interface FunnelAnalysisResult {
  steps: {
    name: string
    users: number
    conversionRate: number
    dropoffRate: number
  }[]
  summary: {
    totalUsers: number
    overallConversionRate: number
    biggestDropoff: string
  }
}

/**
 * 用户细分结果
 */
export interface SegmentationResult {
  segments: {
    name: string
    description: string
    userCount: number
    percentage: number
    characteristics: Record<string, any>
    metrics: {
      averageRevenue: number
      averageSessionDuration: number
      conversionRate: number
    }
  }[]
  insights: string[]
}

/**
 * 归因分析结果
 */
export interface AttributionResult {
  channels: {
    channel: string
    touchpoints: number
    conversions: number
    revenue: number
    costPerAcquisition: number
    returnOnAdSpend: number
  }[]
  models: {
    firstTouch: Record<string, number>
    lastTouch: Record<string, number>
    linear: Record<string, number>
    timeDecay: Record<string, number>
  }
}

/**
 * 数据分析引擎服务类
 */
export class DataAnalysisService {
  /**
   * 队列分析
   */
  static async performCohortAnalysis(
    userId: string,
    startDate: Date,
    endDate: Date,
    cohortType: 'daily' | 'weekly' | 'monthly' = 'weekly'
  ): Promise<CohortAnalysisResult> {
    logger.info('执行队列分析', { userId, cohortType, startDate, endDate })

    try {
      // 获取用户首次访问数据
      const firstVisits = await prisma.analyticsEvent.groupBy({
        by: ['userId'],
        where: {
          userId: { not: null },
          type: 'PAGE_VIEW',
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        },
        _min: {
          timestamp: true
        }
      })

      // 按队列分组用户
      const cohorts = new Map<string, string[]>()
      
      for (const visit of firstVisits) {
        if (!visit.userId || !visit._min.timestamp) continue
        
        const cohortDate = this.getCohortDate(visit._min.timestamp, cohortType)
        
        if (!cohorts.has(cohortDate)) {
          cohorts.set(cohortDate, [])
        }
        cohorts.get(cohortDate)!.push(visit.userId)
      }

      // 计算每个队列的留存率
      const cohortResults = []
      
      for (const [cohortDate, userIds] of cohorts) {
        const cohortStartDate = new Date(cohortDate)
        const periods = []

        // 计算各个周期的留存率
        for (let period = 0; period < 12; period++) {
          const periodStart = new Date(cohortStartDate)
          const periodEnd = new Date(cohortStartDate)

          switch (cohortType) {
            case 'daily':
              periodStart.setDate(periodStart.getDate() + period)
              periodEnd.setDate(periodEnd.getDate() + period + 1)
              break
            case 'weekly':
              periodStart.setDate(periodStart.getDate() + period * 7)
              periodEnd.setDate(periodEnd.getDate() + (period + 1) * 7)
              break
            case 'monthly':
              periodStart.setMonth(periodStart.getMonth() + period)
              periodEnd.setMonth(periodEnd.getMonth() + period + 1)
              break
          }

          // 查询该周期内活跃的用户
          const activeUsers = await prisma.analyticsEvent.findMany({
            where: {
              userId: { in: userIds },
              timestamp: {
                gte: periodStart,
                lt: periodEnd
              }
            },
            select: { userId: true },
            distinct: ['userId']
          })

          const retentionRate = userIds.length > 0 ? 
            (activeUsers.length / userIds.length) * 100 : 0

          periods.push({
            period,
            users: activeUsers.length,
            retentionRate: Math.round(retentionRate * 100) / 100
          })
        }

        cohortResults.push({
          cohortDate,
          cohortSize: userIds.length,
          periods
        })
      }

      // 计算汇总统计
      const summary = this.calculateCohortSummary(cohortResults)

      logger.info('队列分析完成', {
        userId,
        cohortCount: cohortResults.length
      })

      return {
        cohorts: cohortResults,
        summary
      }
    } catch (error) {
      logger.error('队列分析失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '队列分析失败')
    }
  }

  /**
   * 漏斗分析
   */
  static async performFunnelAnalysis(
    userId: string,
    steps: { name: string; eventType: string; filters?: any }[],
    startDate: Date,
    endDate: Date
  ): Promise<FunnelAnalysisResult> {
    logger.info('执行漏斗分析', { userId, stepCount: steps.length })

    try {
      const funnelSteps = []
      let previousUsers = new Set<string>()
      let isFirstStep = true

      for (const [index, step] of steps.entries()) {
        // 构建查询条件
        const where: any = {
          type: step.eventType,
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        }

        // 应用过滤器
        if (step.filters) {
          Object.assign(where, step.filters)
        }

        // 如果不是第一步，只查询上一步的用户
        if (!isFirstStep && previousUsers.size > 0) {
          where.userId = { in: Array.from(previousUsers) }
        }

        // 查询该步骤的用户
        const stepUsers = await prisma.analyticsEvent.findMany({
          where,
          select: { userId: true },
          distinct: ['userId']
        })

        const currentUsers = new Set(
          stepUsers.map(u => u.userId).filter(Boolean) as string[]
        )

        // 计算转化率
        const conversionRate = isFirstStep ? 100 : 
          previousUsers.size > 0 ? (currentUsers.size / previousUsers.size) * 100 : 0

        const dropoffRate = 100 - conversionRate

        funnelSteps.push({
          name: step.name,
          users: currentUsers.size,
          conversionRate: Math.round(conversionRate * 100) / 100,
          dropoffRate: Math.round(dropoffRate * 100) / 100
        })

        previousUsers = currentUsers
        isFirstStep = false
      }

      // 计算汇总统计
      const totalUsers = funnelSteps[0]?.users || 0
      const finalUsers = funnelSteps[funnelSteps.length - 1]?.users || 0
      const overallConversionRate = totalUsers > 0 ? (finalUsers / totalUsers) * 100 : 0

      // 找出最大流失步骤
      let biggestDropoff = ''
      let maxDropoff = 0
      for (const step of funnelSteps) {
        if (step.dropoffRate > maxDropoff) {
          maxDropoff = step.dropoffRate
          biggestDropoff = step.name
        }
      }

      logger.info('漏斗分析完成', {
        userId,
        totalUsers,
        overallConversionRate
      })

      return {
        steps: funnelSteps,
        summary: {
          totalUsers,
          overallConversionRate: Math.round(overallConversionRate * 100) / 100,
          biggestDropoff
        }
      }
    } catch (error) {
      logger.error('漏斗分析失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '漏斗分析失败')
    }
  }

  /**
   * 用户细分分析
   */
  static async performSegmentationAnalysis(
    userId: string,
    segmentationCriteria: {
      demographic?: any
      behavioral?: any
      geographic?: any
      psychographic?: any
    },
    startDate: Date,
    endDate: Date
  ): Promise<SegmentationResult> {
    logger.info('执行用户细分分析', { userId })

    try {
      // 获取用户数据
      const users = await prisma.user.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        include: {
          profile: true
        }
      })

      // 获取用户行为数据
      const userBehavior = await this.getUserBehaviorMetrics(
        users.map(u => u.id),
        startDate,
        endDate
      )

      // 执行细分
      const segments = this.performUserSegmentation(
        users,
        userBehavior,
        segmentationCriteria
      )

      // 生成洞察
      const insights = this.generateSegmentationInsights(segments)

      logger.info('用户细分分析完成', {
        userId,
        segmentCount: segments.length
      })

      return {
        segments,
        insights
      }
    } catch (error) {
      logger.error('用户细分分析失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '用户细分分析失败')
    }
  }

  /**
   * 归因分析
   */
  static async performAttributionAnalysis(
    userId: string,
    conversionEvent: string,
    touchpointEvents: string[],
    startDate: Date,
    endDate: Date,
    lookbackWindow: number = 30
  ): Promise<AttributionResult> {
    logger.info('执行归因分析', { userId, conversionEvent })

    try {
      // 获取转化事件
      const conversions = await prisma.analyticsEvent.findMany({
        where: {
          type: conversionEvent as any,
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        }
      })

      // 获取每个转化的触点路径
      const attributionData = []

      for (const conversion of conversions) {
        if (!conversion.userId) continue

        const lookbackStart = new Date(conversion.timestamp)
        lookbackStart.setDate(lookbackStart.getDate() - lookbackWindow)

        // 获取转化前的触点
        const touchpoints = await prisma.analyticsEvent.findMany({
          where: {
            userId: conversion.userId,
            type: { in: touchpointEvents as any[] },
            timestamp: {
              gte: lookbackStart,
              lt: conversion.timestamp
            }
          },
          orderBy: { timestamp: 'asc' }
        })

        if (touchpoints.length > 0) {
          attributionData.push({
            conversionId: conversion.id,
            userId: conversion.userId,
            conversionValue: conversion.properties?.value || 0,
            touchpoints: touchpoints.map(tp => ({
              channel: tp.properties?.channel || 'direct',
              timestamp: tp.timestamp,
              cost: tp.properties?.cost || 0
            }))
          })
        }
      }

      // 计算不同归因模型
      const channels = this.calculateChannelMetrics(attributionData)
      const models = this.calculateAttributionModels(attributionData)

      logger.info('归因分析完成', {
        userId,
        conversionCount: conversions.length,
        channelCount: channels.length
      })

      return {
        channels,
        models
      }
    } catch (error) {
      logger.error('归因分析失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '归因分析失败')
    }
  }

  /**
   * 异常检测
   */
  static async performAnomalyDetection(
    userId: string,
    metric: string,
    startDate: Date,
    endDate: Date,
    sensitivity: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<{
    anomalies: {
      timestamp: string
      value: number
      expectedValue: number
      deviation: number
      severity: 'low' | 'medium' | 'high'
    }[]
    summary: {
      totalAnomalies: number
      severityDistribution: Record<string, number>
      mostAnomalousDay: string
    }
  }> {
    logger.info('执行异常检测', { userId, metric, sensitivity })

    try {
      // 获取历史数据
      const historicalData = await this.getMetricTimeSeries(
        userId,
        metric,
        startDate,
        endDate
      )

      // 计算基线和阈值
      const baseline = this.calculateBaseline(historicalData)
      const threshold = this.calculateAnomalyThreshold(baseline, sensitivity)

      // 检测异常
      const anomalies = []
      for (const dataPoint of historicalData) {
        const deviation = Math.abs(dataPoint.value - baseline.mean)
        
        if (deviation > threshold) {
          const severity = this.classifyAnomalySeverity(deviation, threshold)
          
          anomalies.push({
            timestamp: dataPoint.timestamp,
            value: dataPoint.value,
            expectedValue: baseline.mean,
            deviation,
            severity
          })
        }
      }

      // 计算汇总统计
      const severityDistribution = anomalies.reduce((acc, anomaly) => {
        acc[anomaly.severity] = (acc[anomaly.severity] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const mostAnomalousDay = anomalies.length > 0 ? 
        anomalies.reduce((max, current) => 
          current.deviation > max.deviation ? current : max
        ).timestamp : ''

      logger.info('异常检测完成', {
        userId,
        anomalyCount: anomalies.length
      })

      return {
        anomalies,
        summary: {
          totalAnomalies: anomalies.length,
          severityDistribution,
          mostAnomalousDay
        }
      }
    } catch (error) {
      logger.error('异常检测失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '异常检测失败')
    }
  }

  /**
   * 获取队列日期
   */
  private static getCohortDate(date: Date, cohortType: string): string {
    const d = new Date(date)
    
    switch (cohortType) {
      case 'daily':
        return d.toISOString().split('T')[0]
      case 'weekly':
        const weekStart = new Date(d)
        weekStart.setDate(d.getDate() - d.getDay())
        return weekStart.toISOString().split('T')[0]
      case 'monthly':
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-01`
      default:
        return d.toISOString().split('T')[0]
    }
  }

  /**
   * 计算队列汇总统计
   */
  private static calculateCohortSummary(cohorts: any[]): any {
    if (cohorts.length === 0) {
      return {
        averageRetention: [],
        bestCohort: '',
        worstCohort: ''
      }
    }

    // 计算平均留存率
    const maxPeriods = Math.max(...cohorts.map(c => c.periods.length))
    const averageRetention = []

    for (let period = 0; period < maxPeriods; period++) {
      const periodRetentions = cohorts
        .map(c => c.periods[period]?.retentionRate || 0)
        .filter(r => r > 0)
      
      const average = periodRetentions.length > 0 ? 
        periodRetentions.reduce((sum, r) => sum + r, 0) / periodRetentions.length : 0
      
      averageRetention.push(Math.round(average * 100) / 100)
    }

    // 找出最佳和最差队列
    let bestCohort = cohorts[0].cohortDate
    let worstCohort = cohorts[0].cohortDate
    let bestRetention = 0
    let worstRetention = 100

    for (const cohort of cohorts) {
      // 使用第4周的留存率作为比较标准
      const retention = cohort.periods[3]?.retentionRate || 0
      
      if (retention > bestRetention) {
        bestRetention = retention
        bestCohort = cohort.cohortDate
      }
      
      if (retention < worstRetention) {
        worstRetention = retention
        worstCohort = cohort.cohortDate
      }
    }

    return {
      averageRetention,
      bestCohort,
      worstCohort
    }
  }

  /**
   * 获取用户行为指标
   */
  private static async getUserBehaviorMetrics(
    userIds: string[],
    startDate: Date,
    endDate: Date
  ): Promise<Map<string, any>> {
    const behaviorMetrics = new Map()

    // 批量查询用户行为数据
    const events = await prisma.analyticsEvent.findMany({
      where: {
        userId: { in: userIds },
        timestamp: {
          gte: startDate,
          lte: endDate
        }
      }
    })

    // 按用户聚合数据
    for (const userId of userIds) {
      const userEvents = events.filter(e => e.userId === userId)
      
      const metrics = {
        totalEvents: userEvents.length,
        sessionCount: new Set(userEvents.map(e => e.sessionId)).size,
        pageViews: userEvents.filter(e => e.type === 'PAGE_VIEW').length,
        purchases: userEvents.filter(e => e.type === 'PURCHASE').length,
        totalRevenue: userEvents
          .filter(e => e.type === 'PURCHASE')
          .reduce((sum, e) => sum + (e.properties?.value || 0), 0),
        avgSessionDuration: 0, // 需要更复杂的计算
        lastActiveDate: userEvents.length > 0 ? 
          Math.max(...userEvents.map(e => new Date(e.timestamp).getTime())) : 0
      }

      behaviorMetrics.set(userId, metrics)
    }

    return behaviorMetrics
  }

  /**
   * 执行用户细分
   */
  private static performUserSegmentation(
    users: any[],
    behaviorMetrics: Map<string, any>,
    criteria: any
  ): any[] {
    const segments = [
      {
        name: '高价值用户',
        description: '购买频次高、消费金额大的用户',
        users: [],
        characteristics: { minRevenue: 1000, minPurchases: 5 }
      },
      {
        name: '活跃用户',
        description: '访问频次高但购买较少的用户',
        users: [],
        characteristics: { minPageViews: 50, maxPurchases: 2 }
      },
      {
        name: '新用户',
        description: '最近注册的用户',
        users: [],
        characteristics: { maxDaysSinceSignup: 30 }
      },
      {
        name: '流失风险用户',
        description: '长时间未活跃的用户',
        users: [],
        characteristics: { minDaysSinceLastActive: 30 }
      }
    ]

    // 将用户分配到细分
    for (const user of users) {
      const metrics = behaviorMetrics.get(user.id) || {}
      const daysSinceSignup = Math.floor(
        (Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24)
      )
      const daysSinceLastActive = metrics.lastActiveDate ? 
        Math.floor((Date.now() - metrics.lastActiveDate) / (1000 * 60 * 60 * 24)) : 999

      // 高价值用户
      if (metrics.totalRevenue >= 1000 && metrics.purchases >= 5) {
        segments[0].users.push(user)
      }
      // 活跃用户
      else if (metrics.pageViews >= 50 && metrics.purchases <= 2) {
        segments[1].users.push(user)
      }
      // 新用户
      else if (daysSinceSignup <= 30) {
        segments[2].users.push(user)
      }
      // 流失风险用户
      else if (daysSinceLastActive >= 30) {
        segments[3].users.push(user)
      }
    }

    // 计算细分指标
    const totalUsers = users.length
    return segments.map(segment => ({
      ...segment,
      userCount: segment.users.length,
      percentage: totalUsers > 0 ? (segment.users.length / totalUsers) * 100 : 0,
      metrics: this.calculateSegmentMetrics(segment.users, behaviorMetrics)
    }))
  }

  /**
   * 计算细分指标
   */
  private static calculateSegmentMetrics(users: any[], behaviorMetrics: Map<string, any>): any {
    if (users.length === 0) {
      return {
        averageRevenue: 0,
        averageSessionDuration: 0,
        conversionRate: 0
      }
    }

    let totalRevenue = 0
    let totalSessions = 0
    let totalPurchases = 0

    for (const user of users) {
      const metrics = behaviorMetrics.get(user.id) || {}
      totalRevenue += metrics.totalRevenue || 0
      totalSessions += metrics.sessionCount || 0
      totalPurchases += metrics.purchases || 0
    }

    return {
      averageRevenue: Math.round((totalRevenue / users.length) * 100) / 100,
      averageSessionDuration: 0, // 需要实现
      conversionRate: totalSessions > 0 ? (totalPurchases / totalSessions) * 100 : 0
    }
  }

  /**
   * 生成细分洞察
   */
  private static generateSegmentationInsights(segments: any[]): string[] {
    const insights = []

    // 找出最大的细分
    const largestSegment = segments.reduce((max, current) => 
      current.userCount > max.userCount ? current : max
    )
    insights.push(`最大的用户群体是"${largestSegment.name}"，占总用户的${largestSegment.percentage.toFixed(1)}%`)

    // 找出最有价值的细分
    const mostValuableSegment = segments.reduce((max, current) => 
      current.metrics.averageRevenue > max.metrics.averageRevenue ? current : max
    )
    insights.push(`"${mostValuableSegment.name}"是最有价值的用户群体，平均收入为¥${mostValuableSegment.metrics.averageRevenue}`)

    return insights
  }

  /**
   * 计算渠道指标
   */
  private static calculateChannelMetrics(attributionData: any[]): any[] {
    const channelMetrics = new Map()

    for (const conversion of attributionData) {
      for (const touchpoint of conversion.touchpoints) {
        const channel = touchpoint.channel
        
        if (!channelMetrics.has(channel)) {
          channelMetrics.set(channel, {
            touchpoints: 0,
            conversions: 0,
            revenue: 0,
            cost: 0
          })
        }

        const metrics = channelMetrics.get(channel)
        metrics.touchpoints++
        metrics.revenue += conversion.conversionValue
        metrics.cost += touchpoint.cost
      }

      // 为每个转化的所有渠道增加转化计数
      const channels = new Set(conversion.touchpoints.map((tp: any) => tp.channel))
      for (const channel of channels) {
        const metrics = channelMetrics.get(channel)
        if (metrics) {
          metrics.conversions++
        }
      }
    }

    return Array.from(channelMetrics.entries()).map(([channel, metrics]) => ({
      channel,
      touchpoints: metrics.touchpoints,
      conversions: metrics.conversions,
      revenue: metrics.revenue,
      costPerAcquisition: metrics.conversions > 0 ? metrics.cost / metrics.conversions : 0,
      returnOnAdSpend: metrics.cost > 0 ? metrics.revenue / metrics.cost : 0
    }))
  }

  /**
   * 计算归因模型
   */
  private static calculateAttributionModels(attributionData: any[]): any {
    const models = {
      firstTouch: new Map(),
      lastTouch: new Map(),
      linear: new Map(),
      timeDecay: new Map()
    }

    for (const conversion of attributionData) {
      const touchpoints = conversion.touchpoints
      const value = conversion.conversionValue

      if (touchpoints.length === 0) continue

      // 首次接触归因
      const firstChannel = touchpoints[0].channel
      models.firstTouch.set(firstChannel, (models.firstTouch.get(firstChannel) || 0) + value)

      // 最后接触归因
      const lastChannel = touchpoints[touchpoints.length - 1].channel
      models.lastTouch.set(lastChannel, (models.lastTouch.get(lastChannel) || 0) + value)

      // 线性归因
      const linearValue = value / touchpoints.length
      for (const touchpoint of touchpoints) {
        models.linear.set(
          touchpoint.channel,
          (models.linear.get(touchpoint.channel) || 0) + linearValue
        )
      }

      // 时间衰减归因
      const totalWeight = touchpoints.reduce((sum, _, index) => sum + Math.pow(0.5, touchpoints.length - index - 1), 0)
      for (let i = 0; i < touchpoints.length; i++) {
        const weight = Math.pow(0.5, touchpoints.length - i - 1) / totalWeight
        const weightedValue = value * weight
        models.timeDecay.set(
          touchpoints[i].channel,
          (models.timeDecay.get(touchpoints[i].channel) || 0) + weightedValue
        )
      }
    }

    return {
      firstTouch: Object.fromEntries(models.firstTouch),
      lastTouch: Object.fromEntries(models.lastTouch),
      linear: Object.fromEntries(models.linear),
      timeDecay: Object.fromEntries(models.timeDecay)
    }
  }

  /**
   * 获取指标时间序列
   */
  private static async getMetricTimeSeries(
    userId: string,
    metric: string,
    startDate: Date,
    endDate: Date
  ): Promise<{ timestamp: string; value: number }[]> {
    // 这里应该根据具体的指标类型查询相应的数据
    // 简化实现，返回模拟数据
    const data = []
    const current = new Date(startDate)
    
    while (current <= endDate) {
      // 模拟数据生成
      const baseValue = 100
      const randomVariation = (Math.random() - 0.5) * 20
      const value = baseValue + randomVariation

      data.push({
        timestamp: current.toISOString().split('T')[0],
        value
      })

      current.setDate(current.getDate() + 1)
    }

    return data
  }

  /**
   * 计算基线
   */
  private static calculateBaseline(data: { value: number }[]): { mean: number; std: number } {
    const values = data.map(d => d.value)
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length
    const std = Math.sqrt(variance)

    return { mean, std }
  }

  /**
   * 计算异常阈值
   */
  private static calculateAnomalyThreshold(baseline: { std: number }, sensitivity: string): number {
    const multipliers = {
      low: 3,
      medium: 2,
      high: 1.5
    }

    return baseline.std * multipliers[sensitivity as keyof typeof multipliers]
  }

  /**
   * 分类异常严重程度
   */
  private static classifyAnomalySeverity(deviation: number, threshold: number): 'low' | 'medium' | 'high' {
    if (deviation > threshold * 2) return 'high'
    if (deviation > threshold * 1.5) return 'medium'
    return 'low'
  }
}
