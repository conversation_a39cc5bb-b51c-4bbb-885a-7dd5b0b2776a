// 邮件营销系统服务
// 提供邮件模板、发送管理、统计分析等邮件营销功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { emailService } from './email.service'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { DataSanitizer } from '../utils/validation'
import { config } from '../config'

/**
 * 邮件模板类型
 */
export enum EmailTemplateType {
  WELCOME = 'WELCOME',
  NEWSLETTER = 'NEWSLETTER',
  PROMOTIONAL = 'PROMOTIONAL',
  TRANSACTIONAL = 'TRANSACTIONAL',
  FOLLOW_UP = 'FOLLOW_UP',
  ABANDONED_CART = 'ABANDONED_CART',
  SURVEY = 'SURVEY',
  EVENT_INVITATION = 'EVENT_INVITATION'
}

/**
 * 邮件发送状态
 */
export enum EmailSendStatus {
  PENDING = 'PENDING',
  SENDING = 'SENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  OPENED = 'OPENED',
  CLICKED = 'CLICKED',
  BOUNCED = 'BOUNCED',
  COMPLAINED = 'COMPLAINED',
  UNSUBSCRIBED = 'UNSUBSCRIBED',
  FAILED = 'FAILED'
}

/**
 * 邮件模板创建请求接口
 */
export interface CreateEmailTemplateRequest {
  name: string
  description?: string
  type: EmailTemplateType
  subject: string
  htmlContent: string
  textContent?: string
  variables?: string[]
  previewText?: string
  tags?: string[]
  isActive?: boolean
}

/**
 * 邮件发送请求接口
 */
export interface SendEmailRequest {
  templateId?: string
  recipients: {
    email: string
    name?: string
    variables?: Record<string, any>
  }[]
  subject?: string
  htmlContent?: string
  textContent?: string
  fromName?: string
  fromEmail?: string
  replyTo?: string
  trackOpens?: boolean
  trackClicks?: boolean
  scheduleAt?: Date
  tags?: string[]
}

/**
 * 邮件列表接口
 */
export interface EmailList {
  id: string
  name: string
  description?: string
  subscribers: {
    email: string
    name?: string
    status: 'SUBSCRIBED' | 'UNSUBSCRIBED' | 'BOUNCED'
    subscribedAt: Date
    metadata?: Record<string, any>
  }[]
  tags?: string[]
  createdAt: Date
  updatedAt: Date
}

/**
 * 邮件营销统计接口
 */
export interface EmailMarketingStats {
  totalSent: number
  totalDelivered: number
  totalOpened: number
  totalClicked: number
  totalBounced: number
  totalComplaints: number
  totalUnsubscribed: number
  deliveryRate: number
  openRate: number
  clickRate: number
  bounceRate: number
  complaintRate: number
  unsubscribeRate: number
}

/**
 * 邮件营销服务类
 */
export class EmailMarketingService {
  /**
   * 创建邮件模板
   */
  static async createEmailTemplate(
    request: CreateEmailTemplateRequest,
    userId: string
  ): Promise<any> {
    logger.info('创建邮件模板', { userId, templateName: request.name })

    try {
      // 数据验证和清理
      const cleanData = this.validateAndCleanTemplateData(request)

      // 检查模板名称是否重复
      const existingTemplate = await prisma.emailTemplate.findFirst({
        where: {
          name: cleanData.name,
          userId,
          deletedAt: null
        }
      })

      if (existingTemplate) {
        throw new ApiError(ErrorCodes.VALIDATION_ERROR, '模板名称已存在')
      }

      // 创建邮件模板
      const template = await prisma.emailTemplate.create({
        data: {
          userId,
          name: cleanData.name,
          description: cleanData.description,
          type: cleanData.type,
          subject: cleanData.subject,
          htmlContent: cleanData.htmlContent,
          textContent: cleanData.textContent,
          variables: cleanData.variables || [],
          previewText: cleanData.previewText,
          tags: cleanData.tags || [],
          isActive: cleanData.isActive ?? true,
          metadata: {
            createdBy: userId,
            version: 1
          }
        }
      })

      // 清除相关缓存
      await this.clearUserTemplateCache(userId)

      logger.info('邮件模板创建成功', {
        userId,
        templateId: template.id,
        templateName: template.name
      })

      return template
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('创建邮件模板失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建邮件模板失败')
    }
  }

  /**
   * 发送邮件
   */
  static async sendEmail(
    request: SendEmailRequest,
    userId: string
  ): Promise<{
    batchId: string
    totalRecipients: number
    estimatedDeliveryTime: Date
  }> {
    logger.info('发送营销邮件', {
      userId,
      recipientCount: request.recipients.length,
      templateId: request.templateId
    })

    try {
      // 验证发送权限和限制
      await this.validateSendingLimits(userId, request.recipients.length)

      let emailContent: { subject: string; htmlContent: string; textContent?: string }

      if (request.templateId) {
        // 使用模板发送
        const template = await this.getEmailTemplate(request.templateId, userId)
        emailContent = {
          subject: request.subject || template.subject,
          htmlContent: template.htmlContent,
          textContent: template.textContent
        }
      } else {
        // 直接发送
        if (!request.subject || !request.htmlContent) {
          throw new ApiError(ErrorCodes.VALIDATION_ERROR, '缺少邮件主题或内容')
        }
        emailContent = {
          subject: request.subject,
          htmlContent: request.htmlContent,
          textContent: request.textContent
        }
      }

      // 创建邮件批次
      const batch = await prisma.emailBatch.create({
        data: {
          userId,
          templateId: request.templateId,
          subject: emailContent.subject,
          totalRecipients: request.recipients.length,
          status: 'PENDING',
          scheduledAt: request.scheduleAt || new Date(),
          settings: {
            trackOpens: request.trackOpens ?? true,
            trackClicks: request.trackClicks ?? true,
            fromName: request.fromName,
            fromEmail: request.fromEmail,
            replyTo: request.replyTo
          },
          tags: request.tags || []
        }
      })

      // 创建邮件发送记录
      const emailRecords = await Promise.all(
        request.recipients.map(async (recipient) => {
          return prisma.emailSend.create({
            data: {
              batchId: batch.id,
              userId,
              recipientEmail: recipient.email,
              recipientName: recipient.name,
              subject: emailContent.subject,
              htmlContent: this.processEmailVariables(
                emailContent.htmlContent,
                recipient.variables || {}
              ),
              textContent: emailContent.textContent ? 
                this.processEmailVariables(emailContent.textContent, recipient.variables || {}) : 
                undefined,
              status: EmailSendStatus.PENDING,
              metadata: {
                variables: recipient.variables,
                trackingId: this.generateTrackingId()
              }
            }
          })
        })
      )

      // 如果是立即发送，开始发送流程
      if (!request.scheduleAt || request.scheduleAt <= new Date()) {
        await this.processBatchSending(batch.id)
      }

      // 更新用户发送统计
      await this.updateUserSendingStats(userId, request.recipients.length)

      logger.info('营销邮件发送任务创建成功', {
        userId,
        batchId: batch.id,
        recipientCount: request.recipients.length
      })

      return {
        batchId: batch.id,
        totalRecipients: request.recipients.length,
        estimatedDeliveryTime: request.scheduleAt || new Date()
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('发送营销邮件失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '发送邮件失败')
    }
  }

  /**
   * 获取邮件模板
   */
  static async getEmailTemplate(templateId: string, userId: string): Promise<any> {
    try {
      const template = await prisma.emailTemplate.findFirst({
        where: {
          id: templateId,
          userId,
          deletedAt: null
        }
      })

      if (!template) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '邮件模板不存在')
      }

      return template
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('获取邮件模板失败', {
        userId,
        templateId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取邮件模板失败')
    }
  }

  /**
   * 获取用户邮件模板列表
   */
  static async getUserEmailTemplates(
    userId: string,
    options: {
      page?: number
      limit?: number
      type?: EmailTemplateType
      search?: string
      tags?: string[]
      isActive?: boolean
    } = {}
  ): Promise<{
    templates: any[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    try {
      const {
        page = 1,
        limit = 20,
        type,
        search,
        tags,
        isActive
      } = options

      // 构建查询条件
      const where: any = {
        userId,
        deletedAt: null
      }

      if (type) where.type = type
      if (isActive !== undefined) where.isActive = isActive
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }
      if (tags && tags.length > 0) {
        where.tags = { hasSome: tags }
      }

      const skip = (page - 1) * limit

      const [templates, total] = await Promise.all([
        prisma.emailTemplate.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.emailTemplate.count({ where })
      ])

      const totalPages = Math.ceil(total / limit)

      return {
        templates,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      logger.error('获取用户邮件模板列表失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取邮件模板列表失败')
    }
  }

  /**
   * 获取邮件发送统计
   */
  static async getEmailStats(
    userId: string,
    options: {
      startDate?: Date
      endDate?: Date
      templateId?: string
      batchId?: string
    } = {}
  ): Promise<EmailMarketingStats> {
    try {
      const { startDate, endDate, templateId, batchId } = options

      // 构建查询条件
      const where: any = { userId }

      if (startDate || endDate) {
        where.createdAt = {}
        if (startDate) where.createdAt.gte = startDate
        if (endDate) where.createdAt.lte = endDate
      }

      if (templateId) {
        const batch = await prisma.emailBatch.findFirst({
          where: { templateId, userId }
        })
        if (batch) where.batchId = batch.id
      }

      if (batchId) where.batchId = batchId

      // 获取统计数据
      const [
        totalSent,
        totalDelivered,
        totalOpened,
        totalClicked,
        totalBounced,
        totalComplaints,
        totalUnsubscribed
      ] = await Promise.all([
        prisma.emailSend.count({
          where: { ...where, status: { in: [EmailSendStatus.SENT, EmailSendStatus.DELIVERED, EmailSendStatus.OPENED, EmailSendStatus.CLICKED] } }
        }),
        prisma.emailSend.count({
          where: { ...where, status: { in: [EmailSendStatus.DELIVERED, EmailSendStatus.OPENED, EmailSendStatus.CLICKED] } }
        }),
        prisma.emailSend.count({
          where: { ...where, status: { in: [EmailSendStatus.OPENED, EmailSendStatus.CLICKED] } }
        }),
        prisma.emailSend.count({
          where: { ...where, status: EmailSendStatus.CLICKED }
        }),
        prisma.emailSend.count({
          where: { ...where, status: EmailSendStatus.BOUNCED }
        }),
        prisma.emailSend.count({
          where: { ...where, status: EmailSendStatus.COMPLAINED }
        }),
        prisma.emailSend.count({
          where: { ...where, status: EmailSendStatus.UNSUBSCRIBED }
        })
      ])

      // 计算比率
      const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0
      const openRate = totalDelivered > 0 ? (totalOpened / totalDelivered) * 100 : 0
      const clickRate = totalOpened > 0 ? (totalClicked / totalOpened) * 100 : 0
      const bounceRate = totalSent > 0 ? (totalBounced / totalSent) * 100 : 0
      const complaintRate = totalSent > 0 ? (totalComplaints / totalSent) * 100 : 0
      const unsubscribeRate = totalSent > 0 ? (totalUnsubscribed / totalSent) * 100 : 0

      return {
        totalSent,
        totalDelivered,
        totalOpened,
        totalClicked,
        totalBounced,
        totalComplaints,
        totalUnsubscribed,
        deliveryRate: Math.round(deliveryRate * 100) / 100,
        openRate: Math.round(openRate * 100) / 100,
        clickRate: Math.round(clickRate * 100) / 100,
        bounceRate: Math.round(bounceRate * 100) / 100,
        complaintRate: Math.round(complaintRate * 100) / 100,
        unsubscribeRate: Math.round(unsubscribeRate * 100) / 100
      }
    } catch (error) {
      logger.error('获取邮件统计失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取邮件统计失败')
    }
  }

  /**
   * 处理邮件变量替换
   */
  private static processEmailVariables(content: string, variables: Record<string, any>): string {
    let processedContent = content

    // 替换变量 {{variable_name}}
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
      processedContent = processedContent.replace(regex, String(value))
    }

    // 添加默认变量
    const defaultVariables = {
      current_year: new Date().getFullYear(),
      current_date: new Date().toLocaleDateString('zh-CN'),
      unsubscribe_url: `${config.app.baseUrl}/unsubscribe?token={{unsubscribe_token}}`,
      company_name: config.app.name
    }

    for (const [key, value] of Object.entries(defaultVariables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
      processedContent = processedContent.replace(regex, String(value))
    }

    return processedContent
  }

  /**
   * 验证发送限制
   */
  private static async validateSendingLimits(userId: string, recipientCount: number): Promise<void> {
    // 检查日发送限制
    const today = new Date().toISOString().split('T')[0]
    const dailyKey = `email_send_limit:${userId}:${today}`
    const dailySent = await redis.get(dailyKey) || 0

    const dailyLimit = config.email.dailyLimit || 1000
    if (parseInt(dailySent) + recipientCount > dailyLimit) {
      throw new ApiError(
        ErrorCodes.TOO_MANY_REQUESTS,
        `超出日发送限制 ${dailyLimit} 封邮件`
      )
    }

    // 检查小时发送限制
    const hour = new Date().getHours()
    const hourlyKey = `email_send_limit:${userId}:${today}:${hour}`
    const hourlySent = await redis.get(hourlyKey) || 0

    const hourlyLimit = config.email.hourlyLimit || 100
    if (parseInt(hourlySent) + recipientCount > hourlyLimit) {
      throw new ApiError(
        ErrorCodes.TOO_MANY_REQUESTS,
        `超出小时发送限制 ${hourlyLimit} 封邮件`
      )
    }
  }

  /**
   * 更新用户发送统计
   */
  private static async updateUserSendingStats(userId: string, count: number): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0]
      const hour = new Date().getHours()

      // 更新日统计
      const dailyKey = `email_send_limit:${userId}:${today}`
      await redis.incrby(dailyKey, count)
      await redis.expire(dailyKey, 86400) // 24小时过期

      // 更新小时统计
      const hourlyKey = `email_send_limit:${userId}:${today}:${hour}`
      await redis.incrby(hourlyKey, count)
      await redis.expire(hourlyKey, 3600) // 1小时过期
    } catch (error) {
      logger.warn('更新用户发送统计失败', {
        userId,
        count,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 处理批次发送
   */
  private static async processBatchSending(batchId: string): Promise<void> {
    try {
      // 更新批次状态
      await prisma.emailBatch.update({
        where: { id: batchId },
        data: { status: 'SENDING' }
      })

      // 获取待发送的邮件
      const emails = await prisma.emailSend.findMany({
        where: {
          batchId,
          status: EmailSendStatus.PENDING
        },
        take: 100 // 批量处理，避免一次性处理过多
      })

      // 发送邮件
      for (const email of emails) {
        try {
          await emailService.sendEmail(
            email.recipientEmail,
            email.subject,
            email.htmlContent,
            email.textContent
          )

          // 更新发送状态
          await prisma.emailSend.update({
            where: { id: email.id },
            data: {
              status: EmailSendStatus.SENT,
              sentAt: new Date()
            }
          })
        } catch (error) {
          // 更新失败状态
          await prisma.emailSend.update({
            where: { id: email.id },
            data: {
              status: EmailSendStatus.FAILED,
              errorMessage: error instanceof Error ? error.message : String(error)
            }
          })
        }
      }

      // 检查是否还有待发送的邮件
      const remainingCount = await prisma.emailSend.count({
        where: {
          batchId,
          status: EmailSendStatus.PENDING
        }
      })

      if (remainingCount === 0) {
        // 更新批次状态为已完成
        await prisma.emailBatch.update({
          where: { id: batchId },
          data: { 
            status: 'COMPLETED',
            completedAt: new Date()
          }
        })
      }
    } catch (error) {
      logger.error('处理批次发送失败', {
        batchId,
        error: error instanceof Error ? error.message : String(error)
      })

      // 更新批次状态为失败
      await prisma.emailBatch.update({
        where: { id: batchId },
        data: { status: 'FAILED' }
      })
    }
  }

  /**
   * 生成跟踪ID
   */
  private static generateTrackingId(): string {
    return `track_${Date.now()}_${Math.random().toString(36).substring(2)}`
  }

  /**
   * 验证和清理模板数据
   */
  private static validateAndCleanTemplateData(data: CreateEmailTemplateRequest): CreateEmailTemplateRequest {
    return {
      name: DataSanitizer.sanitizeText(data.name),
      description: data.description ? DataSanitizer.sanitizeText(data.description) : undefined,
      type: data.type,
      subject: DataSanitizer.sanitizeText(data.subject),
      htmlContent: data.htmlContent, // HTML内容不进行过度清理，保持格式
      textContent: data.textContent,
      variables: data.variables?.map(v => DataSanitizer.sanitizeText(v)).filter(Boolean),
      previewText: data.previewText ? DataSanitizer.sanitizeText(data.previewText) : undefined,
      tags: data.tags?.map(tag => DataSanitizer.sanitizeText(tag)).filter(Boolean),
      isActive: data.isActive
    }
  }

  /**
   * 清除用户模板缓存
   */
  private static async clearUserTemplateCache(userId: string): Promise<void> {
    try {
      await redis.delPattern(`user_email_templates:${userId}:*`)
    } catch (error) {
      logger.warn('清除用户模板缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }
}
