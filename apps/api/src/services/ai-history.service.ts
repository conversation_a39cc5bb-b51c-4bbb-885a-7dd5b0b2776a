// AI生成历史管理服务
// 实现生成历史记录、版本管理、收藏功能和导出功能

import { prisma } from '../utils/database'
import { redis } from '../utils/redis'
import { ApiError, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { DataSanitizer } from '../utils/validation'
import ExcelJS from 'exceljs'
import PDFDocument from 'pdfkit'
import fs from 'fs/promises'
import path from 'path'

/**
 * 历史记录查询选项
 */
export interface HistoryQueryOptions {
  page?: number
  limit?: number
  type?: 'TEXT_GENERATION' | 'IMAGE_GENERATION' | 'ALL'
  search?: string
  startDate?: Date
  endDate?: Date
  sortBy?: 'createdAt' | 'updatedAt' | 'prompt'
  sortOrder?: 'asc' | 'desc'
  favoriteOnly?: boolean
}

/**
 * 历史记录项接口
 */
export interface HistoryItem {
  id: string
  type: 'TEXT_GENERATION' | 'IMAGE_GENERATION'
  prompt: string
  result: string
  metadata: any
  isFavorite: boolean
  tags: string[]
  version: number
  parentId?: string
  createdAt: Date
  updatedAt: Date
}

/**
 * 导出选项接口
 */
export interface ExportOptions {
  format: 'excel' | 'pdf' | 'json' | 'csv'
  items: string[]
  includeMetadata?: boolean
  includeImages?: boolean
}

/**
 * AI生成历史管理服务类
 */
export class AIHistoryService {
  /**
   * 获取用户生成历史
   */
  static async getUserHistory(
    userId: string,
    options: HistoryQueryOptions = {}
  ): Promise<{
    items: HistoryItem[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    stats: {
      totalItems: number
      textGenerations: number
      imageGenerations: number
      favoriteCount: number
    }
  }> {
    logger.debug('获取用户AI生成历史', { userId, options })

    try {
      const {
        page = 1,
        limit = 20,
        type,
        search,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        favoriteOnly = false
      } = options

      // 构建查询条件
      const where: any = { userId }

      if (type && type !== 'ALL') {
        where.type = type
      }

      if (search) {
        where.OR = [
          { prompt: { contains: search, mode: 'insensitive' } },
          { result: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (startDate || endDate) {
        where.createdAt = {}
        if (startDate) where.createdAt.gte = startDate
        if (endDate) where.createdAt.lte = endDate
      }

      if (favoriteOnly) {
        where.isFavorite = true
      }

      // 计算偏移量
      const skip = (page - 1) * limit

      // 查询历史记录和统计信息
      const [items, total, stats] = await Promise.all([
        prisma.aIGenerationHistory.findMany({
          where,
          select: {
            id: true,
            type: true,
            prompt: true,
            result: true,
            metadata: true,
            isFavorite: true,
            tags: true,
            version: true,
            parentId: true,
            createdAt: true,
            updatedAt: true
          },
          skip,
          take: limit,
          orderBy: {
            [sortBy]: sortOrder
          }
        }),
        prisma.aIGenerationHistory.count({ where }),
        this.getUserHistoryStats(userId)
      ])

      const totalPages = Math.ceil(total / limit)

      logger.debug('用户AI生成历史获取成功', {
        userId,
        itemCount: items.length,
        total
      })

      return {
        items: items as HistoryItem[],
        pagination: {
          page,
          limit,
          total,
          totalPages
        },
        stats
      }
    } catch (error) {
      logger.error('获取用户AI生成历史失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取生成历史失败')
    }
  }

  /**
   * 获取单个历史记录详情
   */
  static async getHistoryItem(
    itemId: string,
    userId: string
  ): Promise<HistoryItem> {
    logger.debug('获取历史记录详情', { itemId, userId })

    try {
      const item = await prisma.aIGenerationHistory.findFirst({
        where: {
          id: itemId,
          userId
        },
        select: {
          id: true,
          type: true,
          prompt: true,
          result: true,
          metadata: true,
          isFavorite: true,
          tags: true,
          version: true,
          parentId: true,
          createdAt: true,
          updatedAt: true
        }
      })

      if (!item) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '历史记录不存在')
      }

      return item as HistoryItem
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('获取历史记录详情失败', {
        itemId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取历史记录失败')
    }
  }

  /**
   * 更新历史记录
   */
  static async updateHistoryItem(
    itemId: string,
    userId: string,
    updates: {
      isFavorite?: boolean
      tags?: string[]
      customTitle?: string
      notes?: string
    }
  ): Promise<HistoryItem> {
    logger.info('更新历史记录', { itemId, userId, updates })

    try {
      // 验证记录存在且属于用户
      const existingItem = await prisma.aIGenerationHistory.findFirst({
        where: {
          id: itemId,
          userId
        }
      })

      if (!existingItem) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '历史记录不存在')
      }

      // 清理标签
      const cleanTags = updates.tags?.map(tag => DataSanitizer.sanitizeText(tag)).filter(Boolean) || []

      // 构建更新数据
      const updateData: any = {
        updatedAt: new Date()
      }

      if (updates.isFavorite !== undefined) {
        updateData.isFavorite = updates.isFavorite
      }

      if (updates.tags) {
        updateData.tags = cleanTags
      }

      // 更新元数据
      if (updates.customTitle || updates.notes) {
        const currentMetadata = existingItem.metadata as any || {}
        updateData.metadata = {
          ...currentMetadata,
          ...(updates.customTitle && { customTitle: DataSanitizer.sanitizeText(updates.customTitle) }),
          ...(updates.notes && { notes: DataSanitizer.sanitizeText(updates.notes) })
        }
      }

      // 执行更新
      const updatedItem = await prisma.aIGenerationHistory.update({
        where: { id: itemId },
        data: updateData,
        select: {
          id: true,
          type: true,
          prompt: true,
          result: true,
          metadata: true,
          isFavorite: true,
          tags: true,
          version: true,
          parentId: true,
          createdAt: true,
          updatedAt: true
        }
      })

      // 清除相关缓存
      await this.clearUserHistoryCache(userId)

      logger.info('历史记录更新成功', { itemId, userId })

      return updatedItem as HistoryItem
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('更新历史记录失败', {
        itemId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '更新历史记录失败')
    }
  }

  /**
   * 删除历史记录
   */
  static async deleteHistoryItem(
    itemId: string,
    userId: string
  ): Promise<void> {
    logger.info('删除历史记录', { itemId, userId })

    try {
      // 验证记录存在且属于用户
      const existingItem = await prisma.aIGenerationHistory.findFirst({
        where: {
          id: itemId,
          userId
        }
      })

      if (!existingItem) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '历史记录不存在')
      }

      // 软删除（标记为已删除）
      await prisma.aIGenerationHistory.update({
        where: { id: itemId },
        data: {
          deletedAt: new Date(),
          updatedAt: new Date()
        }
      })

      // 清除相关缓存
      await this.clearUserHistoryCache(userId)

      logger.info('历史记录删除成功', { itemId, userId })
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('删除历史记录失败', {
        itemId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '删除历史记录失败')
    }
  }

  /**
   * 批量删除历史记录
   */
  static async batchDeleteHistoryItems(
    itemIds: string[],
    userId: string
  ): Promise<{ deletedCount: number }> {
    logger.info('批量删除历史记录', { itemIds, userId })

    try {
      // 验证所有记录都属于用户
      const existingItems = await prisma.aIGenerationHistory.findMany({
        where: {
          id: { in: itemIds },
          userId,
          deletedAt: null
        },
        select: { id: true }
      })

      const validIds = existingItems.map(item => item.id)

      if (validIds.length === 0) {
        return { deletedCount: 0 }
      }

      // 批量软删除
      await prisma.aIGenerationHistory.updateMany({
        where: {
          id: { in: validIds }
        },
        data: {
          deletedAt: new Date(),
          updatedAt: new Date()
        }
      })

      // 清除相关缓存
      await this.clearUserHistoryCache(userId)

      logger.info('批量删除历史记录成功', {
        requestedCount: itemIds.length,
        deletedCount: validIds.length,
        userId
      })

      return { deletedCount: validIds.length }
    } catch (error) {
      logger.error('批量删除历史记录失败', {
        itemIds,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '批量删除历史记录失败')
    }
  }

  /**
   * 创建历史记录版本
   */
  static async createVersion(
    parentId: string,
    userId: string,
    newPrompt: string,
    newResult: string,
    metadata: any = {}
  ): Promise<HistoryItem> {
    logger.info('创建历史记录版本', { parentId, userId })

    try {
      // 验证父记录存在
      const parentItem = await prisma.aIGenerationHistory.findFirst({
        where: {
          id: parentId,
          userId,
          deletedAt: null
        }
      })

      if (!parentItem) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '父记录不存在')
      }

      // 获取当前最大版本号
      const maxVersion = await prisma.aIGenerationHistory.findFirst({
        where: {
          OR: [
            { id: parentId },
            { parentId }
          ],
          userId
        },
        select: { version: true },
        orderBy: { version: 'desc' }
      })

      const newVersion = (maxVersion?.version || 0) + 1

      // 创建新版本
      const newItem = await prisma.aIGenerationHistory.create({
        data: {
          userId,
          type: parentItem.type,
          prompt: newPrompt,
          result: newResult,
          metadata: {
            ...parentItem.metadata,
            ...metadata,
            isVersion: true,
            originalId: parentItem.parentId || parentId
          },
          parentId,
          version: newVersion,
          isFavorite: false,
          tags: []
        },
        select: {
          id: true,
          type: true,
          prompt: true,
          result: true,
          metadata: true,
          isFavorite: true,
          tags: true,
          version: true,
          parentId: true,
          createdAt: true,
          updatedAt: true
        }
      })

      // 清除相关缓存
      await this.clearUserHistoryCache(userId)

      logger.info('历史记录版本创建成功', {
        parentId,
        newItemId: newItem.id,
        version: newVersion,
        userId
      })

      return newItem as HistoryItem
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('创建历史记录版本失败', {
        parentId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '创建版本失败')
    }
  }

  /**
   * 获取记录的所有版本
   */
  static async getItemVersions(
    itemId: string,
    userId: string
  ): Promise<HistoryItem[]> {
    logger.debug('获取记录版本', { itemId, userId })

    try {
      // 查找所有相关版本
      const versions = await prisma.aIGenerationHistory.findMany({
        where: {
          OR: [
            { id: itemId, userId },
            { parentId: itemId, userId }
          ],
          deletedAt: null
        },
        select: {
          id: true,
          type: true,
          prompt: true,
          result: true,
          metadata: true,
          isFavorite: true,
          tags: true,
          version: true,
          parentId: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { version: 'asc' }
      })

      return versions as HistoryItem[]
    } catch (error) {
      logger.error('获取记录版本失败', {
        itemId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取版本失败')
    }
  }

  /**
   * 导出历史记录
   */
  static async exportHistory(
    userId: string,
    options: ExportOptions
  ): Promise<string> {
    logger.info('导出历史记录', { userId, options })

    try {
      // 获取要导出的记录
      const items = await prisma.aIGenerationHistory.findMany({
        where: {
          id: { in: options.items },
          userId,
          deletedAt: null
        },
        orderBy: { createdAt: 'desc' }
      })

      if (items.length === 0) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '没有找到要导出的记录')
      }

      // 根据格式生成文件
      let filePath: string
      
      switch (options.format) {
        case 'excel':
          filePath = await this.exportToExcel(items, userId, options)
          break
        case 'pdf':
          filePath = await this.exportToPDF(items, userId, options)
          break
        case 'json':
          filePath = await this.exportToJSON(items, userId, options)
          break
        case 'csv':
          filePath = await this.exportToCSV(items, userId, options)
          break
        default:
          throw new ApiError(ErrorCodes.VALIDATION_ERROR, '不支持的导出格式')
      }

      logger.info('历史记录导出成功', {
        userId,
        format: options.format,
        itemCount: items.length,
        filePath
      })

      return filePath
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      logger.error('导出历史记录失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '导出失败')
    }
  }

  /**
   * 获取用户历史统计
   */
  private static async getUserHistoryStats(userId: string): Promise<{
    totalItems: number
    textGenerations: number
    imageGenerations: number
    favoriteCount: number
  }> {
    try {
      const [total, textCount, imageCount, favoriteCount] = await Promise.all([
        prisma.aIGenerationHistory.count({
          where: { userId, deletedAt: null }
        }),
        prisma.aIGenerationHistory.count({
          where: { userId, type: 'TEXT_GENERATION', deletedAt: null }
        }),
        prisma.aIGenerationHistory.count({
          where: { userId, type: 'IMAGE_GENERATION', deletedAt: null }
        }),
        prisma.aIGenerationHistory.count({
          where: { userId, isFavorite: true, deletedAt: null }
        })
      ])

      return {
        totalItems: total,
        textGenerations: textCount,
        imageGenerations: imageCount,
        favoriteCount
      }
    } catch (error) {
      logger.warn('获取用户历史统计失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      
      return {
        totalItems: 0,
        textGenerations: 0,
        imageGenerations: 0,
        favoriteCount: 0
      }
    }
  }

  /**
   * 清除用户历史缓存
   */
  private static async clearUserHistoryCache(userId: string): Promise<void> {
    try {
      await redis.delPattern(`ai_history:${userId}:*`)
    } catch (error) {
      logger.warn('清除用户历史缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 导出为Excel格式
   */
  private static async exportToExcel(
    items: any[],
    userId: string,
    options: ExportOptions
  ): Promise<string> {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('AI生成历史')

    // 设置列标题
    const columns = [
      { header: 'ID', key: 'id', width: 20 },
      { header: '类型', key: 'type', width: 15 },
      { header: '提示词', key: 'prompt', width: 50 },
      { header: '生成结果', key: 'result', width: 50 },
      { header: '是否收藏', key: 'isFavorite', width: 10 },
      { header: '标签', key: 'tags', width: 30 },
      { header: '创建时间', key: 'createdAt', width: 20 }
    ]

    if (options.includeMetadata) {
      columns.push({ header: '元数据', key: 'metadata', width: 30 })
    }

    worksheet.columns = columns

    // 添加数据
    items.forEach(item => {
      const row: any = {
        id: item.id,
        type: item.type === 'TEXT_GENERATION' ? '文本生成' : '图像生成',
        prompt: item.prompt,
        result: item.type === 'TEXT_GENERATION' ? item.result : '图像文件',
        isFavorite: item.isFavorite ? '是' : '否',
        tags: Array.isArray(item.tags) ? item.tags.join(', ') : '',
        createdAt: new Date(item.createdAt).toLocaleString('zh-CN')
      }

      if (options.includeMetadata) {
        row.metadata = JSON.stringify(item.metadata, null, 2)
      }

      worksheet.addRow(row)
    })

    // 保存文件
    const fileName = `ai_history_${userId}_${Date.now()}.xlsx`
    const filePath = path.join(process.cwd(), 'temp', fileName)
    
    await fs.mkdir(path.dirname(filePath), { recursive: true })
    await workbook.xlsx.writeFile(filePath)

    return filePath
  }

  /**
   * 导出为PDF格式
   */
  private static async exportToPDF(
    items: any[],
    userId: string,
    options: ExportOptions
  ): Promise<string> {
    const fileName = `ai_history_${userId}_${Date.now()}.pdf`
    const filePath = path.join(process.cwd(), 'temp', fileName)
    
    await fs.mkdir(path.dirname(filePath), { recursive: true })

    const doc = new PDFDocument()
    doc.pipe(fs.createWriteStream(filePath))

    // 添加标题
    doc.fontSize(20).text('AI生成历史', { align: 'center' })
    doc.moveDown()

    // 添加每个记录
    items.forEach((item, index) => {
      doc.fontSize(14).text(`${index + 1}. ${item.type === 'TEXT_GENERATION' ? '文本生成' : '图像生成'}`)
      doc.fontSize(10).text(`ID: ${item.id}`)
      doc.text(`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`)
      doc.text(`提示词: ${item.prompt}`)
      
      if (item.type === 'TEXT_GENERATION') {
        doc.text(`生成结果: ${item.result.substring(0, 200)}${item.result.length > 200 ? '...' : ''}`)
      }
      
      doc.moveDown()
    })

    doc.end()

    return filePath
  }

  /**
   * 导出为JSON格式
   */
  private static async exportToJSON(
    items: any[],
    userId: string,
    options: ExportOptions
  ): Promise<string> {
    const fileName = `ai_history_${userId}_${Date.now()}.json`
    const filePath = path.join(process.cwd(), 'temp', fileName)
    
    await fs.mkdir(path.dirname(filePath), { recursive: true })

    const exportData = {
      exportedAt: new Date().toISOString(),
      userId,
      itemCount: items.length,
      items: items.map(item => ({
        id: item.id,
        type: item.type,
        prompt: item.prompt,
        result: item.result,
        isFavorite: item.isFavorite,
        tags: item.tags,
        createdAt: item.createdAt,
        ...(options.includeMetadata && { metadata: item.metadata })
      }))
    }

    await fs.writeFile(filePath, JSON.stringify(exportData, null, 2), 'utf-8')

    return filePath
  }

  /**
   * 导出为CSV格式
   */
  private static async exportToCSV(
    items: any[],
    userId: string,
    options: ExportOptions
  ): Promise<string> {
    const fileName = `ai_history_${userId}_${Date.now()}.csv`
    const filePath = path.join(process.cwd(), 'temp', fileName)
    
    await fs.mkdir(path.dirname(filePath), { recursive: true })

    // 构建CSV内容
    const headers = ['ID', '类型', '提示词', '生成结果', '是否收藏', '标签', '创建时间']
    if (options.includeMetadata) {
      headers.push('元数据')
    }

    const csvLines = [headers.join(',')]

    items.forEach(item => {
      const row = [
        `"${item.id}"`,
        `"${item.type === 'TEXT_GENERATION' ? '文本生成' : '图像生成'}"`,
        `"${item.prompt.replace(/"/g, '""')}"`,
        `"${item.type === 'TEXT_GENERATION' ? item.result.replace(/"/g, '""') : '图像文件'}"`,
        `"${item.isFavorite ? '是' : '否'}"`,
        `"${Array.isArray(item.tags) ? item.tags.join(', ') : ''}"`,
        `"${new Date(item.createdAt).toLocaleString('zh-CN')}"`
      ]

      if (options.includeMetadata) {
        row.push(`"${JSON.stringify(item.metadata).replace(/"/g, '""')}"`)
      }

      csvLines.push(row.join(','))
    })

    await fs.writeFile(filePath, csvLines.join('\n'), 'utf-8')

    return filePath
  }
}
