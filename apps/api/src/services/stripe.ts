import Stripe from 'stripe'
import { config } from '../config'
import { paymentLogger } from '../utils/logger'
import { prisma } from '../utils/database'

/**
 * Stripe支付服务类
 * 处理订阅管理、支付处理、账单等功能
 */
export class StripeService {
  private stripe: Stripe

  constructor() {
    this.stripe = new Stripe(config.stripe.secretKey, {
      apiVersion: '2023-10-16',
    })
  }

  /**
   * 创建客户
   * @param userEmail 用户邮箱
   * @param userName 用户姓名
   */
  async createCustomer(userEmail: string, userName?: string): Promise<Stripe.Customer> {
    try {
      paymentLogger.info('创建Stripe客户', { userEmail })

      const customer = await this.stripe.customers.create({
        email: userEmail,
        name: userName,
        metadata: {
          source: 'ai-digital-marketing'
        }
      })

      paymentLogger.info('Stripe客户创建成功', { customerId: customer.id, userEmail })
      return customer

    } catch (error) {
      paymentLogger.error('创建Stripe客户失败', { error, userEmail })
      throw new Error(`创建客户失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 创建订阅
   * @param customerId Stripe客户ID
   * @param priceId 价格ID
   * @param options 订阅选项
   */
  async createSubscription(
    customerId: string,
    priceId: string,
    options: {
      trialPeriodDays?: number
      couponId?: string
      metadata?: Record<string, string>
    } = {}
  ): Promise<Stripe.Subscription> {
    try {
      paymentLogger.info('创建订阅', { customerId, priceId, options })

      const subscriptionData: Stripe.SubscriptionCreateParams = {
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        metadata: options.metadata || {}
      }

      if (options.trialPeriodDays) {
        subscriptionData.trial_period_days = options.trialPeriodDays
      }

      if (options.couponId) {
        subscriptionData.coupon = options.couponId
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionData)

      paymentLogger.info('订阅创建成功', { 
        subscriptionId: subscription.id, 
        customerId, 
        status: subscription.status 
      })

      return subscription

    } catch (error) {
      paymentLogger.error('创建订阅失败', { error, customerId, priceId })
      throw new Error(`创建订阅失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 创建支付会话
   * @param customerId Stripe客户ID
   * @param priceId 价格ID
   * @param options 会话选项
   */
  async createCheckoutSession(
    customerId: string,
    priceId: string,
    options: {
      successUrl: string
      cancelUrl: string
      trialPeriodDays?: number
      couponId?: string
      metadata?: Record<string, string>
    }
  ): Promise<Stripe.Checkout.Session> {
    try {
      paymentLogger.info('创建支付会话', { customerId, priceId, options })

      const sessionData: Stripe.Checkout.SessionCreateParams = {
        customer: customerId,
        payment_method_types: ['card'],
        mode: 'subscription',
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        success_url: options.successUrl,
        cancel_url: options.cancelUrl,
        metadata: options.metadata || {},
        subscription_data: {
          metadata: options.metadata || {}
        }
      }

      if (options.trialPeriodDays) {
        sessionData.subscription_data!.trial_period_days = options.trialPeriodDays
      }

      if (options.couponId) {
        sessionData.discounts = [{ coupon: options.couponId }]
      }

      const session = await this.stripe.checkout.sessions.create(sessionData)

      paymentLogger.info('支付会话创建成功', { 
        sessionId: session.id, 
        customerId,
        url: session.url 
      })

      return session

    } catch (error) {
      paymentLogger.error('创建支付会话失败', { error, customerId, priceId })
      throw new Error(`创建支付会话失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 获取订阅信息
   * @param subscriptionId 订阅ID
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['default_payment_method', 'latest_invoice']
      })

      return subscription

    } catch (error) {
      paymentLogger.error('获取订阅信息失败', { error, subscriptionId })
      throw new Error(`获取订阅信息失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 取消订阅
   * @param subscriptionId 订阅ID
   * @param immediately 是否立即取消
   */
  async cancelSubscription(subscriptionId: string, immediately: boolean = false): Promise<Stripe.Subscription> {
    try {
      paymentLogger.info('取消订阅', { subscriptionId, immediately })

      let subscription: Stripe.Subscription

      if (immediately) {
        subscription = await this.stripe.subscriptions.cancel(subscriptionId)
      } else {
        subscription = await this.stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true
        })
      }

      paymentLogger.info('订阅取消成功', { 
        subscriptionId, 
        status: subscription.status,
        cancelAtPeriodEnd: subscription.cancel_at_period_end 
      })

      return subscription

    } catch (error) {
      paymentLogger.error('取消订阅失败', { error, subscriptionId })
      throw new Error(`取消订阅失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 更新订阅
   * @param subscriptionId 订阅ID
   * @param priceId 新的价格ID
   */
  async updateSubscription(subscriptionId: string, priceId: string): Promise<Stripe.Subscription> {
    try {
      paymentLogger.info('更新订阅', { subscriptionId, priceId })

      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId)
      
      const updatedSubscription = await this.stripe.subscriptions.update(subscriptionId, {
        items: [
          {
            id: subscription.items.data[0].id,
            price: priceId,
          },
        ],
        proration_behavior: 'create_prorations',
      })

      paymentLogger.info('订阅更新成功', { 
        subscriptionId, 
        newPriceId: priceId,
        status: updatedSubscription.status 
      })

      return updatedSubscription

    } catch (error) {
      paymentLogger.error('更新订阅失败', { error, subscriptionId, priceId })
      throw new Error(`更新订阅失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 创建客户门户会话
   * @param customerId Stripe客户ID
   * @param returnUrl 返回URL
   */
  async createPortalSession(customerId: string, returnUrl: string): Promise<Stripe.BillingPortal.Session> {
    try {
      paymentLogger.info('创建客户门户会话', { customerId, returnUrl })

      const session = await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      })

      paymentLogger.info('客户门户会话创建成功', { 
        sessionId: session.id, 
        customerId,
        url: session.url 
      })

      return session

    } catch (error) {
      paymentLogger.error('创建客户门户会话失败', { error, customerId })
      throw new Error(`创建客户门户会话失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 处理Webhook事件
   * @param payload 请求体
   * @param signature Stripe签名
   */
  async handleWebhook(payload: string | Buffer, signature: string): Promise<void> {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        config.stripe.webhookSecret
      )

      paymentLogger.info('处理Stripe Webhook事件', { 
        eventType: event.type, 
        eventId: event.id 
      })

      switch (event.type) {
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription)
          break

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
          break

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
          break

        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice)
          break

        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice)
          break

        default:
          paymentLogger.info('未处理的Webhook事件类型', { eventType: event.type })
      }

    } catch (error) {
      paymentLogger.error('处理Webhook事件失败', { error })
      throw error
    }
  }

  /**
   * 处理订阅创建事件
   */
  private async handleSubscriptionCreated(subscription: Stripe.Subscription) {
    try {
      // TODO: 更新数据库中的订阅状态
      paymentLogger.info('处理订阅创建事件', { 
        subscriptionId: subscription.id,
        customerId: subscription.customer,
        status: subscription.status 
      })

    } catch (error) {
      paymentLogger.error('处理订阅创建事件失败', { error, subscriptionId: subscription.id })
    }
  }

  /**
   * 处理订阅更新事件
   */
  private async handleSubscriptionUpdated(subscription: Stripe.Subscription) {
    try {
      // TODO: 更新数据库中的订阅状态
      paymentLogger.info('处理订阅更新事件', { 
        subscriptionId: subscription.id,
        customerId: subscription.customer,
        status: subscription.status 
      })

    } catch (error) {
      paymentLogger.error('处理订阅更新事件失败', { error, subscriptionId: subscription.id })
    }
  }

  /**
   * 处理订阅删除事件
   */
  private async handleSubscriptionDeleted(subscription: Stripe.Subscription) {
    try {
      // TODO: 更新数据库中的订阅状态
      paymentLogger.info('处理订阅删除事件', { 
        subscriptionId: subscription.id,
        customerId: subscription.customer 
      })

    } catch (error) {
      paymentLogger.error('处理订阅删除事件失败', { error, subscriptionId: subscription.id })
    }
  }

  /**
   * 处理支付成功事件
   */
  private async handlePaymentSucceeded(invoice: Stripe.Invoice) {
    try {
      // TODO: 更新数据库中的支付记录
      paymentLogger.info('处理支付成功事件', { 
        invoiceId: invoice.id,
        subscriptionId: invoice.subscription,
        amount: invoice.amount_paid 
      })

    } catch (error) {
      paymentLogger.error('处理支付成功事件失败', { error, invoiceId: invoice.id })
    }
  }

  /**
   * 处理支付失败事件
   */
  private async handlePaymentFailed(invoice: Stripe.Invoice) {
    try {
      // TODO: 更新数据库中的支付记录，发送通知
      paymentLogger.info('处理支付失败事件', { 
        invoiceId: invoice.id,
        subscriptionId: invoice.subscription,
        amount: invoice.amount_due 
      })

    } catch (error) {
      paymentLogger.error('处理支付失败事件失败', { error, invoiceId: invoice.id })
    }
  }
}

// 创建单例实例
export const stripeService = new StripeService()
