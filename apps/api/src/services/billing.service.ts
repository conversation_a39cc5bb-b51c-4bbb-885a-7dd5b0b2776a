// 账单管理服务
// 处理发票生成、账单历史、付款提醒、退款等功能

import { prisma } from '../utils/database'
import { logger } from '../utils/logger'
import { ApiError, ErrorCodes } from '../utils/response'
import { PaymentService } from './payment.service'
import { emailService } from './email.service'
import PDFDocument from 'pdfkit'
import fs from 'fs/promises'
import path from 'path'

/**
 * 账单状态
 */
export enum BillStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELED = 'CANCELED',
  REFUNDED = 'REFUNDED'
}

/**
 * 账单项目
 */
export interface BillItem {
  description: string
  quantity: number
  unitPrice: number
  amount: number
  taxRate?: number
  taxAmount?: number
}

/**
 * 账单信息
 */
export interface BillInfo {
  id: string
  userId: string
  billNumber: string
  status: BillStatus
  issueDate: Date
  dueDate: Date
  items: BillItem[]
  subtotal: number
  taxTotal: number
  total: number
  currency: string
  notes?: string
  metadata?: any
}

/**
 * 创建账单请求
 */
export interface CreateBillRequest {
  userId: string
  items: BillItem[]
  dueDate: Date
  notes?: string
  metadata?: any
}

/**
 * 账单管理服务类
 */
export class BillingService {
  /**
   * 创建账单
   */
  static async createBill(request: CreateBillRequest): Promise<BillInfo> {
    try {
      const { userId, items, dueDate, notes, metadata } = request

      // 验证用户存在
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true }
      })

      if (!user) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '用户不存在')
      }

      // 计算金额
      const subtotal = items.reduce((sum, item) => sum + item.amount, 0)
      const taxTotal = items.reduce((sum, item) => sum + (item.taxAmount || 0), 0)
      const total = subtotal + taxTotal

      // 生成账单号
      const billNumber = await this.generateBillNumber()

      // 创建账单记录
      const bill = await prisma.bill.create({
        data: {
          userId,
          billNumber,
          status: BillStatus.DRAFT,
          issueDate: new Date(),
          dueDate,
          items: items as any,
          subtotal,
          taxTotal,
          total,
          currency: 'CNY',
          notes,
          metadata: metadata || {}
        }
      })

      logger.info('创建账单成功', {
        userId,
        billId: bill.id,
        billNumber,
        total
      })

      return this.formatBillInfo(bill)
    } catch (error) {
      logger.error('创建账单失败', {
        userId: request.userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '创建账单失败')
    }
  }

  /**
   * 获取用户账单列表
   */
  static async getUserBills(
    userId: string,
    options: {
      page?: number
      limit?: number
      status?: BillStatus
      startDate?: Date
      endDate?: Date
    } = {}
  ): Promise<{
    bills: BillInfo[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        startDate,
        endDate
      } = options

      const where: any = { userId }

      if (status) {
        where.status = status
      }

      if (startDate || endDate) {
        where.issueDate = {}
        if (startDate) where.issueDate.gte = startDate
        if (endDate) where.issueDate.lte = endDate
      }

      const skip = (page - 1) * limit

      const [bills, total] = await Promise.all([
        prisma.bill.findMany({
          where,
          skip,
          take: limit,
          orderBy: { issueDate: 'desc' }
        }),
        prisma.bill.count({ where })
      ])

      const totalPages = Math.ceil(total / limit)

      return {
        bills: bills.map(bill => this.formatBillInfo(bill)),
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      logger.error('获取用户账单列表失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '获取账单列表失败')
    }
  }

  /**
   * 获取账单详情
   */
  static async getBillById(billId: string, userId: string): Promise<BillInfo> {
    try {
      const bill = await prisma.bill.findFirst({
        where: {
          id: billId,
          userId
        }
      })

      if (!bill) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '账单不存在')
      }

      return this.formatBillInfo(bill)
    } catch (error) {
      logger.error('获取账单详情失败', {
        billId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '获取账单详情失败')
    }
  }

  /**
   * 发送账单
   */
  static async sendBill(billId: string, userId: string): Promise<void> {
    try {
      const bill = await prisma.bill.findFirst({
        where: {
          id: billId,
          userId
        }
      })

      if (!bill) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '账单不存在')
      }

      if (bill.status !== BillStatus.DRAFT) {
        throw new ApiError(ErrorCodes.BAD_REQUEST, '只能发送草稿状态的账单')
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true }
      })

      if (!user) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '用户不存在')
      }

      // 生成PDF账单
      const pdfPath = await this.generateBillPDF(bill, user)

      // 发送邮件
      await emailService.sendEmail(
        user.email,
        `账单 ${bill.billNumber}`,
        this.getBillEmailTemplate(bill),
        [{
          filename: `bill_${bill.billNumber}.pdf`,
          path: pdfPath
        }]
      )

      // 更新账单状态
      await prisma.bill.update({
        where: { id: billId },
        data: {
          status: BillStatus.SENT,
          sentAt: new Date()
        }
      })

      // 清理临时文件
      await fs.unlink(pdfPath)

      logger.info('发送账单成功', {
        billId,
        userId,
        billNumber: bill.billNumber
      })
    } catch (error) {
      logger.error('发送账单失败', {
        billId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '发送账单失败')
    }
  }

  /**
   * 标记账单为已支付
   */
  static async markBillAsPaid(
    billId: string,
    paymentIntentId?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        status: BillStatus.PAID,
        paidAt: new Date()
      }

      if (paymentIntentId) {
        updateData.metadata = {
          paymentIntentId
        }
      }

      await prisma.bill.update({
        where: { id: billId },
        data: updateData
      })

      logger.info('标记账单为已支付', {
        billId,
        paymentIntentId
      })
    } catch (error) {
      logger.error('标记账单为已支付失败', {
        billId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw new ApiError(ErrorCodes.INTERNAL_ERROR, '更新账单状态失败')
    }
  }

  /**
   * 处理退款
   */
  static async processBillRefund(
    billId: string,
    userId: string,
    amount?: number,
    reason?: string
  ): Promise<any> {
    try {
      const bill = await prisma.bill.findFirst({
        where: {
          id: billId,
          userId
        }
      })

      if (!bill) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '账单不存在')
      }

      if (bill.status !== BillStatus.PAID) {
        throw new ApiError(ErrorCodes.BAD_REQUEST, '只能退款已支付的账单')
      }

      const paymentIntentId = bill.metadata?.paymentIntentId
      if (!paymentIntentId) {
        throw new ApiError(ErrorCodes.BAD_REQUEST, '账单没有关联的支付记录')
      }

      // 创建Stripe退款
      const refund = await PaymentService.createRefund(
        paymentIntentId,
        amount,
        reason
      )

      // 更新账单状态
      await prisma.bill.update({
        where: { id: billId },
        data: {
          status: BillStatus.REFUNDED,
          refundedAt: new Date(),
          metadata: {
            ...bill.metadata,
            refundId: refund.id,
            refundAmount: refund.amount / 100,
            refundReason: reason
          }
        }
      })

      logger.info('处理账单退款成功', {
        billId,
        userId,
        refundId: refund.id,
        refundAmount: refund.amount / 100
      })

      return {
        refund,
        bill: await this.getBillById(billId, userId)
      }
    } catch (error) {
      logger.error('处理账单退款失败', {
        billId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '处理退款失败')
    }
  }

  /**
   * 发送付款提醒
   */
  static async sendPaymentReminder(billId: string): Promise<void> {
    try {
      const bill = await prisma.bill.findUnique({
        where: { id: billId },
        include: {
          user: {
            include: { profile: true }
          }
        }
      })

      if (!bill) {
        throw new ApiError(ErrorCodes.NOT_FOUND, '账单不存在')
      }

      if (bill.status === BillStatus.PAID) {
        throw new ApiError(ErrorCodes.BAD_REQUEST, '账单已支付，无需提醒')
      }

      // 发送提醒邮件
      await emailService.sendEmail(
        bill.user.email,
        `付款提醒 - 账单 ${bill.billNumber}`,
        this.getPaymentReminderEmailTemplate(bill)
      )

      // 记录提醒发送
      await prisma.bill.update({
        where: { id: billId },
        data: {
          metadata: {
            ...bill.metadata,
            lastReminderSent: new Date().toISOString()
          }
        }
      })

      logger.info('发送付款提醒成功', {
        billId,
        billNumber: bill.billNumber,
        userEmail: bill.user.email
      })
    } catch (error) {
      logger.error('发送付款提醒失败', {
        billId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error instanceof ApiError ? error : new ApiError(ErrorCodes.INTERNAL_ERROR, '发送付款提醒失败')
    }
  }

  /**
   * 检查逾期账单
   */
  static async checkOverdueBills(): Promise<void> {
    try {
      const now = new Date()
      
      // 查找逾期账单
      const overdueBills = await prisma.bill.findMany({
        where: {
          status: BillStatus.SENT,
          dueDate: {
            lt: now
          }
        }
      })

      // 更新状态为逾期
      if (overdueBills.length > 0) {
        await prisma.bill.updateMany({
          where: {
            id: {
              in: overdueBills.map(bill => bill.id)
            }
          },
          data: {
            status: BillStatus.OVERDUE
          }
        })

        logger.info('更新逾期账单状态', {
          count: overdueBills.length
        })
      }
    } catch (error) {
      logger.error('检查逾期账单失败', {
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 生成账单号
   */
  private static async generateBillNumber(): Promise<string> {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    
    // 获取当月账单数量
    const startOfMonth = new Date(year, now.getMonth(), 1)
    const endOfMonth = new Date(year, now.getMonth() + 1, 0)
    
    const count = await prisma.bill.count({
      where: {
        issueDate: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      }
    })

    const sequence = String(count + 1).padStart(4, '0')
    return `BILL-${year}${month}-${sequence}`
  }

  /**
   * 格式化账单信息
   */
  private static formatBillInfo(bill: any): BillInfo {
    return {
      id: bill.id,
      userId: bill.userId,
      billNumber: bill.billNumber,
      status: bill.status,
      issueDate: bill.issueDate,
      dueDate: bill.dueDate,
      items: bill.items,
      subtotal: bill.subtotal,
      taxTotal: bill.taxTotal,
      total: bill.total,
      currency: bill.currency,
      notes: bill.notes,
      metadata: bill.metadata
    }
  }

  /**
   * 生成账单PDF
   */
  private static async generateBillPDF(bill: any, user: any): Promise<string> {
    const doc = new PDFDocument()
    const fileName = `bill_${bill.billNumber}_${Date.now()}.pdf`
    const filePath = path.join(process.cwd(), 'temp', fileName)

    // 确保目录存在
    await fs.mkdir(path.dirname(filePath), { recursive: true })

    const stream = fs.createWriteStream(filePath)
    doc.pipe(stream)

    // 添加标题
    doc.fontSize(20).text('账单', { align: 'center' })
    doc.moveDown()

    // 添加账单信息
    doc.fontSize(12)
      .text(`账单号: ${bill.billNumber}`)
      .text(`开票日期: ${bill.issueDate.toLocaleDateString('zh-CN')}`)
      .text(`到期日期: ${bill.dueDate.toLocaleDateString('zh-CN')}`)
      .text(`客户: ${user.firstName} ${user.lastName}`)
      .text(`邮箱: ${user.email}`)

    doc.moveDown()

    // 添加账单项目
    doc.text('账单明细:')
    doc.moveDown(0.5)

    for (const item of bill.items) {
      doc.text(`${item.description} x ${item.quantity} = ¥${item.amount.toFixed(2)}`)
    }

    doc.moveDown()

    // 添加总计
    doc.text(`小计: ¥${bill.subtotal.toFixed(2)}`)
    doc.text(`税费: ¥${bill.taxTotal.toFixed(2)}`)
    doc.fontSize(14).text(`总计: ¥${bill.total.toFixed(2)}`, { align: 'right' })

    if (bill.notes) {
      doc.moveDown()
      doc.fontSize(12).text(`备注: ${bill.notes}`)
    }

    doc.end()

    return new Promise((resolve, reject) => {
      stream.on('finish', () => resolve(filePath))
      stream.on('error', reject)
    })
  }

  /**
   * 获取账单邮件模板
   */
  private static getBillEmailTemplate(bill: any): string {
    return `
      <h2>您的账单已生成</h2>
      <p>账单号: ${bill.billNumber}</p>
      <p>金额: ¥${bill.total.toFixed(2)}</p>
      <p>到期日期: ${bill.dueDate.toLocaleDateString('zh-CN')}</p>
      <p>请查看附件中的详细账单，并在到期日前完成付款。</p>
      <p>如有任何问题，请联系我们的客服团队。</p>
    `
  }

  /**
   * 获取付款提醒邮件模板
   */
  private static getPaymentReminderEmailTemplate(bill: any): string {
    const isOverdue = new Date() > bill.dueDate
    
    return `
      <h2>${isOverdue ? '账单逾期提醒' : '付款提醒'}</h2>
      <p>账单号: ${bill.billNumber}</p>
      <p>金额: ¥${bill.total.toFixed(2)}</p>
      <p>到期日期: ${bill.dueDate.toLocaleDateString('zh-CN')}</p>
      ${isOverdue ? 
        '<p style="color: red;">此账单已逾期，请尽快完成付款以避免服务中断。</p>' :
        '<p>此账单即将到期，请及时完成付款。</p>'
      }
      <p>如已完成付款，请忽略此邮件。</p>
    `
  }
}
