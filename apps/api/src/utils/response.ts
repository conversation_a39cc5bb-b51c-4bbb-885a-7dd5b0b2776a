// API响应格式化工具
// 统一API响应格式，提供标准化的成功和错误响应

import { FastifyReply } from 'fastify'

/**
 * 标准API响应接口
 */
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    pagination?: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    timestamp: string
    requestId?: string
  }
}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
}

/**
 * 响应工具类
 */
export class ResponseHelper {
  /**
   * 发送成功响应
   */
  static success<T>(
    reply: FastifyReply,
    data?: T,
    statusCode: number = 200,
    meta?: any
  ): FastifyReply {
    const response: ApiResponse<T> = {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        requestId: reply.request.id,
        ...meta,
      },
    }

    return reply.code(statusCode).send(response)
  }

  /**
   * 发送分页数据响应
   */
  static successWithPagination<T>(
    reply: FastifyReply,
    data: T[],
    pagination: PaginationInfo,
    statusCode: number = 200
  ): FastifyReply {
    const response: ApiResponse<T[]> = {
      success: true,
      data,
      meta: {
        pagination,
        timestamp: new Date().toISOString(),
        requestId: reply.request.id,
      },
    }

    return reply.code(statusCode).send(response)
  }

  /**
   * 发送错误响应
   */
  static error(
    reply: FastifyReply,
    code: string,
    message: string,
    statusCode: number = 400,
    details?: any
  ): FastifyReply {
    const response: ApiResponse = {
      success: false,
      error: {
        code,
        message,
        details,
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: reply.request.id,
      },
    }

    return reply.code(statusCode).send(response)
  }

  /**
   * 发送验证错误响应
   */
  static validationError(
    reply: FastifyReply,
    errors: any[],
    message: string = '请求参数验证失败'
  ): FastifyReply {
    return this.error(reply, 'VALIDATION_ERROR', message, 400, errors)
  }

  /**
   * 发送未授权响应
   */
  static unauthorized(
    reply: FastifyReply,
    message: string = '未授权访问'
  ): FastifyReply {
    return this.error(reply, 'UNAUTHORIZED', message, 401)
  }

  /**
   * 发送禁止访问响应
   */
  static forbidden(
    reply: FastifyReply,
    message: string = '禁止访问'
  ): FastifyReply {
    return this.error(reply, 'FORBIDDEN', message, 403)
  }

  /**
   * 发送资源未找到响应
   */
  static notFound(
    reply: FastifyReply,
    message: string = '资源未找到'
  ): FastifyReply {
    return this.error(reply, 'NOT_FOUND', message, 404)
  }

  /**
   * 发送冲突响应
   */
  static conflict(
    reply: FastifyReply,
    message: string = '资源冲突'
  ): FastifyReply {
    return this.error(reply, 'CONFLICT', message, 409)
  }

  /**
   * 发送限流响应
   */
  static tooManyRequests(
    reply: FastifyReply,
    message: string = '请求过于频繁'
  ): FastifyReply {
    return this.error(reply, 'TOO_MANY_REQUESTS', message, 429)
  }

  /**
   * 发送服务器错误响应
   */
  static internalError(
    reply: FastifyReply,
    message: string = '服务器内部错误',
    details?: any
  ): FastifyReply {
    return this.error(reply, 'INTERNAL_ERROR', message, 500, details)
  }

  /**
   * 发送服务不可用响应
   */
  static serviceUnavailable(
    reply: FastifyReply,
    message: string = '服务暂时不可用'
  ): FastifyReply {
    return this.error(reply, 'SERVICE_UNAVAILABLE', message, 503)
  }
}

/**
 * 错误代码常量
 */
export const ErrorCodes = {
  // 通用错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  TOO_MANY_REQUESTS: 'TOO_MANY_REQUESTS',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',

  // 认证相关错误
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  ACCOUNT_SUSPENDED: 'ACCOUNT_SUSPENDED',

  // 用户相关错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  INVALID_PASSWORD: 'INVALID_PASSWORD',
  PASSWORD_TOO_WEAK: 'PASSWORD_TOO_WEAK',

  // 业务相关错误
  CAMPAIGN_NOT_FOUND: 'CAMPAIGN_NOT_FOUND',
  CAMPAIGN_ALREADY_RUNNING: 'CAMPAIGN_ALREADY_RUNNING',
  INSUFFICIENT_CREDITS: 'INSUFFICIENT_CREDITS',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',

  // 第三方服务错误
  OPENAI_API_ERROR: 'OPENAI_API_ERROR',
  STRIPE_API_ERROR: 'STRIPE_API_ERROR',
  EMAIL_SERVICE_ERROR: 'EMAIL_SERVICE_ERROR',
  STORAGE_SERVICE_ERROR: 'STORAGE_SERVICE_ERROR',

  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',
} as const

/**
 * 错误消息映射
 */
export const ErrorMessages = {
  [ErrorCodes.VALIDATION_ERROR]: '请求参数验证失败',
  [ErrorCodes.UNAUTHORIZED]: '未授权访问',
  [ErrorCodes.FORBIDDEN]: '禁止访问',
  [ErrorCodes.NOT_FOUND]: '资源未找到',
  [ErrorCodes.CONFLICT]: '资源冲突',
  [ErrorCodes.TOO_MANY_REQUESTS]: '请求过于频繁',
  [ErrorCodes.INTERNAL_ERROR]: '服务器内部错误',
  [ErrorCodes.SERVICE_UNAVAILABLE]: '服务暂时不可用',

  [ErrorCodes.INVALID_CREDENTIALS]: '用户名或密码错误',
  [ErrorCodes.TOKEN_EXPIRED]: '令牌已过期',
  [ErrorCodes.TOKEN_INVALID]: '无效的令牌',
  [ErrorCodes.EMAIL_NOT_VERIFIED]: '邮箱未验证',
  [ErrorCodes.ACCOUNT_SUSPENDED]: '账户已被暂停',

  [ErrorCodes.USER_NOT_FOUND]: '用户不存在',
  [ErrorCodes.USER_ALREADY_EXISTS]: '用户已存在',
  [ErrorCodes.INVALID_PASSWORD]: '密码错误',
  [ErrorCodes.PASSWORD_TOO_WEAK]: '密码强度不足',

  [ErrorCodes.CAMPAIGN_NOT_FOUND]: '营销活动不存在',
  [ErrorCodes.CAMPAIGN_ALREADY_RUNNING]: '营销活动已在运行中',
  [ErrorCodes.INSUFFICIENT_CREDITS]: '积分不足',
  [ErrorCodes.QUOTA_EXCEEDED]: '配额已超限',
  [ErrorCodes.INVALID_FILE_TYPE]: '不支持的文件类型',
  [ErrorCodes.FILE_TOO_LARGE]: '文件过大',

  [ErrorCodes.OPENAI_API_ERROR]: 'OpenAI服务错误',
  [ErrorCodes.STRIPE_API_ERROR]: 'Stripe支付服务错误',
  [ErrorCodes.EMAIL_SERVICE_ERROR]: '邮件服务错误',
  [ErrorCodes.STORAGE_SERVICE_ERROR]: '存储服务错误',

  [ErrorCodes.DATABASE_ERROR]: '数据库错误',
  [ErrorCodes.CACHE_ERROR]: '缓存服务错误',
  [ErrorCodes.CONNECTION_ERROR]: '连接错误',
} as const

/**
 * 自定义错误类
 */
export class ApiError extends Error {
  public readonly code: string
  public readonly statusCode: number
  public readonly details?: any

  constructor(
    code: string,
    message?: string,
    statusCode: number = 400,
    details?: any
  ) {
    super(message || ErrorMessages[code as keyof typeof ErrorMessages] || '未知错误')
    this.name = 'ApiError'
    this.code = code
    this.statusCode = statusCode
    this.details = details

    // 确保堆栈跟踪正确
    Error.captureStackTrace(this, ApiError)
  }
}

/**
 * 分页工具函数
 */
export function calculatePagination(
  page: number,
  limit: number,
  total: number
): PaginationInfo {
  const totalPages = Math.ceil(total / limit)
  
  return {
    page: Math.max(1, page),
    limit: Math.max(1, limit),
    total,
    totalPages: Math.max(1, totalPages),
  }
}

/**
 * 获取分页偏移量
 */
export function getPaginationOffset(page: number, limit: number): number {
  return Math.max(0, (page - 1) * limit)
}
