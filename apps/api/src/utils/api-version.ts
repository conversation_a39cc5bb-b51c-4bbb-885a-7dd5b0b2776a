// API版本控制工具
// 提供API版本管理和向后兼容性支持

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { ResponseHelper } from './response'
import { logger } from './logger'

/**
 * API版本信息接口
 */
export interface ApiVersionInfo {
  version: string
  deprecated?: boolean
  deprecationDate?: Date
  sunsetDate?: Date
  description?: string
  changes?: string[]
}

/**
 * 支持的API版本配置
 */
export const API_VERSIONS: Record<string, ApiVersionInfo> = {
  'v1': {
    version: '1.0.0',
    description: 'AI数字营销平台API第一版',
    changes: [
      '初始版本发布',
      '用户管理功能',
      'AI内容生成功能',
      '营销活动管理功能',
      '数据分析功能',
      '支付订阅功能'
    ]
  },
  'v2': {
    version: '2.0.0',
    description: 'AI数字营销平台API第二版（规划中）',
    changes: [
      '增强的AI功能',
      '更多营销渠道支持',
      '高级分析功能',
      '企业级功能'
    ]
  }
}

/**
 * 默认API版本
 */
export const DEFAULT_API_VERSION = 'v1'

/**
 * 当前稳定版本
 */
export const CURRENT_STABLE_VERSION = 'v1'

/**
 * API版本管理类
 */
export class ApiVersionManager {
  private supportedVersions: Set<string>

  constructor() {
    this.supportedVersions = new Set(Object.keys(API_VERSIONS))
  }

  /**
   * 检查版本是否支持
   */
  isVersionSupported(version: string): boolean {
    return this.supportedVersions.has(version)
  }

  /**
   * 获取版本信息
   */
  getVersionInfo(version: string): ApiVersionInfo | null {
    return API_VERSIONS[version] || null
  }

  /**
   * 检查版本是否已弃用
   */
  isVersionDeprecated(version: string): boolean {
    const versionInfo = this.getVersionInfo(version)
    return versionInfo?.deprecated || false
  }

  /**
   * 检查版本是否已过期
   */
  isVersionSunset(version: string): boolean {
    const versionInfo = this.getVersionInfo(version)
    if (!versionInfo?.sunsetDate) return false
    
    return new Date() > versionInfo.sunsetDate
  }

  /**
   * 获取所有支持的版本
   */
  getSupportedVersions(): string[] {
    return Array.from(this.supportedVersions)
  }

  /**
   * 获取最新版本
   */
  getLatestVersion(): string {
    const versions = this.getSupportedVersions()
    return versions[versions.length - 1] || DEFAULT_API_VERSION
  }
}

// 创建全局版本管理器实例
export const versionManager = new ApiVersionManager()

/**
 * 从请求中提取API版本
 */
export function extractApiVersion(request: FastifyRequest): string {
  // 1. 从URL路径中提取版本 (优先级最高)
  const pathMatch = request.url.match(/^\/api\/(v\d+)\//)
  if (pathMatch) {
    return pathMatch[1]
  }

  // 2. 从Accept头中提取版本
  const acceptHeader = request.headers.accept
  if (acceptHeader) {
    const versionMatch = acceptHeader.match(/application\/vnd\.ai-marketing\.(v\d+)\+json/)
    if (versionMatch) {
      return versionMatch[1]
    }
  }

  // 3. 从自定义头中提取版本
  const versionHeader = request.headers['api-version'] as string
  if (versionHeader && versionManager.isVersionSupported(versionHeader)) {
    return versionHeader
  }

  // 4. 从查询参数中提取版本
  const query = request.query as { version?: string }
  if (query.version && versionManager.isVersionSupported(query.version)) {
    return query.version
  }

  // 5. 返回默认版本
  return DEFAULT_API_VERSION
}

/**
 * API版本验证中间件
 */
export async function apiVersionMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  const requestedVersion = extractApiVersion(request)

  // 检查版本是否支持
  if (!versionManager.isVersionSupported(requestedVersion)) {
    return ResponseHelper.error(
      reply,
      'UNSUPPORTED_API_VERSION',
      `不支持的API版本: ${requestedVersion}`,
      400,
      {
        requestedVersion,
        supportedVersions: versionManager.getSupportedVersions(),
        latestVersion: versionManager.getLatestVersion()
      }
    )
  }

  // 检查版本是否已过期
  if (versionManager.isVersionSunset(requestedVersion)) {
    return ResponseHelper.error(
      reply,
      'API_VERSION_SUNSET',
      `API版本已过期: ${requestedVersion}`,
      410,
      {
        requestedVersion,
        latestVersion: versionManager.getLatestVersion()
      }
    )
  }

  // 添加弃用警告头
  if (versionManager.isVersionDeprecated(requestedVersion)) {
    const versionInfo = versionManager.getVersionInfo(requestedVersion)
    reply.header('Warning', '299 - "API版本已弃用，请升级到最新版本"')
    reply.header('Sunset', versionInfo?.sunsetDate?.toISOString() || '')
    reply.header('Link', `</api/${versionManager.getLatestVersion()}>; rel="successor-version"`)
    
    logger.warn('使用了已弃用的API版本', {
      version: requestedVersion,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip
    })
  }

  // 在请求对象中存储版本信息
  ;(request as any).apiVersion = requestedVersion

  // 添加版本信息到响应头
  reply.header('API-Version', requestedVersion)
  reply.header('API-Supported-Versions', versionManager.getSupportedVersions().join(', '))
}

/**
 * 注册版本化路由的辅助函数
 */
export function registerVersionedRoute(
  app: FastifyInstance,
  version: string,
  path: string,
  handler: any,
  options: any = {}
) {
  const versionedPath = `/api/${version}${path}`
  
  // 注册路由
  app.register(async function (fastify) {
    fastify.addHook('preHandler', apiVersionMiddleware)
    await fastify.register(handler, { prefix: versionedPath, ...options })
  })

  logger.info(`注册版本化路由: ${versionedPath}`)
}

/**
 * 创建API版本信息端点
 */
export function createVersionInfoEndpoint(app: FastifyInstance) {
  app.get('/api/versions', async (request, reply) => {
    const versions = Object.entries(API_VERSIONS).map(([key, info]) => ({
      version: key,
      ...info,
      isSupported: versionManager.isVersionSupported(key),
      isDeprecated: versionManager.isVersionDeprecated(key),
      isSunset: versionManager.isVersionSunset(key),
      isCurrent: key === CURRENT_STABLE_VERSION
    }))

    return ResponseHelper.success(reply, {
      versions,
      defaultVersion: DEFAULT_API_VERSION,
      currentStableVersion: CURRENT_STABLE_VERSION,
      latestVersion: versionManager.getLatestVersion()
    })
  })

  logger.info('API版本信息端点已创建: /api/versions')
}

/**
 * 版本兼容性检查装饰器
 */
export function RequireVersion(minVersion: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (request: FastifyRequest, reply: FastifyReply) {
      const currentVersion = (request as any).apiVersion || DEFAULT_API_VERSION
      
      // 简单的版本比较（假设版本格式为 v1, v2, v3...）
      const currentVersionNum = parseInt(currentVersion.replace('v', ''))
      const minVersionNum = parseInt(minVersion.replace('v', ''))
      
      if (currentVersionNum < minVersionNum) {
        return ResponseHelper.error(
          reply,
          'INSUFFICIENT_API_VERSION',
          `此功能需要API版本 ${minVersion} 或更高版本`,
          400,
          {
            currentVersion,
            requiredVersion: minVersion,
            latestVersion: versionManager.getLatestVersion()
          }
        )
      }

      return method.apply(this, [request, reply])
    }
  }
}

/**
 * 版本特定功能检查装饰器
 */
export function VersionSpecific(supportedVersions: string[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (request: FastifyRequest, reply: FastifyReply) {
      const currentVersion = (request as any).apiVersion || DEFAULT_API_VERSION
      
      if (!supportedVersions.includes(currentVersion)) {
        return ResponseHelper.error(
          reply,
          'FEATURE_NOT_AVAILABLE',
          `此功能在API版本 ${currentVersion} 中不可用`,
          400,
          {
            currentVersion,
            supportedVersions,
            latestVersion: versionManager.getLatestVersion()
          }
        )
      }

      return method.apply(this, [request, reply])
    }
  }
}
