// 错误处理工具
// 提供统一的错误处理、日志记录、告警通知等功能

import { Request, Response, NextFunction } from 'express'
import { logger } from './logger'
import { SecurityService, AuditEventType } from '../services/security.service'
import { MonitoringService } from '../services/monitoring.service'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  PAYMENT_ERROR = 'PAYMENT_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}

/**
 * 错误严重级别
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 应用错误类
 */
export class AppError extends Error {
  public readonly type: ErrorType
  public readonly statusCode: number
  public readonly severity: ErrorSeverity
  public readonly isOperational: boolean
  public readonly context?: any
  public readonly timestamp: Date

  constructor(
    type: ErrorType,
    message: string,
    statusCode: number = 500,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    isOperational: boolean = true,
    context?: any
  ) {
    super(message)
    
    this.type = type
    this.statusCode = statusCode
    this.severity = severity
    this.isOperational = isOperational
    this.context = context
    this.timestamp = new Date()
    
    // 保持堆栈跟踪
    Error.captureStackTrace(this, this.constructor)
  }
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  /**
   * 处理应用错误
   */
  static handleError(error: Error, req?: Request): void {
    if (error instanceof AppError) {
      this.handleAppError(error, req)
    } else {
      this.handleUnknownError(error, req)
    }
  }

  /**
   * 处理应用错误
   */
  private static handleAppError(error: AppError, req?: Request): void {
    // 记录错误日志
    const logLevel = this.getLogLevel(error.severity)
    const logData = {
      type: error.type,
      message: error.message,
      statusCode: error.statusCode,
      severity: error.severity,
      context: error.context,
      stack: error.stack,
      timestamp: error.timestamp,
      request: req ? this.extractRequestInfo(req) : undefined
    }

    logger[logLevel]('应用错误', logData)

    // 记录审计日志
    if (req) {
      this.logSecurityEvent(error, req)
    }

    // 更新监控指标
    MonitoringService.recordRequest(0, true)

    // 发送告警（高严重级别错误）
    if (error.severity === ErrorSeverity.HIGH || error.severity === ErrorSeverity.CRITICAL) {
      this.sendAlert(error, req)
    }
  }

  /**
   * 处理未知错误
   */
  private static handleUnknownError(error: Error, req?: Request): void {
    const logData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date(),
      request: req ? this.extractRequestInfo(req) : undefined
    }

    logger.error('未知错误', logData)

    // 记录为严重错误
    MonitoringService.recordRequest(0, true)

    // 发送告警
    this.sendAlert(error, req)
  }

  /**
   * Express错误处理中间件
   */
  static expressErrorHandler(
    error: Error,
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    // 处理错误
    ErrorHandler.handleError(error, req)

    // 构建响应
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        error: {
          type: error.type,
          message: error.message,
          ...(process.env.NODE_ENV === 'development' && {
            stack: error.stack,
            context: error.context
          })
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      })
    } else {
      // 未知错误，返回通用错误信息
      res.status(500).json({
        success: false,
        error: {
          type: ErrorType.INTERNAL_ERROR,
          message: process.env.NODE_ENV === 'production' 
            ? '服务器内部错误' 
            : error.message
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      })
    }
  }

  /**
   * 未捕获异常处理器
   */
  static handleUncaughtException(error: Error): void {
    logger.error('未捕获异常', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date()
    })

    // 发送紧急告警
    this.sendCriticalAlert(error)

    // 优雅关闭应用
    process.exit(1)
  }

  /**
   * 未处理Promise拒绝处理器
   */
  static handleUnhandledRejection(reason: any, promise: Promise<any>): void {
    logger.error('未处理的Promise拒绝', {
      reason: reason instanceof Error ? reason.message : String(reason),
      stack: reason instanceof Error ? reason.stack : undefined,
      promise: promise.toString(),
      timestamp: new Date()
    })

    // 发送告警
    this.sendAlert(reason instanceof Error ? reason : new Error(String(reason)))
  }

  /**
   * 获取日志级别
   */
  private static getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error'
      case ErrorSeverity.MEDIUM:
        return 'warn'
      case ErrorSeverity.LOW:
        return 'info'
      default:
        return 'error'
    }
  }

  /**
   * 提取请求信息
   */
  private static extractRequestInfo(req: Request): any {
    return {
      method: req.method,
      url: req.url,
      headers: {
        'user-agent': req.get('user-agent'),
        'x-forwarded-for': req.get('x-forwarded-for'),
        'x-real-ip': req.get('x-real-ip')
      },
      ip: req.ip,
      userId: (req as any).user?.id,
      sessionId: req.sessionID,
      body: this.sanitizeRequestBody(req.body),
      query: req.query,
      params: req.params
    }
  }

  /**
   * 清理请求体中的敏感信息
   */
  private static sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body
    }

    const sanitized = { ...body }
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'creditCard', 'ssn']

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]'
      }
    }

    return sanitized
  }

  /**
   * 记录安全事件
   */
  private static async logSecurityEvent(error: AppError, req: Request): Promise<void> {
    try {
      let eventType: AuditEventType = AuditEventType.SECURITY_VIOLATION

      // 根据错误类型确定审计事件类型
      switch (error.type) {
        case ErrorType.AUTHENTICATION_ERROR:
          eventType = AuditEventType.USER_LOGIN
          break
        case ErrorType.AUTHORIZATION_ERROR:
          eventType = AuditEventType.SECURITY_VIOLATION
          break
        case ErrorType.RATE_LIMIT_ERROR:
          eventType = AuditEventType.SECURITY_VIOLATION
          break
        default:
          eventType = AuditEventType.API_ACCESS
      }

      await SecurityService.logAuditEvent(
        eventType,
        req.url,
        req.method,
        {
          errorType: error.type,
          errorMessage: error.message,
          statusCode: error.statusCode
        },
        {
          userId: (req as any).user?.id,
          sessionId: req.sessionID,
          ipAddress: req.ip || 'unknown',
          userAgent: req.get('user-agent') || 'unknown',
          success: false,
          errorMessage: error.message
        }
      )
    } catch (auditError) {
      logger.error('记录审计日志失败', { auditError })
    }
  }

  /**
   * 发送告警
   */
  private static async sendAlert(error: Error, req?: Request): Promise<void> {
    try {
      const alert = {
        type: 'application_error',
        severity: error instanceof AppError ? error.severity : ErrorSeverity.HIGH,
        message: error.message,
        stack: error.stack,
        timestamp: new Date(),
        request: req ? this.extractRequestInfo(req) : undefined,
        environment: process.env.NODE_ENV || 'unknown'
      }

      // 这里可以集成各种告警渠道
      // 例如：Slack、钉钉、邮件、短信等
      logger.warn('发送错误告警', alert)

      // TODO: 实现具体的告警发送逻辑
      // await SlackService.sendAlert(alert)
      // await EmailService.sendAlert(alert)
    } catch (alertError) {
      logger.error('发送告警失败', { alertError })
    }
  }

  /**
   * 发送紧急告警
   */
  private static async sendCriticalAlert(error: Error): Promise<void> {
    try {
      const alert = {
        type: 'critical_error',
        severity: ErrorSeverity.CRITICAL,
        message: `应用发生严重错误: ${error.message}`,
        stack: error.stack,
        timestamp: new Date(),
        environment: process.env.NODE_ENV || 'unknown',
        action: 'application_restart_required'
      }

      logger.error('发送紧急告警', alert)

      // TODO: 实现紧急告警发送逻辑
      // await EmergencyAlertService.send(alert)
    } catch (alertError) {
      logger.error('发送紧急告警失败', { alertError })
    }
  }
}

/**
 * 错误工厂类
 */
export class ErrorFactory {
  /**
   * 创建验证错误
   */
  static createValidationError(message: string, context?: any): AppError {
    return new AppError(
      ErrorType.VALIDATION_ERROR,
      message,
      400,
      ErrorSeverity.LOW,
      true,
      context
    )
  }

  /**
   * 创建认证错误
   */
  static createAuthenticationError(message: string = '认证失败'): AppError {
    return new AppError(
      ErrorType.AUTHENTICATION_ERROR,
      message,
      401,
      ErrorSeverity.MEDIUM
    )
  }

  /**
   * 创建授权错误
   */
  static createAuthorizationError(message: string = '权限不足'): AppError {
    return new AppError(
      ErrorType.AUTHORIZATION_ERROR,
      message,
      403,
      ErrorSeverity.MEDIUM
    )
  }

  /**
   * 创建资源不存在错误
   */
  static createNotFoundError(resource: string = '资源'): AppError {
    return new AppError(
      ErrorType.NOT_FOUND_ERROR,
      `${resource}不存在`,
      404,
      ErrorSeverity.LOW
    )
  }

  /**
   * 创建冲突错误
   */
  static createConflictError(message: string): AppError {
    return new AppError(
      ErrorType.CONFLICT_ERROR,
      message,
      409,
      ErrorSeverity.MEDIUM
    )
  }

  /**
   * 创建频率限制错误
   */
  static createRateLimitError(message: string = '请求频率超限'): AppError {
    return new AppError(
      ErrorType.RATE_LIMIT_ERROR,
      message,
      429,
      ErrorSeverity.MEDIUM
    )
  }

  /**
   * 创建支付错误
   */
  static createPaymentError(message: string, context?: any): AppError {
    return new AppError(
      ErrorType.PAYMENT_ERROR,
      message,
      402,
      ErrorSeverity.HIGH,
      true,
      context
    )
  }

  /**
   * 创建外部服务错误
   */
  static createExternalServiceError(service: string, message: string): AppError {
    return new AppError(
      ErrorType.EXTERNAL_SERVICE_ERROR,
      `${service}服务错误: ${message}`,
      503,
      ErrorSeverity.HIGH
    )
  }

  /**
   * 创建数据库错误
   */
  static createDatabaseError(message: string, context?: any): AppError {
    return new AppError(
      ErrorType.DATABASE_ERROR,
      `数据库错误: ${message}`,
      500,
      ErrorSeverity.HIGH,
      true,
      context
    )
  }

  /**
   * 创建内部错误
   */
  static createInternalError(message: string = '服务器内部错误', context?: any): AppError {
    return new AppError(
      ErrorType.INTERNAL_ERROR,
      message,
      500,
      ErrorSeverity.HIGH,
      false,
      context
    )
  }
}

/**
 * 异步错误包装器
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

/**
 * 初始化错误处理
 */
export function initializeErrorHandling(): void {
  // 处理未捕获异常
  process.on('uncaughtException', ErrorHandler.handleUncaughtException)

  // 处理未处理的Promise拒绝
  process.on('unhandledRejection', ErrorHandler.handleUnhandledRejection)

  logger.info('错误处理系统初始化完成')
}

// 导出常用错误类型
export {
  AppError,
  ErrorHandler,
  ErrorFactory,
  ErrorType,
  ErrorSeverity
}
