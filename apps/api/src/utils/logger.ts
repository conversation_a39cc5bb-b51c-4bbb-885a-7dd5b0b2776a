import winston from 'winston'
import { config } from '../config'

/**
 * 自定义日志格式
 * 包含时间戳、日志级别、消息和元数据
 */
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`
    
    // 如果有额外的元数据，添加到日志中
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta, null, 2)}`
    }
    
    return logMessage
  })
)

/**
 * 开发环境日志格式
 * 更加友好的控制台输出格式
 */
const devFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} ${level}: ${message}`
    
    // 在开发环境中，如果有错误堆栈，单独显示
    if (meta.stack) {
      logMessage += `\n${meta.stack}`
    } else if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta, null, 2)}`
    }
    
    return logMessage
  })
)

/**
 * 创建Winston日志记录器
 */
export const logger = winston.createLogger({
  level: config.logging.level,
  format: config.isDevelopment ? devFormat : logFormat,
  defaultMeta: {
    service: 'ai-marketing-api',
    environment: config.env,
  },
  transports: [
    // 控制台输出
    new winston.transports.Console({
      handleExceptions: true,
      handleRejections: true,
    }),
  ],
  // 退出时不等待日志写入完成
  exitOnError: false,
})

// 生产环境添加文件日志
if (config.isProduction) {
  logger.add(
    new winston.transports.File({
      filename: config.logging.file,
      handleExceptions: true,
      handleRejections: true,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: logFormat,
    })
  )
  
  // 错误日志单独文件
  logger.add(
    new winston.transports.File({
      filename: config.logging.file.replace('.log', '-error.log'),
      level: 'error',
      handleExceptions: true,
      handleRejections: true,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: logFormat,
    })
  )
}

/**
 * 创建子日志记录器
 * 用于不同模块的日志记录
 */
export function createChildLogger(module: string) {
  return logger.child({ module })
}

/**
 * HTTP请求日志中间件
 * 记录API请求的详细信息
 */
export function createRequestLogger() {
  return {
    logRequest: (request: any) => {
      const startTime = Date.now()
      
      logger.info('HTTP请求开始', {
        method: request.method,
        url: request.url,
        userAgent: request.headers['user-agent'],
        ip: request.ip,
        requestId: request.id,
      })
      
      return startTime
    },
    
    logResponse: (request: any, reply: any, startTime: number) => {
      const duration = Date.now() - startTime
      
      logger.info('HTTP请求完成', {
        method: request.method,
        url: request.url,
        statusCode: reply.statusCode,
        duration: `${duration}ms`,
        requestId: request.id,
      })
    },
    
    logError: (request: any, error: any) => {
      logger.error('HTTP请求错误', {
        method: request.method,
        url: request.url,
        error: error.message,
        stack: error.stack,
        requestId: request.id,
      })
    },
  }
}

/**
 * 数据库操作日志记录器
 */
export const dbLogger = createChildLogger('database')

/**
 * AI服务日志记录器
 */
export const aiLogger = createChildLogger('ai-service')

/**
 * 支付服务日志记录器
 */
export const paymentLogger = createChildLogger('payment')

/**
 * 邮件服务日志记录器
 */
export const emailLogger = createChildLogger('email')

/**
 * 认证服务日志记录器
 */
export const authLogger = createChildLogger('auth')

/**
 * 营销活动日志记录器
 */
export const campaignLogger = createChildLogger('campaign')

/**
 * 数据分析日志记录器
 */
export const analyticsLogger = createChildLogger('analytics')

/**
 * 性能监控日志记录器
 */
export const performanceLogger = createChildLogger('performance')

/**
 * 安全相关日志记录器
 */
export const securityLogger = createChildLogger('security')

/**
 * 记录性能指标
 */
export function logPerformance(operation: string, duration: number, metadata?: any) {
  performanceLogger.info(`性能指标: ${operation}`, {
    operation,
    duration: `${duration}ms`,
    ...metadata,
  })
}

/**
 * 记录安全事件
 */
export function logSecurityEvent(event: string, details: any) {
  securityLogger.warn(`安全事件: ${event}`, {
    event,
    timestamp: new Date().toISOString(),
    ...details,
  })
}

/**
 * 记录业务指标
 */
export function logBusinessMetric(metric: string, value: number, metadata?: any) {
  logger.info(`业务指标: ${metric}`, {
    metric,
    value,
    timestamp: new Date().toISOString(),
    ...metadata,
  })
}

/**
 * 记录API调用
 */
export function logApiCall(service: string, endpoint: string, duration: number, success: boolean) {
  logger.info(`外部API调用: ${service}`, {
    service,
    endpoint,
    duration: `${duration}ms`,
    success,
    timestamp: new Date().toISOString(),
  })
}

/**
 * 记录用户操作
 */
export function logUserAction(userId: string, action: string, metadata?: any) {
  logger.info(`用户操作: ${action}`, {
    userId,
    action,
    timestamp: new Date().toISOString(),
    ...metadata,
  })
}

/**
 * 记录系统事件
 */
export function logSystemEvent(event: string, metadata?: any) {
  logger.info(`系统事件: ${event}`, {
    event,
    timestamp: new Date().toISOString(),
    ...metadata,
  })
}

export default logger
