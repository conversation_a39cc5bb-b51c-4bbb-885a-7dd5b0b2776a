// 输入验证和数据清理工具
// 提供安全的数据验证、清理和转换功能

import { z } from 'zod'
import DOMPurify from 'isomorphic-dompurify'
import validator from 'validator'
import { ApiError, ErrorCodes } from './response'
import { logger } from './logger'

/**
 * 通用验证规则
 */
export const ValidationRules = {
  // 用户相关
  email: z.string().email('邮箱格式不正确').max(255, '邮箱长度不能超过255个字符'),
  password: z.string()
    .min(8, '密码长度至少8个字符')
    .max(128, '密码长度不能超过128个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),
  name: z.string()
    .min(1, '名称不能为空')
    .max(50, '名称长度不能超过50个字符')
    .regex(/^[a-zA-Z\u4e00-\u9fa5\s]+$/, '名称只能包含字母、中文和空格'),
  phone: z.string()
    .regex(/^1[3-9]\d{9}$/, '手机号格式不正确'),
  
  // 通用字段
  id: z.string().uuid('ID格式不正确'),
  url: z.string().url('URL格式不正确').max(2048, 'URL长度不能超过2048个字符'),
  text: z.string().max(10000, '文本长度不能超过10000个字符'),
  title: z.string()
    .min(1, '标题不能为空')
    .max(200, '标题长度不能超过200个字符'),
  description: z.string().max(1000, '描述长度不能超过1000个字符'),
  
  // 分页参数
  page: z.number().int().min(1, '页码必须大于0').default(1),
  limit: z.number().int().min(1, '每页数量必须大于0').max(100, '每页数量不能超过100').default(20),
  
  // 日期时间
  date: z.string().datetime('日期时间格式不正确'),
  dateRange: z.object({
    startDate: z.string().datetime('开始日期格式不正确'),
    endDate: z.string().datetime('结束日期格式不正确')
  }).refine(data => new Date(data.startDate) <= new Date(data.endDate), {
    message: '开始日期不能晚于结束日期'
  }),
  
  // 营销活动相关
  campaignType: z.enum(['EMAIL', 'SMS', 'SOCIAL', 'PUSH', 'DISPLAY', 'SEARCH'], {
    errorMap: () => ({ message: '营销活动类型不正确' })
  }),
  campaignStatus: z.enum(['DRAFT', 'SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELLED'], {
    errorMap: () => ({ message: '营销活动状态不正确' })
  }),
  budget: z.number().min(0, '预算不能为负数').max(1000000, '预算不能超过100万'),
  
  // AI生成相关
  aiPrompt: z.string()
    .min(1, '提示词不能为空')
    .max(2000, '提示词长度不能超过2000个字符'),
  aiType: z.enum(['TEXT_GENERATION', 'IMAGE_GENERATION', 'CONTENT_OPTIMIZATION'], {
    errorMap: () => ({ message: 'AI生成类型不正确' })
  }),
  
  // 文件上传相关
  fileSize: z.number().max(10 * 1024 * 1024, '文件大小不能超过10MB'),
  fileName: z.string()
    .min(1, '文件名不能为空')
    .max(255, '文件名长度不能超过255个字符')
    .regex(/^[^<>:"/\\|?*]+$/, '文件名包含非法字符'),
  
  // 支付相关
  amount: z.number().min(0.01, '金额必须大于0').max(999999.99, '金额不能超过999999.99'),
  currency: z.enum(['CNY', 'USD', 'EUR'], {
    errorMap: () => ({ message: '货币类型不正确' })
  })
}

/**
 * 数据清理工具类
 */
export class DataSanitizer {
  /**
   * 清理HTML内容，防止XSS攻击
   */
  static sanitizeHtml(html: string): string {
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'a'],
      ALLOWED_ATTR: ['href', 'target'],
      ALLOW_DATA_ATTR: false
    })
  }

  /**
   * 清理文本内容，移除危险字符
   */
  static sanitizeText(text: string): string {
    return validator.escape(text.trim())
  }

  /**
   * 清理文件名，移除危险字符
   */
  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9\u4e00-\u9fa5._-]/g, '_') // 替换非法字符为下划线
      .replace(/_{2,}/g, '_') // 合并多个下划线
      .replace(/^_+|_+$/g, '') // 移除首尾下划线
  }

  /**
   * 清理URL，确保安全
   */
  static sanitizeUrl(url: string): string {
    if (!validator.isURL(url, { protocols: ['http', 'https'] })) {
      throw new ApiError(ErrorCodes.VALIDATION_ERROR, 'URL格式不正确')
    }
    return url
  }

  /**
   * 清理邮箱地址
   */
  static sanitizeEmail(email: string): string {
    const normalizedEmail = validator.normalizeEmail(email.toLowerCase().trim())
    if (!normalizedEmail || !validator.isEmail(normalizedEmail)) {
      throw new ApiError(ErrorCodes.VALIDATION_ERROR, '邮箱格式不正确')
    }
    return normalizedEmail
  }

  /**
   * 清理手机号
   */
  static sanitizePhone(phone: string): string {
    const cleanPhone = phone.replace(/\D/g, '') // 移除所有非数字字符
    if (!validator.isMobilePhone(cleanPhone, 'zh-CN')) {
      throw new ApiError(ErrorCodes.VALIDATION_ERROR, '手机号格式不正确')
    }
    return cleanPhone
  }

  /**
   * 清理JSON数据，移除危险字段
   */
  static sanitizeJson(data: any, allowedFields: string[]): any {
    if (typeof data !== 'object' || data === null) {
      return data
    }

    const sanitized: any = {}
    for (const field of allowedFields) {
      if (data.hasOwnProperty(field)) {
        sanitized[field] = data[field]
      }
    }
    return sanitized
  }
}

/**
 * 验证装饰器
 */
export function ValidateBody(schema: z.ZodSchema) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (request: any, reply: any) {
      try {
        const validatedData = schema.parse(request.body)
        request.body = validatedData
        return method.apply(this, [request, reply])
      } catch (error) {
        if (error instanceof z.ZodError) {
          const validationErrors = error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))

          logger.warn('请求体验证失败', {
            url: request.url,
            method: request.method,
            errors: validationErrors
          })

          return reply.code(400).send({
            success: false,
            error: {
              code: ErrorCodes.VALIDATION_ERROR,
              message: '请求参数验证失败',
              details: validationErrors
            },
            meta: {
              timestamp: new Date().toISOString(),
              requestId: request.id
            }
          })
        }
        throw error
      }
    }
  }
}

/**
 * 查询参数验证装饰器
 */
export function ValidateQuery(schema: z.ZodSchema) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (request: any, reply: any) {
      try {
        const validatedData = schema.parse(request.query)
        request.query = validatedData
        return method.apply(this, [request, reply])
      } catch (error) {
        if (error instanceof z.ZodError) {
          const validationErrors = error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))

          return reply.code(400).send({
            success: false,
            error: {
              code: ErrorCodes.VALIDATION_ERROR,
              message: '查询参数验证失败',
              details: validationErrors
            },
            meta: {
              timestamp: new Date().toISOString(),
              requestId: request.id
            }
          })
        }
        throw error
      }
    }
  }
}

/**
 * 路径参数验证装饰器
 */
export function ValidateParams(schema: z.ZodSchema) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (request: any, reply: any) {
      try {
        const validatedData = schema.parse(request.params)
        request.params = validatedData
        return method.apply(this, [request, reply])
      } catch (error) {
        if (error instanceof z.ZodError) {
          const validationErrors = error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))

          return reply.code(400).send({
            success: false,
            error: {
              code: ErrorCodes.VALIDATION_ERROR,
              message: '路径参数验证失败',
              details: validationErrors
            },
            meta: {
              timestamp: new Date().toISOString(),
              requestId: request.id
            }
          })
        }
        throw error
      }
    }
  }
}

/**
 * 通用验证函数
 */
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationErrors = error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code
      }))

      throw new ApiError(
        ErrorCodes.VALIDATION_ERROR,
        '数据验证失败',
        400,
        validationErrors
      )
    }
    throw error
  }
}

/**
 * 分页参数验证
 */
export const PaginationSchema = z.object({
  page: ValidationRules.page,
  limit: ValidationRules.limit
})

/**
 * 常用验证模式
 */
export const CommonSchemas = {
  // 用户注册
  userRegister: z.object({
    email: ValidationRules.email,
    password: ValidationRules.password,
    firstName: ValidationRules.name,
    lastName: ValidationRules.name,
    phone: ValidationRules.phone.optional()
  }),

  // 用户登录
  userLogin: z.object({
    email: ValidationRules.email,
    password: z.string().min(1, '密码不能为空')
  }),

  // 用户资料更新
  userUpdate: z.object({
    firstName: ValidationRules.name.optional(),
    lastName: ValidationRules.name.optional(),
    phone: ValidationRules.phone.optional(),
    avatar: ValidationRules.url.optional()
  }),

  // 营销活动创建
  campaignCreate: z.object({
    name: ValidationRules.title,
    description: ValidationRules.description.optional(),
    type: ValidationRules.campaignType,
    budget: ValidationRules.budget,
    targetAudience: z.record(z.any()).optional()
  }),

  // AI内容生成
  aiGenerate: z.object({
    prompt: ValidationRules.aiPrompt,
    type: ValidationRules.aiType,
    options: z.record(z.any()).optional()
  })
}
