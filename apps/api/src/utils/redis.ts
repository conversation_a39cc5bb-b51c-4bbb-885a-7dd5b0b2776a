// Redis 缓存工具类
// 提供缓存操作的封装和管理

import { createClient, RedisClientType } from 'redis'
import { config } from '../config'
import { logger } from './logger'

/**
 * Redis 客户端类
 * 封装Redis操作，提供缓存管理功能
 */
export class RedisClient {
  private client: RedisClientType
  private isConnected: boolean = false

  constructor() {
    this.client = createClient({
      url: config.redis.url,
      password: config.redis.password,
      socket: {
        connectTimeout: 10000,
        lazyConnect: true,
      },
    })

    this.setupEventHandlers()
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    this.client.on('connect', () => {
      logger.info('Redis客户端连接中...')
    })

    this.client.on('ready', () => {
      this.isConnected = true
      logger.info('Redis客户端连接成功')
    })

    this.client.on('error', (error) => {
      this.isConnected = false
      logger.error('Redis连接错误:', error)
    })

    this.client.on('end', () => {
      this.isConnected = false
      logger.info('Redis连接已关闭')
    })

    this.client.on('reconnecting', () => {
      logger.info('Redis客户端重新连接中...')
    })
  }

  /**
   * 连接到Redis
   */
  async connect(): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.client.connect()
      }
    } catch (error) {
      logger.error('Redis连接失败:', error)
      throw error
    }
  }

  /**
   * 断开Redis连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.client.disconnect()
      }
    } catch (error) {
      logger.error('Redis断开连接失败:', error)
      throw error
    }
  }

  /**
   * 检查连接状态
   */
  isReady(): boolean {
    return this.isConnected
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value)
      const fullKey = this.getFullKey(key)

      if (ttl) {
        await this.client.setEx(fullKey, ttl, serializedValue)
      } else {
        await this.client.set(fullKey, serializedValue)
      }

      logger.debug(`缓存设置成功: ${fullKey}`)
    } catch (error) {
      logger.error(`缓存设置失败: ${key}`, error)
      throw error
    }
  }

  /**
   * 获取缓存
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const fullKey = this.getFullKey(key)
      const value = await this.client.get(fullKey)

      if (value === null) {
        return null
      }

      const parsedValue = JSON.parse(value)
      logger.debug(`缓存获取成功: ${fullKey}`)
      return parsedValue
    } catch (error) {
      logger.error(`缓存获取失败: ${key}`, error)
      return null
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      const fullKey = this.getFullKey(key)
      await this.client.del(fullKey)
      logger.debug(`缓存删除成功: ${fullKey}`)
    } catch (error) {
      logger.error(`缓存删除失败: ${key}`, error)
      throw error
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const fullKey = this.getFullKey(key)
      const result = await this.client.exists(fullKey)
      return result === 1
    } catch (error) {
      logger.error(`缓存检查失败: ${key}`, error)
      return false
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      const fullKey = this.getFullKey(key)
      await this.client.expire(fullKey, ttl)
      logger.debug(`缓存过期时间设置成功: ${fullKey}, TTL: ${ttl}`)
    } catch (error) {
      logger.error(`缓存过期时间设置失败: ${key}`, error)
      throw error
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      const fullKey = this.getFullKey(key)
      return await this.client.ttl(fullKey)
    } catch (error) {
      logger.error(`获取缓存TTL失败: ${key}`, error)
      return -1
    }
  }

  /**
   * 批量删除缓存
   */
  async delPattern(pattern: string): Promise<void> {
    try {
      const fullPattern = this.getFullKey(pattern)
      const keys = await this.client.keys(fullPattern)
      
      if (keys.length > 0) {
        await this.client.del(keys)
        logger.debug(`批量删除缓存成功: ${keys.length} 个键`)
      }
    } catch (error) {
      logger.error(`批量删除缓存失败: ${pattern}`, error)
      throw error
    }
  }

  /**
   * 增加计数器
   */
  async incr(key: string): Promise<number> {
    try {
      const fullKey = this.getFullKey(key)
      return await this.client.incr(fullKey)
    } catch (error) {
      logger.error(`计数器增加失败: ${key}`, error)
      throw error
    }
  }

  /**
   * 减少计数器
   */
  async decr(key: string): Promise<number> {
    try {
      const fullKey = this.getFullKey(key)
      return await this.client.decr(fullKey)
    } catch (error) {
      logger.error(`计数器减少失败: ${key}`, error)
      throw error
    }
  }

  /**
   * 哈希表操作 - 设置字段
   */
  async hset(key: string, field: string, value: any): Promise<void> {
    try {
      const fullKey = this.getFullKey(key)
      const serializedValue = JSON.stringify(value)
      await this.client.hSet(fullKey, field, serializedValue)
      logger.debug(`哈希表设置成功: ${fullKey}.${field}`)
    } catch (error) {
      logger.error(`哈希表设置失败: ${key}.${field}`, error)
      throw error
    }
  }

  /**
   * 哈希表操作 - 获取字段
   */
  async hget<T = any>(key: string, field: string): Promise<T | null> {
    try {
      const fullKey = this.getFullKey(key)
      const value = await this.client.hGet(fullKey, field)
      
      if (value === undefined) {
        return null
      }

      return JSON.parse(value)
    } catch (error) {
      logger.error(`哈希表获取失败: ${key}.${field}`, error)
      return null
    }
  }

  /**
   * 哈希表操作 - 删除字段
   */
  async hdel(key: string, field: string): Promise<void> {
    try {
      const fullKey = this.getFullKey(key)
      await this.client.hDel(fullKey, field)
      logger.debug(`哈希表删除成功: ${fullKey}.${field}`)
    } catch (error) {
      logger.error(`哈希表删除失败: ${key}.${field}`, error)
      throw error
    }
  }

  /**
   * 获取完整的缓存键名
   */
  private getFullKey(key: string): string {
    return `${config.cache.prefix}${key}`
  }

  /**
   * 获取Redis客户端实例（用于高级操作）
   */
  getClient(): RedisClientType {
    return this.client
  }
}

// 创建全局Redis客户端实例
export const redis = new RedisClient()

/**
 * 缓存装饰器
 * 用于自动缓存函数结果
 */
export function Cache(key: string, ttl: number = 3600) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${key}:${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cachedResult = await redis.get(cacheKey)
      if (cachedResult !== null) {
        logger.debug(`缓存命中: ${cacheKey}`)
        return cachedResult
      }

      // 执行原方法
      const result = await method.apply(this, args)
      
      // 存储到缓存
      await redis.set(cacheKey, result, ttl)
      logger.debug(`缓存存储: ${cacheKey}`)
      
      return result
    }
  }
}
