import { PrismaClient } from '@prisma/client'
import { config } from '../config'
import { dbLogger } from './logger'

/**
 * Prisma客户端配置
 * 包含日志记录和错误处理
 */
const prismaConfig = {
  // 数据源配置
  datasources: {
    db: {
      url: config.database.url,
    },
  },
  
  // 日志配置
  log: [
    {
      emit: 'event' as const,
      level: 'query' as const,
    },
    {
      emit: 'event' as const,
      level: 'error' as const,
    },
    {
      emit: 'event' as const,
      level: 'info' as const,
    },
    {
      emit: 'event' as const,
      level: 'warn' as const,
    },
  ],
}

/**
 * 创建Prisma客户端实例
 */
export const prisma = new PrismaClient(prismaConfig)

/**
 * 设置Prisma事件监听器
 * 记录数据库操作日志
 */
prisma.$on('query', (e) => {
  dbLogger.debug('数据库查询', {
    query: e.query,
    params: e.params,
    duration: `${e.duration}ms`,
    timestamp: e.timestamp,
  })
})

prisma.$on('error', (e) => {
  dbLogger.error('数据库错误', {
    message: e.message,
    target: e.target,
    timestamp: e.timestamp,
  })
})

prisma.$on('info', (e) => {
  dbLogger.info('数据库信息', {
    message: e.message,
    target: e.target,
    timestamp: e.timestamp,
  })
})

prisma.$on('warn', (e) => {
  dbLogger.warn('数据库警告', {
    message: e.message,
    target: e.target,
    timestamp: e.timestamp,
  })
})

/**
 * 数据库连接健康检查
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`
    dbLogger.info('数据库健康检查通过')
    return true
  } catch (error) {
    dbLogger.error('数据库健康检查失败', { error })
    return false
  }
}

/**
 * 数据库连接统计信息
 */
export async function getDatabaseStats() {
  try {
    const stats = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation
      FROM pg_stats 
      WHERE schemaname = 'public'
      LIMIT 10
    `
    
    return stats
  } catch (error) {
    dbLogger.error('获取数据库统计信息失败', { error })
    return null
  }
}

/**
 * 执行数据库事务
 * 提供统一的事务处理和错误记录
 */
export async function executeTransaction<T>(
  callback: (tx: any) => Promise<T>,
  options?: {
    maxWait?: number
    timeout?: number
  }
): Promise<T> {
  const startTime = Date.now()
  
  try {
    const result = await prisma.$transaction(callback, {
      maxWait: options?.maxWait || 5000, // 5秒
      timeout: options?.timeout || 10000, // 10秒
    })
    
    const duration = Date.now() - startTime
    dbLogger.info('事务执行成功', { duration: `${duration}ms` })
    
    return result
  } catch (error) {
    const duration = Date.now() - startTime
    dbLogger.error('事务执行失败', {
      error: error instanceof Error ? error.message : String(error),
      duration: `${duration}ms`,
    })
    throw error
  }
}

/**
 * 批量操作工具
 * 用于高效处理大量数据操作
 */
export class BatchOperations {
  private batchSize: number
  
  constructor(batchSize: number = 1000) {
    this.batchSize = batchSize
  }
  
  /**
   * 批量创建记录
   */
  async batchCreate<T>(
    model: any,
    data: T[],
    options?: { skipDuplicates?: boolean }
  ): Promise<void> {
    const batches = this.createBatches(data)
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]
      
      try {
        await model.createMany({
          data: batch,
          skipDuplicates: options?.skipDuplicates || false,
        })
        
        dbLogger.debug(`批量创建完成 ${i + 1}/${batches.length}`, {
          batchSize: batch.length,
          totalBatches: batches.length,
        })
      } catch (error) {
        dbLogger.error(`批量创建失败 ${i + 1}/${batches.length}`, {
          error: error instanceof Error ? error.message : String(error),
          batchSize: batch.length,
        })
        throw error
      }
    }
  }
  
  /**
   * 批量更新记录
   */
  async batchUpdate<T>(
    operations: Array<() => Promise<T>>
  ): Promise<T[]> {
    const batches = this.createBatches(operations)
    const results: T[] = []
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]
      
      try {
        const batchResults = await Promise.all(
          batch.map(operation => operation())
        )
        
        results.push(...batchResults)
        
        dbLogger.debug(`批量更新完成 ${i + 1}/${batches.length}`, {
          batchSize: batch.length,
          totalBatches: batches.length,
        })
      } catch (error) {
        dbLogger.error(`批量更新失败 ${i + 1}/${batches.length}`, {
          error: error instanceof Error ? error.message : String(error),
          batchSize: batch.length,
        })
        throw error
      }
    }
    
    return results
  }
  
  /**
   * 将数组分割成批次
   */
  private createBatches<T>(items: T[]): T[][] {
    const batches: T[][] = []
    
    for (let i = 0; i < items.length; i += this.batchSize) {
      batches.push(items.slice(i, i + this.batchSize))
    }
    
    return batches
  }
}

/**
 * 数据库查询优化工具
 */
export class QueryOptimizer {
  /**
   * 分页查询
   */
  static async paginate<T>(
    model: any,
    options: {
      page: number
      limit: number
      where?: any
      orderBy?: any
      include?: any
      select?: any
    }
  ): Promise<{
    data: T[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
      hasNext: boolean
      hasPrev: boolean
    }
  }> {
    const { page, limit, where, orderBy, include, select } = options
    
    // 计算偏移量
    const skip = (page - 1) * limit
    
    // 并行执行查询和计数
    const [data, total] = await Promise.all([
      model.findMany({
        skip,
        take: limit,
        where,
        orderBy,
        include,
        select,
      }),
      model.count({ where }),
    ])
    
    // 计算分页信息
    const totalPages = Math.ceil(total / limit)
    const hasNext = page < totalPages
    const hasPrev = page > 1
    
    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    }
  }
  
  /**
   * 游标分页查询
   */
  static async cursorPaginate<T>(
    model: any,
    options: {
      cursor?: any
      limit: number
      where?: any
      orderBy?: any
      include?: any
      select?: any
    }
  ): Promise<{
    data: T[]
    nextCursor?: any
    hasNext: boolean
  }> {
    const { cursor, limit, where, orderBy, include, select } = options
    
    // 查询比限制多一条记录，用于判断是否有下一页
    const data = await model.findMany({
      take: limit + 1,
      cursor: cursor ? { id: cursor } : undefined,
      skip: cursor ? 1 : 0,
      where,
      orderBy,
      include,
      select,
    })
    
    // 判断是否有下一页
    const hasNext = data.length > limit
    
    // 如果有下一页，移除多查询的那条记录
    if (hasNext) {
      data.pop()
    }
    
    // 获取下一页的游标
    const nextCursor = hasNext && data.length > 0 
      ? data[data.length - 1].id 
      : undefined
    
    return {
      data,
      nextCursor,
      hasNext,
    }
  }
}

/**
 * 数据库连接池监控
 */
export function monitorConnectionPool() {
  setInterval(async () => {
    try {
      // 这里可以添加连接池监控逻辑
      // Prisma目前不直接暴露连接池统计信息
      // 可以通过查询系统表获取连接信息
      
      const connections = await prisma.$queryRaw`
        SELECT count(*) as active_connections 
        FROM pg_stat_activity 
        WHERE state = 'active'
      `
      
      dbLogger.debug('数据库连接池状态', { connections })
    } catch (error) {
      dbLogger.error('连接池监控失败', { error })
    }
  }, 60000) // 每分钟检查一次
}

/**
 * 优雅关闭数据库连接
 */
export async function closeDatabaseConnection() {
  try {
    await prisma.$disconnect()
    dbLogger.info('数据库连接已关闭')
  } catch (error) {
    dbLogger.error('关闭数据库连接失败', { error })
    throw error
  }
}

export default prisma
