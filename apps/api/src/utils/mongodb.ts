// MongoDB 连接和操作工具类
// 用于处理用户行为分析、日志存储等非关系型数据

import { MongoClient, Db, Collection, MongoClientOptions } from 'mongodb'
import { config } from '../config'
import { logger } from './logger'

/**
 * MongoDB 客户端类
 * 封装MongoDB操作，提供文档数据库功能
 */
export class MongoDBClient {
  private client: MongoClient
  private db: Db | null = null
  private isConnected: boolean = false

  constructor() {
    const options: MongoClientOptions = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4, // 使用IPv4
    }

    this.client = new MongoClient(config.mongodb.url, options)
  }

  /**
   * 连接到MongoDB
   */
  async connect(): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.client.connect()
        this.db = this.client.db(config.mongodb.dbName)
        this.isConnected = true
        logger.info('MongoDB连接成功')
      }
    } catch (error) {
      logger.error('MongoDB连接失败:', error)
      throw error
    }
  }

  /**
   * 断开MongoDB连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.client.close()
        this.isConnected = false
        this.db = null
        logger.info('MongoDB连接已关闭')
      }
    } catch (error) {
      logger.error('MongoDB断开连接失败:', error)
      throw error
    }
  }

  /**
   * 检查连接状态
   */
  isReady(): boolean {
    return this.isConnected && this.db !== null
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): Db {
    if (!this.db) {
      throw new Error('MongoDB未连接')
    }
    return this.db
  }

  /**
   * 获取集合
   */
  getCollection<T = any>(name: string): Collection<T> {
    if (!this.db) {
      throw new Error('MongoDB未连接')
    }
    return this.db.collection<T>(name)
  }

  /**
   * 记录用户行为
   */
  async recordUserBehavior(behavior: {
    userId: string
    action: string
    page?: string
    sessionId?: string
    metadata?: any
    ipAddress?: string
    userAgent?: string
  }): Promise<void> {
    try {
      const collection = this.getCollection('user_behaviors')
      
      const document = {
        ...behavior,
        timestamp: new Date(),
      }

      await collection.insertOne(document)
      logger.debug(`用户行为记录成功: ${behavior.userId} - ${behavior.action}`)
    } catch (error) {
      logger.error('用户行为记录失败:', error)
      throw error
    }
  }

  /**
   * 获取用户行为历史
   */
  async getUserBehaviorHistory(
    userId: string,
    options: {
      limit?: number
      skip?: number
      startDate?: Date
      endDate?: Date
      actions?: string[]
    } = {}
  ): Promise<any[]> {
    try {
      const collection = this.getCollection('user_behaviors')
      
      const query: any = { userId }
      
      // 时间范围过滤
      if (options.startDate || options.endDate) {
        query.timestamp = {}
        if (options.startDate) {
          query.timestamp.$gte = options.startDate
        }
        if (options.endDate) {
          query.timestamp.$lte = options.endDate
        }
      }
      
      // 行为类型过滤
      if (options.actions && options.actions.length > 0) {
        query.action = { $in: options.actions }
      }

      const cursor = collection
        .find(query)
        .sort({ timestamp: -1 })
        .skip(options.skip || 0)
        .limit(options.limit || 100)

      return await cursor.toArray()
    } catch (error) {
      logger.error('获取用户行为历史失败:', error)
      throw error
    }
  }

  /**
   * 记录AI生成内容
   */
  async recordAIGeneratedContent(content: {
    userId: string
    type: 'text' | 'image' | 'video'
    prompt: string
    result?: string
    metadata?: any
    status: 'pending' | 'processing' | 'completed' | 'failed'
  }): Promise<string> {
    try {
      const collection = this.getCollection('ai_generated_content')
      
      const document = {
        ...content,
        createdAt: new Date(),
      }

      const result = await collection.insertOne(document)
      logger.debug(`AI生成内容记录成功: ${content.userId} - ${content.type}`)
      
      return result.insertedId.toString()
    } catch (error) {
      logger.error('AI生成内容记录失败:', error)
      throw error
    }
  }

  /**
   * 更新AI生成内容状态
   */
  async updateAIGeneratedContent(
    id: string,
    updates: {
      result?: string
      status?: 'pending' | 'processing' | 'completed' | 'failed'
      metadata?: any
    }
  ): Promise<void> {
    try {
      const collection = this.getCollection('ai_generated_content')
      
      await collection.updateOne(
        { _id: id },
        { 
          $set: {
            ...updates,
            updatedAt: new Date(),
          }
        }
      )
      
      logger.debug(`AI生成内容更新成功: ${id}`)
    } catch (error) {
      logger.error('AI生成内容更新失败:', error)
      throw error
    }
  }

  /**
   * 更新用户画像
   */
  async updateUserProfile(userId: string, profile: {
    demographics?: any
    interests?: string[]
    behaviors?: any
    preferences?: any
    segments?: string[]
    score?: any
  }): Promise<void> {
    try {
      const collection = this.getCollection('user_profiles')
      
      await collection.updateOne(
        { userId },
        { 
          $set: {
            ...profile,
            updatedAt: new Date(),
          }
        },
        { upsert: true }
      )
      
      logger.debug(`用户画像更新成功: ${userId}`)
    } catch (error) {
      logger.error('用户画像更新失败:', error)
      throw error
    }
  }

  /**
   * 获取用户画像
   */
  async getUserProfile(userId: string): Promise<any | null> {
    try {
      const collection = this.getCollection('user_profiles')
      
      const profile = await collection.findOne({ userId })
      return profile
    } catch (error) {
      logger.error('获取用户画像失败:', error)
      throw error
    }
  }

  /**
   * 记录营销活动数据
   */
  async recordCampaignData(data: {
    campaignId: string
    metrics?: any
    audience?: any
    performance?: any
  }): Promise<void> {
    try {
      const collection = this.getCollection('campaign_data')
      
      const document = {
        ...data,
        timestamp: new Date(),
      }

      await collection.insertOne(document)
      logger.debug(`营销活动数据记录成功: ${data.campaignId}`)
    } catch (error) {
      logger.error('营销活动数据记录失败:', error)
      throw error
    }
  }

  /**
   * 获取营销活动分析数据
   */
  async getCampaignAnalytics(
    campaignId: string,
    options: {
      startDate?: Date
      endDate?: Date
      aggregation?: 'hour' | 'day' | 'week' | 'month'
    } = {}
  ): Promise<any[]> {
    try {
      const collection = this.getCollection('campaign_data')
      
      const pipeline: any[] = [
        { $match: { campaignId } }
      ]
      
      // 时间范围过滤
      if (options.startDate || options.endDate) {
        const timeMatch: any = {}
        if (options.startDate) {
          timeMatch.$gte = options.startDate
        }
        if (options.endDate) {
          timeMatch.$lte = options.endDate
        }
        pipeline.push({ $match: { timestamp: timeMatch } })
      }
      
      // 时间聚合
      if (options.aggregation) {
        const groupBy = this.getTimeGroupBy(options.aggregation)
        pipeline.push({
          $group: {
            _id: groupBy,
            count: { $sum: 1 },
            avgMetrics: { $avg: '$metrics' },
            data: { $push: '$$ROOT' }
          }
        })
        pipeline.push({ $sort: { _id: 1 } })
      }

      return await collection.aggregate(pipeline).toArray()
    } catch (error) {
      logger.error('获取营销活动分析数据失败:', error)
      throw error
    }
  }

  /**
   * 记录系统日志
   */
  async recordSystemLog(log: {
    level: 'error' | 'warn' | 'info' | 'debug'
    message: string
    service?: string
    userId?: string
    requestId?: string
    metadata?: any
  }): Promise<void> {
    try {
      const collection = this.getCollection('system_logs')
      
      const document = {
        ...log,
        timestamp: new Date(),
      }

      await collection.insertOne(document)
    } catch (error) {
      // 日志记录失败不应该影响主要业务流程
      console.error('系统日志记录失败:', error)
    }
  }

  /**
   * 获取时间分组表达式
   */
  private getTimeGroupBy(aggregation: string): any {
    switch (aggregation) {
      case 'hour':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' },
          hour: { $hour: '$timestamp' }
        }
      case 'day':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' },
          day: { $dayOfMonth: '$timestamp' }
        }
      case 'week':
        return {
          year: { $year: '$timestamp' },
          week: { $week: '$timestamp' }
        }
      case 'month':
        return {
          year: { $year: '$timestamp' },
          month: { $month: '$timestamp' }
        }
      default:
        return '$timestamp'
    }
  }
}

// 创建全局MongoDB客户端实例
export const mongodb = new MongoDBClient()
