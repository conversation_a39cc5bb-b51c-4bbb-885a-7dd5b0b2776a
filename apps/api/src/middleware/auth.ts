import { FastifyRequest, FastifyReply } from 'fastify'
import { prisma } from '../utils/database'
import { authLogger } from '../utils/logger'

/**
 * JWT认证中间件
 * 验证用户身份并将用户信息添加到请求对象中
 */
export async function authenticate(request: FastifyRequest, reply: FastifyReply) {
  try {
    // 从请求头获取Authorization token
    const authHeader = request.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      authLogger.warn('认证失败：缺少Authorization头部', {
        url: request.url,
        method: request.method,
        ip: request.ip,
      })
      
      return reply.code(401).send({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: '缺少访问令牌',
        },
        timestamp: new Date().toISOString(),
      })
    }

    // 提取token
    const token = authHeader.substring(7) // 移除 "Bearer " 前缀

    try {
      // 验证JWT token
      const decoded = request.server.jwt.verify(token) as any
      
      // 检查token类型
      if (decoded.type === 'refresh') {
        authLogger.warn('认证失败：使用了刷新令牌作为访问令牌', {
          userId: decoded.userId,
        })
        
        return reply.code(401).send({
          success: false,
          error: {
            code: 'INVALID_TOKEN_TYPE',
            message: '令牌类型错误',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 查找用户信息
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          status: true,
        }
      })

      if (!user) {
        authLogger.warn('认证失败：用户不存在', {
          userId: decoded.userId,
        })
        
        return reply.code(401).send({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 检查用户状态
      if (user.status !== 'ACTIVE') {
        authLogger.warn('认证失败：用户账户被禁用', {
          userId: user.id,
          status: user.status,
        })
        
        return reply.code(403).send({
          success: false,
          error: {
            code: 'ACCOUNT_DISABLED',
            message: '账户已被禁用',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 检查会话是否存在且有效
      const session = await prisma.userSession.findFirst({
        where: {
          userId: user.id,
          token: token,
          expiresAt: {
            gt: new Date()
          }
        }
      })

      if (!session) {
        authLogger.warn('认证失败：会话无效或已过期', {
          userId: user.id,
        })
        
        return reply.code(401).send({
          success: false,
          error: {
            code: 'SESSION_EXPIRED',
            message: '会话已过期，请重新登录',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 将用户信息添加到请求对象
      request.user = {
        id: user.id,
        email: user.email,
        role: decoded.role || 'user',
      }

      authLogger.debug('用户认证成功', {
        userId: user.id,
        email: user.email,
        url: request.url,
        method: request.method,
      })

    } catch (jwtError) {
      authLogger.warn('认证失败：JWT令牌无效', {
        error: jwtError instanceof Error ? jwtError.message : String(jwtError),
        url: request.url,
        method: request.method,
      })
      
      return reply.code(401).send({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: '访问令牌无效',
        },
        timestamp: new Date().toISOString(),
      })
    }

  } catch (error) {
    authLogger.error('认证中间件错误', {
      error: error instanceof Error ? error.message : String(error),
      url: request.url,
      method: request.method,
    })
    
    return reply.code(500).send({
      success: false,
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: '认证过程中发生错误',
      },
      timestamp: new Date().toISOString(),
    })
  }
}

/**
 * 可选认证中间件
 * 如果提供了token则验证，否则继续执行
 */
export async function optionalAuthenticate(request: FastifyRequest, reply: FastifyReply) {
  const authHeader = request.headers.authorization
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    // 没有提供token，继续执行
    return
  }

  // 有token则进行认证
  return authenticate(request, reply)
}

/**
 * 管理员权限检查中间件
 * 需要先通过authenticate中间件
 */
export async function requireAdmin(request: FastifyRequest, reply: FastifyReply) {
  if (!request.user) {
    return reply.code(401).send({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: '未授权访问',
      },
      timestamp: new Date().toISOString(),
    })
  }

  if (request.user.role !== 'admin') {
    authLogger.warn('权限检查失败：需要管理员权限', {
      userId: request.user.id,
      role: request.user.role,
      url: request.url,
      method: request.method,
    })
    
    return reply.code(403).send({
      success: false,
      error: {
        code: 'INSUFFICIENT_PERMISSIONS',
        message: '权限不足，需要管理员权限',
      },
      timestamp: new Date().toISOString(),
    })
  }
}

/**
 * 用户权限检查中间件
 * 检查用户是否有权限访问特定资源
 */
export function requirePermission(permission: string) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    if (!request.user) {
      return reply.code(401).send({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授权访问',
        },
        timestamp: new Date().toISOString(),
      })
    }

    // 这里可以实现更复杂的权限检查逻辑
    // 例如从数据库查询用户权限等
    
    authLogger.debug('权限检查', {
      userId: request.user.id,
      permission,
      url: request.url,
      method: request.method,
    })

    // 暂时简单实现：管理员拥有所有权限
    if (request.user.role === 'admin') {
      return
    }

    // 普通用户的权限检查逻辑
    const userPermissions = ['read:profile', 'update:profile', 'create:campaign', 'read:campaign']
    
    if (!userPermissions.includes(permission)) {
      authLogger.warn('权限检查失败：权限不足', {
        userId: request.user.id,
        permission,
        userPermissions,
      })
      
      return reply.code(403).send({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: `权限不足，需要 ${permission} 权限`,
        },
        timestamp: new Date().toISOString(),
      })
    }
  }
}

/**
 * 资源所有者检查中间件
 * 检查用户是否是资源的所有者
 */
export function requireOwnership(resourceIdParam: string = 'id') {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    if (!request.user) {
      return reply.code(401).send({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授权访问',
        },
        timestamp: new Date().toISOString(),
      })
    }

    const resourceId = (request.params as any)[resourceIdParam]
    const userId = request.user.id

    // 管理员可以访问所有资源
    if (request.user.role === 'admin') {
      return
    }

    // 检查资源所有权的逻辑需要根据具体资源类型实现
    // 这里提供一个通用的框架
    
    authLogger.debug('资源所有权检查', {
      userId,
      resourceId,
      resourceIdParam,
      url: request.url,
      method: request.method,
    })

    // 如果资源ID就是用户ID，则允许访问
    if (resourceId === userId) {
      return
    }

    // 其他情况需要具体实现
    // 例如检查campaign是否属于该用户等
    
    authLogger.warn('资源所有权检查失败', {
      userId,
      resourceId,
      resourceIdParam,
    })
    
    return reply.code(403).send({
      success: false,
      error: {
        code: 'RESOURCE_ACCESS_DENIED',
        message: '无权访问该资源',
      },
      timestamp: new Date().toISOString(),
    })
  }
}

// 注册认证中间件到Fastify实例
export function registerAuthMiddleware(fastify: any) {
  fastify.decorate('authenticate', authenticate)
  fastify.decorate('optionalAuthenticate', optionalAuthenticate)
  fastify.decorate('requireAdmin', requireAdmin)
  fastify.decorate('requirePermission', requirePermission)
  fastify.decorate('requireOwnership', requireOwnership)
}
