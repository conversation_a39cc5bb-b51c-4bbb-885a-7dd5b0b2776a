// 安全中间件
// 提供各种安全保护功能，包括CORS、安全头、输入验证等

import { FastifyRequest, FastifyReply } from 'fastify'
import rateLimit from '@fastify/rate-limit'
import helmet from '@fastify/helmet'
import cors from '@fastify/cors'
import { config } from '../config'
import { ResponseHelper, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'
import { redis } from '../utils/redis'

/**
 * CORS配置
 */
export const corsConfig = {
  origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => {
    // 允许的域名列表
    const allowedOrigins = config.security.allowedOrigins

    // 开发环境允许所有域名
    if (config.isDevelopment) {
      callback(null, true)
      return
    }

    // 检查是否在允许列表中
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      logger.warn('CORS阻止的请求', { origin })
      callback(new Error('CORS策略不允许此域名'), false)
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Request-ID',
    'X-Forwarded-For',
    'User-Agent',
    'API-Version'
  ],
  exposedHeaders: [
    'X-Total-Count',
    'X-Page-Count',
    'X-Current-Page',
    'X-Per-Page',
    'API-Version',
    'API-Supported-Versions',
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset'
  ],
  maxAge: 86400 // 24小时
}

/**
 * 安全头配置
 */
export const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:'],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", 'https://api.openai.com', 'https://api.stripe.com'],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
}

/**
 * 限流配置
 */
export const rateLimitConfig = {
  max: config.security.rateLimit.max,
  timeWindow: config.security.rateLimit.window,
  cache: 10000,
  allowList: ['127.0.0.1', '::1'], // 本地IP白名单
  skipOnError: false,
  keyGenerator: (request: FastifyRequest) => {
    // 优先使用用户ID，其次使用IP
    const userId = (request as any).user?.id
    if (userId) {
      return `user:${userId}`
    }
    return request.ip
  },
  errorResponseBuilder: (request: FastifyRequest, context: any) => {
    return {
      success: false,
      error: {
        code: ErrorCodes.TOO_MANY_REQUESTS,
        message: '请求过于频繁，请稍后再试',
        details: {
          limit: context.max,
          window: context.timeWindow,
          retryAfter: Math.round(context.ttl / 1000)
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: request.id
      }
    }
  },
  onExceeding: (request: FastifyRequest) => {
    logger.warn('限流触发', {
      ip: request.ip,
      url: request.url,
      method: request.method,
      userAgent: request.headers['user-agent'],
      userId: (request as any).user?.id
    })
  },
  onExceeded: (request: FastifyRequest) => {
    logger.error('限流超出', {
      ip: request.ip,
      url: request.url,
      method: request.method,
      userAgent: request.headers['user-agent'],
      userId: (request as any).user?.id
    })
  }
}

/**
 * IP白名单中间件
 */
export async function ipWhitelistMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  const clientIp = request.ip
  const whitelist = config.security.ipWhitelist

  // 如果没有配置白名单，则跳过检查
  if (!whitelist || whitelist.length === 0) {
    return
  }

  // 检查IP是否在白名单中
  const isAllowed = whitelist.some(allowedIp => {
    if (allowedIp.includes('/')) {
      // CIDR格式的IP段
      return isIpInCidr(clientIp, allowedIp)
    } else {
      // 单个IP地址
      return clientIp === allowedIp
    }
  })

  if (!isAllowed) {
    logger.warn('IP白名单阻止的请求', {
      ip: clientIp,
      url: request.url,
      method: request.method,
      userAgent: request.headers['user-agent']
    })

    return ResponseHelper.forbidden(reply, 'IP地址不在允许列表中')
  }
}

/**
 * 请求大小限制中间件
 */
export async function requestSizeLimitMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  const contentLength = request.headers['content-length']
  const maxSize = config.security.maxRequestSize

  if (contentLength && parseInt(contentLength) > maxSize) {
    logger.warn('请求体过大', {
      contentLength,
      maxSize,
      ip: request.ip,
      url: request.url
    })

    return ResponseHelper.error(
      reply,
      ErrorCodes.VALIDATION_ERROR,
      '请求体过大',
      413,
      { maxSize, actualSize: contentLength }
    )
  }
}

/**
 * 安全头中间件
 */
export async function securityHeadersMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  // 添加自定义安全头
  reply.header('X-Content-Type-Options', 'nosniff')
  reply.header('X-Frame-Options', 'DENY')
  reply.header('X-XSS-Protection', '1; mode=block')
  reply.header('Referrer-Policy', 'strict-origin-when-cross-origin')
  reply.header('Permissions-Policy', 'geolocation=(), microphone=(), camera=()')
  
  // 移除可能泄露服务器信息的头
  reply.removeHeader('X-Powered-By')
  reply.removeHeader('Server')
}

/**
 * 请求日志中间件
 */
export async function requestLoggingMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  const startTime = Date.now()

  // 记录请求开始
  logger.info('请求开始', {
    method: request.method,
    url: request.url,
    ip: request.ip,
    userAgent: request.headers['user-agent'],
    requestId: request.id,
    userId: (request as any).user?.id
  })

  // 在响应完成后记录
  reply.addHook('onSend', async (request, reply, payload) => {
    const duration = Date.now() - startTime
    
    logger.info('请求完成', {
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      duration,
      ip: request.ip,
      requestId: request.id,
      userId: (request as any).user?.id
    })

    // 添加响应时间头
    reply.header('X-Response-Time', `${duration}ms`)
    
    return payload
  })
}

/**
 * 恶意请求检测中间件
 */
export async function maliciousRequestDetectionMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  const suspiciousPatterns = [
    // SQL注入模式
    /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/i,
    // XSS模式
    /<script[^>]*>.*?<\/script>/gi,
    // 路径遍历模式
    /\.\.[\/\\]/,
    // 命令注入模式
    /[;&|`$(){}[\]]/
  ]

  const requestData = JSON.stringify({
    url: request.url,
    query: request.query,
    body: request.body,
    headers: request.headers
  })

  // 检查是否包含恶意模式
  const isMalicious = suspiciousPatterns.some(pattern => pattern.test(requestData))

  if (isMalicious) {
    logger.error('检测到恶意请求', {
      ip: request.ip,
      url: request.url,
      method: request.method,
      userAgent: request.headers['user-agent'],
      requestData: config.isDevelopment ? requestData : '[REDACTED]'
    })

    // 记录到Redis用于进一步分析
    await redis.incr(`malicious_requests:${request.ip}`)
    await redis.expire(`malicious_requests:${request.ip}`, 3600) // 1小时过期

    return ResponseHelper.forbidden(reply, '请求被安全策略阻止')
  }
}

/**
 * 检查IP是否在CIDR范围内
 */
function isIpInCidr(ip: string, cidr: string): boolean {
  // 简化的CIDR检查实现
  // 在生产环境中应该使用更完善的IP库
  const [network, prefixLength] = cidr.split('/')
  
  if (!prefixLength) {
    return ip === network
  }

  // 这里应该实现完整的CIDR检查逻辑
  // 为了简化，暂时只做基本的前缀匹配
  const prefix = network.split('.').slice(0, Math.ceil(parseInt(prefixLength) / 8)).join('.')
  return ip.startsWith(prefix)
}

/**
 * 注册所有安全中间件
 */
export function registerSecurityMiddleware(app: any) {
  // 注册CORS
  app.register(cors, corsConfig)
  
  // 注册安全头
  app.register(helmet, helmetConfig)
  
  // 注册限流
  app.register(rateLimit, rateLimitConfig)
  
  // 注册自定义安全中间件
  app.addHook('preHandler', ipWhitelistMiddleware)
  app.addHook('preHandler', requestSizeLimitMiddleware)
  app.addHook('preHandler', securityHeadersMiddleware)
  app.addHook('preHandler', requestLoggingMiddleware)
  app.addHook('preHandler', maliciousRequestDetectionMiddleware)
  
  logger.info('安全中间件注册完成')
}
