// 统一错误处理中间件
// 捕获和处理所有API错误，提供统一的错误响应格式

import { FastifyError, FastifyRequest, FastifyReply } from 'fastify'
import { ZodError } from 'zod'
import { PrismaClientKnownRequestError, PrismaClientValidationError } from '@prisma/client/runtime/library'
import { ApiError, ResponseHelper, ErrorCodes } from '../utils/response'
import { logger } from '../utils/logger'

/**
 * 全局错误处理器
 */
export async function errorHandler(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
) {
  // 记录错误日志
  logger.error('API错误:', {
    error: error.message,
    stack: error.stack,
    url: request.url,
    method: request.method,
    headers: request.headers,
    body: request.body,
    query: request.query,
    params: request.params,
    requestId: request.id,
  })

  // 处理不同类型的错误
  if (error instanceof ApiError) {
    // 自定义API错误
    return ResponseHelper.error(
      reply,
      error.code,
      error.message,
      error.statusCode,
      error.details
    )
  }

  if (error instanceof ZodError) {
    // Zod验证错误
    const validationErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
    }))

    return ResponseHelper.validationError(
      reply,
      validationErrors,
      '请求参数验证失败'
    )
  }

  if (error instanceof PrismaClientKnownRequestError) {
    // Prisma数据库错误
    return handlePrismaError(error, reply)
  }

  if (error instanceof PrismaClientValidationError) {
    // Prisma验证错误
    return ResponseHelper.error(
      reply,
      ErrorCodes.VALIDATION_ERROR,
      '数据验证失败',
      400,
      process.env.NODE_ENV === 'development' ? error.message : undefined
    )
  }

  // JWT错误
  if (error.code === 'FST_JWT_BAD_REQUEST') {
    return ResponseHelper.error(
      reply,
      ErrorCodes.TOKEN_INVALID,
      '无效的令牌',
      401
    )
  }

  if (error.code === 'FST_JWT_AUTHORIZATION_TOKEN_EXPIRED') {
    return ResponseHelper.error(
      reply,
      ErrorCodes.TOKEN_EXPIRED,
      '令牌已过期',
      401
    )
  }

  if (error.code === 'FST_JWT_NO_AUTHORIZATION_IN_HEADER') {
    return ResponseHelper.error(
      reply,
      ErrorCodes.UNAUTHORIZED,
      '缺少授权头',
      401
    )
  }

  // 限流错误
  if (error.code === 'FST_TOO_MANY_REQUESTS') {
    return ResponseHelper.tooManyRequests(reply, '请求过于频繁，请稍后再试')
  }

  // 文件上传错误
  if (error.code === 'FST_FILES_LIMIT') {
    return ResponseHelper.error(
      reply,
      ErrorCodes.VALIDATION_ERROR,
      '上传文件数量超出限制',
      400
    )
  }

  if (error.code === 'FST_FILE_SIZE_LIMIT') {
    return ResponseHelper.error(
      reply,
      ErrorCodes.FILE_TOO_LARGE,
      '文件大小超出限制',
      400
    )
  }

  // 请求体过大错误
  if (error.code === 'FST_ERR_CTP_BODY_TOO_LARGE') {
    return ResponseHelper.error(
      reply,
      ErrorCodes.VALIDATION_ERROR,
      '请求体过大',
      413
    )
  }

  // 请求超时错误
  if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
    return ResponseHelper.error(
      reply,
      ErrorCodes.CONNECTION_ERROR,
      '请求超时',
      408
    )
  }

  // 数据库连接错误
  if (error.message.includes('database') || error.message.includes('connection')) {
    return ResponseHelper.error(
      reply,
      ErrorCodes.DATABASE_ERROR,
      '数据库连接错误',
      503
    )
  }

  // 默认服务器错误
  return ResponseHelper.internalError(
    reply,
    '服务器内部错误',
    process.env.NODE_ENV === 'development' ? {
      message: error.message,
      stack: error.stack,
    } : undefined
  )
}

/**
 * 处理Prisma数据库错误
 */
function handlePrismaError(error: PrismaClientKnownRequestError, reply: FastifyReply) {
  switch (error.code) {
    case 'P2002':
      // 唯一约束违反
      const target = error.meta?.target as string[]
      const field = target ? target[0] : '字段'
      return ResponseHelper.conflict(reply, `${field}已存在`)

    case 'P2025':
      // 记录未找到
      return ResponseHelper.notFound(reply, '记录未找到')

    case 'P2003':
      // 外键约束违反
      return ResponseHelper.error(
        reply,
        ErrorCodes.VALIDATION_ERROR,
        '关联数据不存在',
        400
      )

    case 'P2004':
      // 约束失败
      return ResponseHelper.error(
        reply,
        ErrorCodes.VALIDATION_ERROR,
        '数据约束验证失败',
        400
      )

    case 'P2014':
      // 关系违反
      return ResponseHelper.error(
        reply,
        ErrorCodes.CONFLICT,
        '数据关系冲突',
        409
      )

    case 'P2015':
      // 相关记录未找到
      return ResponseHelper.notFound(reply, '相关记录未找到')

    case 'P2016':
      // 查询解释错误
      return ResponseHelper.error(
        reply,
        ErrorCodes.VALIDATION_ERROR,
        '查询参数错误',
        400
      )

    case 'P2017':
      // 记录未连接
      return ResponseHelper.error(
        reply,
        ErrorCodes.VALIDATION_ERROR,
        '记录关系未建立',
        400
      )

    case 'P2018':
      // 必需的连接记录未找到
      return ResponseHelper.notFound(reply, '必需的关联记录未找到')

    case 'P2019':
      // 输入错误
      return ResponseHelper.error(
        reply,
        ErrorCodes.VALIDATION_ERROR,
        '输入数据格式错误',
        400
      )

    case 'P2020':
      // 值超出范围
      return ResponseHelper.error(
        reply,
        ErrorCodes.VALIDATION_ERROR,
        '数据值超出允许范围',
        400
      )

    case 'P2021':
      // 表不存在
      return ResponseHelper.error(
        reply,
        ErrorCodes.DATABASE_ERROR,
        '数据表不存在',
        500
      )

    case 'P2022':
      // 列不存在
      return ResponseHelper.error(
        reply,
        ErrorCodes.DATABASE_ERROR,
        '数据列不存在',
        500
      )

    case 'P2023':
      // 数据不一致
      return ResponseHelper.error(
        reply,
        ErrorCodes.DATABASE_ERROR,
        '数据不一致',
        500
      )

    case 'P2024':
      // 连接池超时
      return ResponseHelper.error(
        reply,
        ErrorCodes.DATABASE_ERROR,
        '数据库连接超时',
        503
      )

    case 'P2027':
      // 查询引擎错误
      return ResponseHelper.error(
        reply,
        ErrorCodes.DATABASE_ERROR,
        '数据库查询错误',
        500
      )

    default:
      // 其他Prisma错误
      return ResponseHelper.error(
        reply,
        ErrorCodes.DATABASE_ERROR,
        '数据库操作失败',
        500,
        process.env.NODE_ENV === 'development' ? {
          code: error.code,
          message: error.message,
          meta: error.meta,
        } : undefined
      )
  }
}

/**
 * 404错误处理器
 */
export async function notFoundHandler(request: FastifyRequest, reply: FastifyReply) {
  return ResponseHelper.notFound(reply, `路由 ${request.method} ${request.url} 不存在`)
}

/**
 * 预处理错误处理器
 * 用于处理请求预处理阶段的错误
 */
export async function preHandlerErrorHandler(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
) {
  // 记录预处理错误
  logger.warn('预处理错误:', {
    error: error.message,
    url: request.url,
    method: request.method,
    requestId: request.id,
  })

  // 认证错误
  if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
    return ResponseHelper.unauthorized(reply, '认证失败')
  }

  // 权限错误
  if (error.message.includes('forbidden') || error.message.includes('permission')) {
    return ResponseHelper.forbidden(reply, '权限不足')
  }

  // 其他预处理错误
  return ResponseHelper.error(
    reply,
    ErrorCodes.VALIDATION_ERROR,
    '请求预处理失败',
    400
  )
}
