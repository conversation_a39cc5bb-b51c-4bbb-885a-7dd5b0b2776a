import { FastifyInstance } from 'fastify'
import { config, corsConfig, swaggerConfig } from '../config'
import { logger } from '../utils/logger'

/**
 * 注册所有Fastify插件
 * 包括CORS、安全、限流、文档等插件
 */
export async function registerPlugins(app: FastifyInstance) {
  try {
    // 注册CORS插件
    await app.register(require('@fastify/cors'), corsConfig)
    logger.info('✅ CORS插件注册成功')

    // 注册安全插件
    await app.register(require('@fastify/helmet'), {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    })
    logger.info('✅ 安全插件注册成功')

    // 注册限流插件
    await app.register(require('@fastify/rate-limit'), {
      max: config.security.rateLimit.max,
      timeWindow: config.security.rateLimit.window,
      errorResponseBuilder: (request, context) => {
        return {
          code: 429,
          error: 'Too Many Requests',
          message: `请求过于频繁，请在 ${Math.round(context.ttl / 1000)} 秒后重试`,
          expiresIn: context.ttl,
        }
      },
    })
    logger.info('✅ 限流插件注册成功')

    // 注册JWT插件
    await app.register(require('@fastify/jwt'), {
      secret: config.jwt.secret,
      sign: {
        expiresIn: config.jwt.expiresIn,
      },
    })
    logger.info('✅ JWT插件注册成功')

    // 注册Cookie插件
    await app.register(require('@fastify/cookie'), {
      secret: config.jwt.secret,
      parseOptions: {
        httpOnly: true,
        secure: config.isProduction,
        sameSite: 'strict',
      },
    })
    logger.info('✅ Cookie插件注册成功')

    // 注册文件上传插件
    await app.register(require('@fastify/multipart'), {
      limits: {
        fileSize: config.upload.maxFileSize,
        files: 5,
      },
    })
    logger.info('✅ 文件上传插件注册成功')

    // 注册Swagger文档插件（仅在开发环境）
    if (config.isDevelopment) {
      await app.register(require('@fastify/swagger'), swaggerConfig)
      await app.register(require('@fastify/swagger-ui'), {
        routePrefix: config.urls.docs,
        uiConfig: swaggerConfig.uiConfig,
        staticCSP: swaggerConfig.staticCSP,
        transformStaticCSP: swaggerConfig.transformStaticCSP,
      })
      logger.info('✅ Swagger文档插件注册成功')
    }

    // 注册请求日志插件
    await app.register(async function (fastify) {
      fastify.addHook('onRequest', async (request, reply) => {
        request.startTime = Date.now()
        logger.info('📥 请求开始', {
          method: request.method,
          url: request.url,
          ip: request.ip,
          userAgent: request.headers['user-agent'],
        })
      })

      fastify.addHook('onResponse', async (request, reply) => {
        const duration = Date.now() - (request.startTime || Date.now())
        logger.info('📤 请求完成', {
          method: request.method,
          url: request.url,
          statusCode: reply.statusCode,
          duration: `${duration}ms`,
        })
      })

      fastify.addHook('onError', async (request, reply, error) => {
        logger.error('❌ 请求错误', {
          method: request.method,
          url: request.url,
          error: error.message,
          stack: error.stack,
        })
      })
    })
    logger.info('✅ 请求日志插件注册成功')

    // 注册健康检查插件
    await app.register(async function (fastify) {
      fastify.get('/health', async (request, reply) => {
        const healthData = {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          environment: config.env,
          version: '1.0.0',
          memory: {
            used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
            external: Math.round(process.memoryUsage().external / 1024 / 1024),
          },
          checks: {
            database: 'healthy', // 这里可以添加数据库健康检查
            redis: 'healthy',    // 这里可以添加Redis健康检查
          }
        }

        return reply.code(200).send(healthData)
      })
    })
    logger.info('✅ 健康检查插件注册成功')

    // 注册错误处理插件
    await app.register(async function (fastify) {
      fastify.setErrorHandler(async (error, request, reply) => {
        // 记录错误日志
        logger.error('🚨 服务器错误', {
          error: error.message,
          stack: error.stack,
          method: request.method,
          url: request.url,
        })

        // 根据错误类型返回不同的响应
        if (error.validation) {
          return reply.code(400).send({
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: '请求参数验证失败',
              details: error.validation,
            },
            timestamp: new Date().toISOString(),
          })
        }

        if (error.statusCode === 401) {
          return reply.code(401).send({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: '未授权访问',
            },
            timestamp: new Date().toISOString(),
          })
        }

        if (error.statusCode === 403) {
          return reply.code(403).send({
            success: false,
            error: {
              code: 'FORBIDDEN',
              message: '禁止访问',
            },
            timestamp: new Date().toISOString(),
          })
        }

        if (error.statusCode === 404) {
          return reply.code(404).send({
            success: false,
            error: {
              code: 'NOT_FOUND',
              message: '资源不存在',
            },
            timestamp: new Date().toISOString(),
          })
        }

        if (error.statusCode === 429) {
          return reply.code(429).send({
            success: false,
            error: {
              code: 'TOO_MANY_REQUESTS',
              message: '请求过于频繁',
            },
            timestamp: new Date().toISOString(),
          })
        }

        // 默认服务器错误
        const statusCode = error.statusCode || 500
        return reply.code(statusCode).send({
          success: false,
          error: {
            code: 'INTERNAL_SERVER_ERROR',
            message: config.isProduction ? '服务器内部错误' : error.message,
          },
          timestamp: new Date().toISOString(),
        })
      })

      // 404处理
      fastify.setNotFoundHandler(async (request, reply) => {
        return reply.code(404).send({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: `路由 ${request.method} ${request.url} 不存在`,
          },
          timestamp: new Date().toISOString(),
        })
      })
    })
    logger.info('✅ 错误处理插件注册成功')

    logger.info('🎉 所有插件注册完成')
  } catch (error) {
    logger.error('❌ 插件注册失败', { error })
    throw error
  }
}

// 扩展FastifyRequest类型以包含自定义属性
declare module 'fastify' {
  interface FastifyRequest {
    startTime?: number
    user?: {
      id: string
      email: string
      role: string
    }
  }
}
