// Swagger API文档配置
// 自动生成API文档和交互式API测试界面

import swaggerJsdoc from 'swagger-jsdoc'
import { version } from '../../package.json'

/**
 * Swagger配置选项
 */
const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'AI数字营销平台 API',
      version,
      description: `
        AI数字营销平台的RESTful API文档
        
        ## 功能特性
        - 用户认证和授权
        - AI内容生成（文本和图像）
        - 营销活动管理
        - 邮件营销自动化
        - 数据分析和报告
        - 工作流自动化
        
        ## 认证方式
        API使用JWT Bearer Token进行认证。在请求头中添加：
        \`Authorization: Bearer <your-token>\`
        
        ## 错误处理
        API使用标准HTTP状态码，错误响应格式：
        \`\`\`json
        {
          "success": false,
          "error": {
            "code": "ERROR_CODE",
            "message": "错误描述",
            "details": {}
          }
        }
        \`\`\`
        
        ## 分页
        列表接口支持分页，参数：
        - \`page\`: 页码（从1开始）
        - \`limit\`: 每页数量（默认20，最大100）
        
        ## 限流
        API实施限流策略：
        - 普通用户：100请求/15分钟
        - 付费用户：1000请求/15分钟
        - 管理员：无限制
      `,
      contact: {
        name: 'API支持',
        email: '<EMAIL>',
        url: 'https://example.com/support'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.API_URL || 'http://localhost:3001',
        description: '开发环境'
      },
      {
        url: 'https://api-staging.example.com',
        description: '测试环境'
      },
      {
        url: 'https://api.example.com',
        description: '生产环境'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT认证令牌'
        }
      },
      schemas: {
        // 通用响应模式
        SuccessResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            data: {
              type: 'object',
              description: '响应数据'
            },
            message: {
              type: 'string',
              description: '成功消息'
            }
          }
        },
        ErrorResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            error: {
              type: 'object',
              properties: {
                code: {
                  type: 'string',
                  description: '错误代码'
                },
                message: {
                  type: 'string',
                  description: '错误消息'
                },
                details: {
                  type: 'object',
                  description: '错误详情'
                }
              }
            }
          }
        },
        // 分页响应模式
        PaginatedResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            data: {
              type: 'array',
              items: {
                type: 'object'
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: {
                  type: 'integer',
                  description: '当前页码'
                },
                limit: {
                  type: 'integer',
                  description: '每页数量'
                },
                total: {
                  type: 'integer',
                  description: '总记录数'
                },
                totalPages: {
                  type: 'integer',
                  description: '总页数'
                }
              }
            }
          }
        },
        // 用户模式
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: '用户ID'
            },
            email: {
              type: 'string',
              format: 'email',
              description: '邮箱地址'
            },
            firstName: {
              type: 'string',
              description: '名字'
            },
            lastName: {
              type: 'string',
              description: '姓氏'
            },
            avatar: {
              type: 'string',
              format: 'uri',
              description: '头像URL'
            },
            role: {
              type: 'string',
              enum: ['USER', 'ADMIN', 'SUPER_ADMIN'],
              description: '用户角色'
            },
            isActive: {
              type: 'boolean',
              description: '是否激活'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: '创建时间'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: '更新时间'
            }
          }
        },
        // 营销活动模式
        Campaign: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: '活动ID'
            },
            name: {
              type: 'string',
              description: '活动名称'
            },
            description: {
              type: 'string',
              description: '活动描述'
            },
            type: {
              type: 'string',
              enum: ['EMAIL', 'SMS', 'SOCIAL_MEDIA', 'DISPLAY_AD', 'SEARCH_AD'],
              description: '活动类型'
            },
            status: {
              type: 'string',
              enum: ['DRAFT', 'SCHEDULED', 'ACTIVE', 'PAUSED', 'COMPLETED', 'CANCELLED'],
              description: '活动状态'
            },
            targetAudience: {
              type: 'object',
              description: '目标受众配置'
            },
            budget: {
              type: 'object',
              properties: {
                total: {
                  type: 'number',
                  description: '总预算'
                },
                daily: {
                  type: 'number',
                  description: '日预算'
                },
                currency: {
                  type: 'string',
                  description: '货币类型'
                }
              }
            },
            schedule: {
              type: 'object',
              properties: {
                startDate: {
                  type: 'string',
                  format: 'date-time',
                  description: '开始时间'
                },
                endDate: {
                  type: 'string',
                  format: 'date-time',
                  description: '结束时间'
                }
              }
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: '创建时间'
            }
          }
        },
        // AI生成历史模式
        AIGenerationHistory: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: '生成记录ID'
            },
            type: {
              type: 'string',
              enum: ['TEXT_GENERATION', 'IMAGE_GENERATION'],
              description: '生成类型'
            },
            prompt: {
              type: 'string',
              description: '输入提示词'
            },
            result: {
              type: 'string',
              description: '生成结果'
            },
            metadata: {
              type: 'object',
              description: '元数据'
            },
            isFavorite: {
              type: 'boolean',
              description: '是否收藏'
            },
            tags: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: '标签'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: '创建时间'
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: '认证失败',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                error: {
                  code: 'UNAUTHORIZED',
                  message: '认证失败，请提供有效的访问令牌'
                }
              }
            }
          }
        },
        ForbiddenError: {
          description: '权限不足',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                error: {
                  code: 'FORBIDDEN',
                  message: '权限不足，无法访问此资源'
                }
              }
            }
          }
        },
        NotFoundError: {
          description: '资源不存在',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                error: {
                  code: 'NOT_FOUND',
                  message: '请求的资源不存在'
                }
              }
            }
          }
        },
        ValidationError: {
          description: '参数验证失败',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                error: {
                  code: 'VALIDATION_ERROR',
                  message: '参数验证失败',
                  details: {
                    field: 'email',
                    message: '邮箱格式不正确'
                  }
                }
              }
            }
          }
        },
        RateLimitError: {
          description: '请求频率超限',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              },
              example: {
                success: false,
                error: {
                  code: 'TOO_MANY_REQUESTS',
                  message: '请求频率超限，请稍后重试'
                }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Authentication',
        description: '用户认证相关接口'
      },
      {
        name: 'Users',
        description: '用户管理相关接口'
      },
      {
        name: 'AI Generation',
        description: 'AI内容生成相关接口'
      },
      {
        name: 'Campaigns',
        description: '营销活动管理相关接口'
      },
      {
        name: 'Email Marketing',
        description: '邮件营销相关接口'
      },
      {
        name: 'Automation',
        description: '营销自动化相关接口'
      },
      {
        name: 'Analytics',
        description: '数据分析相关接口'
      },
      {
        name: 'Reports',
        description: '报告生成相关接口'
      },
      {
        name: 'System',
        description: '系统管理相关接口'
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
    './src/models/*.ts'
  ]
}

/**
 * 生成Swagger规范
 */
export const swaggerSpec = swaggerJsdoc(options)

/**
 * Swagger UI配置
 */
export const swaggerUiOptions = {
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #2563eb }
    .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; border-radius: 8px }
  `,
  customSiteTitle: 'AI数字营销平台 API文档',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    docExpansion: 'none',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    tryItOutEnabled: true
  }
}
