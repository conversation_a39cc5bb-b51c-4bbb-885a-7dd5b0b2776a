import { FastifyInstance } from 'fastify'
import { logger } from '../utils/logger'
import { config } from '../config'

/**
 * 注册所有API路由
 * 按模块组织路由，统一管理API版本
 */
export async function registerRoutes(app: FastifyInstance) {
  try {
    // API版本前缀
    const apiPrefix = config.urls.api

    // 注册认证路由
    await app.register(async function (fastify) {
      await fastify.register(require('./auth'), { prefix: `${apiPrefix}/auth` })
    })
    logger.info('✅ 认证路由注册成功')

    // 注册用户路由
    await app.register(async function (fastify) {
      await fastify.register(require('./users'), { prefix: `${apiPrefix}/users` })
    })
    logger.info('✅ 用户路由注册成功')

    // 注册AI服务路由
    await app.register(async function (fastify) {
      await fastify.register(require('./ai'), { prefix: `${apiPrefix}/ai` })
    })
    logger.info('✅ AI服务路由注册成功')

    // 注册营销活动路由
    await app.register(async function (fastify) {
      await fastify.register(require('./campaigns'), { prefix: `${apiPrefix}/campaigns` })
    })
    logger.info('✅ 营销活动路由注册成功')

    // 注册数据分析路由
    await app.register(async function (fastify) {
      await fastify.register(require('./analytics'), { prefix: `${apiPrefix}/analytics` })
    })
    logger.info('✅ 数据分析路由注册成功')

    // 注册支付路由
    await app.register(async function (fastify) {
      await fastify.register(require('./payments'), { prefix: `${apiPrefix}/payments` })
    })
    logger.info('✅ 支付路由注册成功')

    // 注册文件上传路由
    await app.register(async function (fastify) {
      await fastify.register(require('./uploads'), { prefix: `${apiPrefix}/uploads` })
    })
    logger.info('✅ 文件上传路由注册成功')

    // 注册系统管理路由
    await app.register(async function (fastify) {
      await fastify.register(require('./admin'), { prefix: `${apiPrefix}/admin` })
    })
    logger.info('✅ 系统管理路由注册成功')

    // 根路径重定向到API文档
    await app.register(async function (fastify) {
      fastify.get('/', async (request, reply) => {
        if (config.isDevelopment) {
          return reply.redirect(config.urls.docs)
        }
        return reply.send({
          name: 'AI数字营销平台API',
          version: '1.0.0',
          description: '基于AI技术的数字营销平台后端API服务',
          environment: config.env,
          timestamp: new Date().toISOString(),
          endpoints: {
            health: '/health',
            docs: config.urls.docs,
            api: config.urls.api,
          }
        })
      })
    })

    logger.info('🎉 所有路由注册完成')
  } catch (error) {
    logger.error('❌ 路由注册失败', { error })
    throw error
  }
}
