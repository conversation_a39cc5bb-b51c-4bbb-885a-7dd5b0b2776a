import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { prisma } from '../utils/database'
import { authLogger } from '../utils/logger'
import { config } from '../config'

/**
 * 用户认证路由
 * 包括注册、登录、登出、刷新令牌等功能
 */

// 请求验证模式
const registerSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(8, '密码至少8位').max(100, '密码最多100位'),
  firstName: z.string().min(1, '名字不能为空').max(50, '名字最多50位').optional(),
  lastName: z.string().min(1, '姓氏不能为空').max(50, '姓氏最多50位').optional(),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确').optional(),
})

const loginSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(1, '密码不能为空'),
})

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, '刷新令牌不能为空'),
})

const resetPasswordSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, '当前密码不能为空'),
  newPassword: z.string().min(8, '新密码至少8位').max(100, '新密码最多100位'),
})

export default async function authRoutes(fastify: FastifyInstance) {
  // 用户注册
  fastify.post('/register', {
    schema: {
      tags: ['auth'],
      summary: '用户注册',
      description: '创建新用户账户',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email', description: '邮箱地址' },
          password: { type: 'string', minLength: 8, description: '密码' },
          firstName: { type: 'string', description: '名字' },
          lastName: { type: 'string', description: '姓氏' },
          phone: { type: 'string', description: '手机号' },
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    createdAt: { type: 'string' },
                  }
                },
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' },
              }
            },
            message: { type: 'string' },
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // 验证请求数据
      const validatedData = registerSchema.parse(request.body)
      
      authLogger.info('用户注册请求', { email: validatedData.email })

      // 检查邮箱是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email: validatedData.email }
      })

      if (existingUser) {
        authLogger.warn('注册失败：邮箱已存在', { email: validatedData.email })
        return reply.code(409).send({
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: '该邮箱已被注册',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(validatedData.password, config.security.bcryptRounds)

      // 创建用户
      const user = await prisma.user.create({
        data: {
          email: validatedData.email,
          password: hashedPassword,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          phone: validatedData.phone,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          status: true,
          createdAt: true,
        }
      })

      // 生成JWT令牌
      const accessToken = fastify.jwt.sign(
        { 
          userId: user.id, 
          email: user.email,
          role: 'user' 
        },
        { expiresIn: config.jwt.expiresIn }
      )

      const refreshToken = fastify.jwt.sign(
        { 
          userId: user.id, 
          type: 'refresh' 
        },
        { expiresIn: config.jwt.refreshExpiresIn }
      )

      // 保存刷新令牌到数据库
      await prisma.userSession.create({
        data: {
          userId: user.id,
          token: accessToken,
          refreshToken: refreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
        }
      })

      // 设置HTTP-only Cookie
      reply.setCookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: config.isProduction,
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
      })

      authLogger.info('用户注册成功', { userId: user.id, email: user.email })

      return reply.code(201).send({
        success: true,
        data: {
          user,
          accessToken,
          refreshToken,
        },
        message: '注册成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      authLogger.error('用户注册失败', { error })
      
      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 用户登录
  fastify.post('/login', {
    schema: {
      tags: ['auth'],
      summary: '用户登录',
      description: '用户账户登录',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email', description: '邮箱地址' },
          password: { type: 'string', description: '密码' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const validatedData = loginSchema.parse(request.body)
      
      authLogger.info('用户登录请求', { email: validatedData.email })

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { email: validatedData.email },
        select: {
          id: true,
          email: true,
          password: true,
          firstName: true,
          lastName: true,
          phone: true,
          status: true,
          lastLoginAt: true,
        }
      })

      if (!user) {
        authLogger.warn('登录失败：用户不存在', { email: validatedData.email })
        return reply.code(401).send({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: '邮箱或密码错误',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 检查用户状态
      if (user.status !== 'ACTIVE') {
        authLogger.warn('登录失败：用户账户被禁用', { userId: user.id, status: user.status })
        return reply.code(403).send({
          success: false,
          error: {
            code: 'ACCOUNT_DISABLED',
            message: '账户已被禁用，请联系管理员',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(validatedData.password, user.password)
      if (!isPasswordValid) {
        authLogger.warn('登录失败：密码错误', { email: validatedData.email })
        return reply.code(401).send({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: '邮箱或密码错误',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 生成JWT令牌
      const accessToken = fastify.jwt.sign(
        { 
          userId: user.id, 
          email: user.email,
          role: 'user' 
        },
        { expiresIn: config.jwt.expiresIn }
      )

      const refreshToken = fastify.jwt.sign(
        { 
          userId: user.id, 
          type: 'refresh' 
        },
        { expiresIn: config.jwt.refreshExpiresIn }
      )

      // 保存会话信息
      await prisma.userSession.create({
        data: {
          userId: user.id,
          token: accessToken,
          refreshToken: refreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        }
      })

      // 更新最后登录时间
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      })

      // 设置Cookie
      reply.setCookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: config.isProduction,
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60 * 1000,
      })

      authLogger.info('用户登录成功', { userId: user.id, email: user.email })

      // 返回用户信息（不包含密码）
      const { password, ...userWithoutPassword } = user

      return reply.send({
        success: true,
        data: {
          user: userWithoutPassword,
          accessToken,
          refreshToken,
        },
        message: '登录成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      authLogger.error('用户登录失败', { error })
      
      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 用户登出
  fastify.post('/logout', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['auth'],
      summary: '用户登出',
      description: '用户账户登出',
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id

      if (userId) {
        // 删除用户的所有会话
        await prisma.userSession.deleteMany({
          where: { userId }
        })

        authLogger.info('用户登出成功', { userId })
      }

      // 清除Cookie
      reply.clearCookie('refreshToken')

      return reply.send({
        success: true,
        message: '登出成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      authLogger.error('用户登出失败', { error })
      throw error
    }
  })

  // 刷新令牌
  fastify.post('/refresh', {
    schema: {
      tags: ['auth'],
      summary: '刷新访问令牌',
      description: '使用刷新令牌获取新的访问令牌',
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // 从Cookie或请求体获取刷新令牌
      const refreshToken = request.cookies.refreshToken || 
        (request.body as any)?.refreshToken

      if (!refreshToken) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'MISSING_REFRESH_TOKEN',
            message: '缺少刷新令牌',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 验证刷新令牌
      const decoded = fastify.jwt.verify(refreshToken) as any

      if (decoded.type !== 'refresh') {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'INVALID_TOKEN_TYPE',
            message: '令牌类型错误',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 查找用户会话
      const session = await prisma.userSession.findFirst({
        where: {
          userId: decoded.userId,
          refreshToken: refreshToken,
          expiresAt: {
            gt: new Date()
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              status: true,
            }
          }
        }
      })

      if (!session || session.user.status !== 'ACTIVE') {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'INVALID_REFRESH_TOKEN',
            message: '刷新令牌无效或已过期',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 生成新的访问令牌
      const newAccessToken = fastify.jwt.sign(
        { 
          userId: session.user.id, 
          email: session.user.email,
          role: 'user' 
        },
        { expiresIn: config.jwt.expiresIn }
      )

      // 更新会话记录
      await prisma.userSession.update({
        where: { id: session.id },
        data: { 
          token: newAccessToken,
          updatedAt: new Date()
        }
      })

      authLogger.info('令牌刷新成功', { userId: session.user.id })

      return reply.send({
        success: true,
        data: {
          accessToken: newAccessToken,
        },
        message: '令牌刷新成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      authLogger.error('令牌刷新失败', { error })
      
      return reply.code(401).send({
        success: false,
        error: {
          code: 'TOKEN_REFRESH_FAILED',
          message: '令牌刷新失败',
        },
        timestamp: new Date().toISOString(),
      })
    }
  })
}
