// 用户认证路由
// 定义认证相关的API路由和中间件

import { FastifyInstance } from 'fastify'
import { AuthController } from '../controllers/auth.controller'
import { jwtAuthMiddleware, optionalAuthMiddleware } from '../middleware/auth'

/**
 * 注册认证路由
 */
export async function authRoutes(fastify: FastifyInstance) {
  // 路由前缀
  const prefix = '/auth'

  // 用户注册
  fastify.post(`${prefix}/register`, {
    schema: {
      tags: ['auth'],
      summary: '用户注册',
      description: '创建新用户账户',
      body: {
        type: 'object',
        required: ['email', 'password', 'firstName', 'lastName'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          },
          password: {
            type: 'string',
            minLength: 8,
            maxLength: 128,
            description: '密码（至少8位，包含大小写字母和数字）'
          },
          firstName: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
            description: '名字'
          },
          lastName: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
            description: '姓氏'
          },
          phone: {
            type: 'string',
            pattern: '^1[3-9]\\d{9}$',
            description: '手机号（可选）'
          }
        }
      },
      response: {
        201: {
          description: '注册成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    role: { type: 'string' },
                    emailVerified: { type: 'boolean' }
                  }
                },
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        409: { $ref: '#/components/responses/Conflict' }
      }
    }
  }, AuthController.register)

  // 用户登录
  fastify.post(`${prefix}/login`, {
    schema: {
      tags: ['auth'],
      summary: '用户登录',
      description: '用户登录获取访问令牌',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          },
          password: {
            type: 'string',
            description: '密码'
          },
          rememberMe: {
            type: 'boolean',
            description: '记住登录状态',
            default: false
          }
        }
      },
      response: {
        200: {
          description: '登录成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    role: { type: 'string' },
                    emailVerified: { type: 'boolean' }
                  }
                },
                tokens: {
                  type: 'object',
                  properties: {
                    accessToken: { type: 'string' },
                    refreshToken: { type: 'string' }
                  }
                },
                sessionId: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, AuthController.login)

  // 用户登出
  fastify.post(`${prefix}/logout`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['auth'],
      summary: '用户登出',
      description: '用户登出并销毁会话',
      security: [{ Bearer: [] }],
      response: {
        200: {
          description: '登出成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, AuthController.logout)

  // 刷新令牌
  fastify.post(`${prefix}/refresh`, {
    schema: {
      tags: ['auth'],
      summary: '刷新访问令牌',
      description: '使用刷新令牌获取新的访问令牌',
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: {
            type: 'string',
            description: '刷新令牌'
          }
        }
      },
      response: {
        200: {
          description: '令牌刷新成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, AuthController.refreshToken)

  // 发送密码重置邮件
  fastify.post(`${prefix}/forgot-password`, {
    schema: {
      tags: ['auth'],
      summary: '发送密码重置邮件',
      description: '向用户邮箱发送密码重置链接',
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          }
        }
      },
      response: {
        200: {
          description: '邮件发送成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' }
      }
    }
  }, AuthController.sendPasswordResetEmail)

  // 重置密码
  fastify.post(`${prefix}/reset-password`, {
    schema: {
      tags: ['auth'],
      summary: '重置密码',
      description: '使用重置令牌设置新密码',
      body: {
        type: 'object',
        required: ['token', 'newPassword'],
        properties: {
          token: {
            type: 'string',
            description: '密码重置令牌'
          },
          newPassword: {
            type: 'string',
            minLength: 8,
            maxLength: 128,
            description: '新密码（至少8位，包含大小写字母和数字）'
          }
        }
      },
      response: {
        200: {
          description: '密码重置成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' }
      }
    }
  }, AuthController.resetPassword)

  // 验证邮箱
  fastify.get(`${prefix}/verify-email`, {
    schema: {
      tags: ['auth'],
      summary: '验证邮箱',
      description: '使用验证令牌确认邮箱地址',
      querystring: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            description: '邮箱验证令牌'
          }
        }
      },
      response: {
        200: {
          description: '邮箱验证成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    emailVerified: { type: 'boolean' }
                  }
                }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' }
      }
    }
  }, AuthController.verifyEmail)

  // 重新发送邮箱验证邮件
  fastify.post(`${prefix}/resend-verification`, {
    schema: {
      tags: ['auth'],
      summary: '重新发送邮箱验证邮件',
      description: '重新发送邮箱验证链接',
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          }
        }
      },
      response: {
        200: {
          description: '验证邮件发送成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        404: { $ref: '#/components/responses/NotFound' }
      }
    }
  }, AuthController.resendEmailVerification)

  // 获取当前用户信息
  fastify.get(`${prefix}/me`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['auth'],
      summary: '获取当前用户信息',
      description: '获取当前登录用户的详细信息',
      security: [{ Bearer: [] }],
      response: {
        200: {
          description: '获取成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: { $ref: '#/components/schemas/User' }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, AuthController.getCurrentUser)

  // 修改密码
  fastify.post(`${prefix}/change-password`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['auth'],
      summary: '修改密码',
      description: '修改当前用户的密码',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: {
            type: 'string',
            description: '当前密码'
          },
          newPassword: {
            type: 'string',
            minLength: 8,
            maxLength: 128,
            description: '新密码（至少8位，包含大小写字母和数字）'
          }
        }
      },
      response: {
        200: {
          description: '密码修改成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, AuthController.changePassword)
}
