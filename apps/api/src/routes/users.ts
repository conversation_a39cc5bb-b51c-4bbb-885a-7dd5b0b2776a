import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { prisma } from '../utils/database'
import { logger } from '../utils/logger'
import { config } from '../config'

/**
 * 用户管理路由
 * 包括用户资料管理、密码修改等功能
 */

// 请求验证模式
const updateProfileSchema = z.object({
  firstName: z.string().min(1, '名字不能为空').max(50, '名字最多50位').optional(),
  lastName: z.string().min(1, '姓氏不能为空').max(50, '姓氏最多50位').optional(),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确').optional(),
  timezone: z.string().max(50, '时区最多50位').optional(),
  language: z.string().max(10, '语言代码最多10位').optional(),
})

const updateCompanyProfileSchema = z.object({
  companyName: z.string().min(1, '公司名称不能为空').max(255, '公司名称最多255位').optional(),
  industry: z.string().max(100, '行业最多100位').optional(),
  companySize: z.string().max(50, '公司规模最多50位').optional(),
  websiteUrl: z.string().url('网站地址格式不正确').optional(),
  description: z.string().max(1000, '公司描述最多1000位').optional(),
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, '当前密码不能为空'),
  newPassword: z.string().min(8, '新密码至少8位').max(100, '新密码最多100位'),
})

export default async function userRoutes(fastify: FastifyInstance) {
  // 获取当前用户信息
  fastify.get('/me', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['users'],
      summary: '获取当前用户信息',
      description: '获取当前登录用户的详细信息',
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    phone: { type: 'string' },
                    timezone: { type: 'string' },
                    language: { type: 'string' },
                    status: { type: 'string' },
                    lastLoginAt: { type: 'string' },
                    createdAt: { type: 'string' },
                    profile: { type: 'object' },
                  }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id

      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 获取用户详细信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          timezone: true,
          language: true,
          status: true,
          lastLoginAt: true,
          createdAt: true,
          profile: {
            select: {
              companyName: true,
              industry: true,
              companySize: true,
              websiteUrl: true,
              description: true,
              preferences: true,
            }
          }
        }
      })

      if (!user) {
        return reply.code(404).send({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
          timestamp: new Date().toISOString(),
        })
      }

      logger.info('获取用户信息成功', { userId })

      return reply.send({
        success: true,
        data: { user },
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('获取用户信息失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 更新用户基本信息
  fastify.put('/me', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['users'],
      summary: '更新用户基本信息',
      description: '更新当前用户的基本信息',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          firstName: { type: 'string', description: '名字' },
          lastName: { type: 'string', description: '姓氏' },
          phone: { type: 'string', description: '手机号' },
          timezone: { type: 'string', description: '时区' },
          language: { type: 'string', description: '语言' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      const validatedData = updateProfileSchema.parse(request.body)

      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 更新用户信息
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          timezone: true,
          language: true,
          updatedAt: true,
        }
      })

      logger.info('用户信息更新成功', { userId, updatedFields: Object.keys(validatedData) })

      return reply.send({
        success: true,
        data: { user: updatedUser },
        message: '用户信息更新成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('用户信息更新失败', { error, userId: request.user?.id })
      
      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 更新用户公司信息
  fastify.put('/me/profile', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['users'],
      summary: '更新用户公司信息',
      description: '更新当前用户的公司相关信息',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          companyName: { type: 'string', description: '公司名称' },
          industry: { type: 'string', description: '行业' },
          companySize: { type: 'string', description: '公司规模' },
          websiteUrl: { type: 'string', description: '网站地址' },
          description: { type: 'string', description: '公司描述' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      const validatedData = updateCompanyProfileSchema.parse(request.body)

      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 使用upsert操作，如果profile不存在则创建，存在则更新
      const profile = await prisma.userProfile.upsert({
        where: { userId },
        update: {
          ...validatedData,
          updatedAt: new Date(),
        },
        create: {
          userId,
          ...validatedData,
        },
        select: {
          companyName: true,
          industry: true,
          companySize: true,
          websiteUrl: true,
          description: true,
          updatedAt: true,
        }
      })

      logger.info('用户公司信息更新成功', { userId, updatedFields: Object.keys(validatedData) })

      return reply.send({
        success: true,
        data: { profile },
        message: '公司信息更新成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('用户公司信息更新失败', { error, userId: request.user?.id })
      
      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 修改密码
  fastify.put('/me/password', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['users'],
      summary: '修改密码',
      description: '修改当前用户的登录密码',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: { type: 'string', description: '当前密码' },
          newPassword: { type: 'string', minLength: 8, description: '新密码' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      const validatedData = changePasswordSchema.parse(request.body)

      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 获取用户当前密码
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { password: true }
      })

      if (!user) {
        return reply.code(404).send({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(validatedData.currentPassword, user.password)
      if (!isCurrentPasswordValid) {
        logger.warn('密码修改失败：当前密码错误', { userId })
        return reply.code(400).send({
          success: false,
          error: {
            code: 'INVALID_CURRENT_PASSWORD',
            message: '当前密码错误',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 检查新密码是否与当前密码相同
      const isSamePassword = await bcrypt.compare(validatedData.newPassword, user.password)
      if (isSamePassword) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'SAME_PASSWORD',
            message: '新密码不能与当前密码相同',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 加密新密码
      const hashedNewPassword = await bcrypt.hash(validatedData.newPassword, config.security.bcryptRounds)

      // 更新密码
      await prisma.user.update({
        where: { id: userId },
        data: {
          password: hashedNewPassword,
          updatedAt: new Date(),
        }
      })

      // 删除用户的所有会话，强制重新登录
      await prisma.userSession.deleteMany({
        where: { userId }
      })

      logger.info('密码修改成功', { userId })

      return reply.send({
        success: true,
        message: '密码修改成功，请重新登录',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('密码修改失败', { error, userId: request.user?.id })
      
      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 删除用户账户
  fastify.delete('/me', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['users'],
      summary: '删除用户账户',
      description: '删除当前用户账户（软删除）',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id

      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 软删除用户（更新状态为DELETED）
      await prisma.user.update({
        where: { id: userId },
        data: {
          status: 'DELETED',
          updatedAt: new Date(),
        }
      })

      // 删除用户的所有会话
      await prisma.userSession.deleteMany({
        where: { userId }
      })

      logger.info('用户账户删除成功', { userId })

      return reply.send({
        success: true,
        message: '账户删除成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('用户账户删除失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 获取用户会话列表
  fastify.get('/me/sessions', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['users'],
      summary: '获取用户会话列表',
      description: '获取当前用户的所有活跃会话',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id

      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 获取用户的活跃会话
      const sessions = await prisma.userSession.findMany({
        where: {
          userId,
          expiresAt: {
            gt: new Date()
          }
        },
        select: {
          id: true,
          createdAt: true,
          updatedAt: true,
          expiresAt: true,
        },
        orderBy: {
          updatedAt: 'desc'
        }
      })

      logger.info('获取用户会话列表成功', { userId, sessionCount: sessions.length })

      return reply.send({
        success: true,
        data: { sessions },
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('获取用户会话列表失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 删除指定会话
  fastify.delete('/me/sessions/:sessionId', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['users'],
      summary: '删除指定会话',
      description: '删除用户的指定会话',
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['sessionId'],
        properties: {
          sessionId: { type: 'string', description: '会话ID' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      const { sessionId } = request.params as { sessionId: string }

      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未授权访问',
          },
          timestamp: new Date().toISOString(),
        })
      }

      // 删除指定会话
      const deletedSession = await prisma.userSession.deleteMany({
        where: {
          id: sessionId,
          userId: userId
        }
      })

      if (deletedSession.count === 0) {
        return reply.code(404).send({
          success: false,
          error: {
            code: 'SESSION_NOT_FOUND',
            message: '会话不存在',
          },
          timestamp: new Date().toISOString(),
        })
      }

      logger.info('删除用户会话成功', { userId, sessionId })

      return reply.send({
        success: true,
        message: '会话删除成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('删除用户会话失败', { error, userId: request.user?.id })
      throw error
    }
  })
}
