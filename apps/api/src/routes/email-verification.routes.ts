// 邮箱验证路由
// 定义邮箱验证相关的API路由和中间件

import { FastifyInstance } from 'fastify'
import { EmailVerificationController } from '../controllers/email-verification.controller'
import { optionalAuthMiddleware } from '../middleware/auth'

/**
 * 注册邮箱验证路由
 */
export async function emailVerificationRoutes(fastify: FastifyInstance) {
  // 路由前缀
  const prefix = '/email-verification'

  // 发送邮箱验证码
  fastify.post(`${prefix}/send-code`, {
    preHandler: [optionalAuthMiddleware],
    schema: {
      tags: ['email-verification'],
      summary: '发送邮箱验证码',
      description: '向指定邮箱发送6位数字验证码',
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          }
        }
      },
      response: {
        200: {
          description: '验证码发送成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                expiresAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        429: { $ref: '#/components/responses/TooManyRequests' }
      }
    }
  }, EmailVerificationController.sendVerificationCode)

  // 验证邮箱验证码
  fastify.post(`${prefix}/verify-code`, {
    preHandler: [optionalAuthMiddleware],
    schema: {
      tags: ['email-verification'],
      summary: '验证邮箱验证码',
      description: '使用6位数字验证码验证邮箱',
      body: {
        type: 'object',
        required: ['email', 'code'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          },
          code: {
            type: 'string',
            pattern: '^\\d{6}$',
            description: '6位数字验证码'
          }
        }
      },
      response: {
        200: {
          description: '验证成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                verified: { type: 'boolean' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, EmailVerificationController.verifyCode)

  // 通过令牌验证邮箱
  fastify.get(`${prefix}/verify-token`, {
    schema: {
      tags: ['email-verification'],
      summary: '通过令牌验证邮箱',
      description: '使用邮件中的验证令牌验证邮箱地址',
      querystring: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            description: '邮箱验证令牌'
          }
        }
      },
      response: {
        200: {
          description: '验证成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    emailVerified: { type: 'boolean' }
                  }
                }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' }
      }
    }
  }, EmailVerificationController.verifyEmailByToken)

  // 重新发送邮箱验证
  fastify.post(`${prefix}/resend`, {
    schema: {
      tags: ['email-verification'],
      summary: '重新发送邮箱验证',
      description: '重新发送邮箱验证链接',
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          }
        }
      },
      response: {
        200: {
          description: '验证邮件发送成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' }
      }
    }
  }, EmailVerificationController.resendEmailVerification)

  // 检查邮箱验证状态
  fastify.get(`${prefix}/status`, {
    schema: {
      tags: ['email-verification'],
      summary: '检查邮箱验证状态',
      description: '检查指定邮箱的验证状态',
      querystring: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          }
        }
      },
      response: {
        200: {
          description: '查询成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                email: { type: 'string' },
                verified: { type: 'boolean' },
                verifiedAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        404: { $ref: '#/components/responses/NotFound' }
      }
    }
  }, EmailVerificationController.checkVerificationStatus)
}
