// 用户管理路由
// 定义用户资料管理相关的API路由和中间件

import { FastifyInstance } from 'fastify'
import { UserController } from '../controllers/user.controller'
import { jwtAuthMiddleware, requireRole } from '../middleware/auth'

/**
 * 注册用户管理路由
 */
export async function userRoutes(fastify: FastifyInstance) {
  // 路由前缀
  const prefix = '/users'

  // 获取当前用户资料
  fastify.get(`${prefix}/profile`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['users'],
      summary: '获取当前用户资料',
      description: '获取当前登录用户的详细资料信息',
      security: [{ Bearer: [] }],
      response: {
        200: {
          description: '获取成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    fullName: { type: 'string' },
                    avatar: { type: 'string' },
                    phone: { type: 'string' },
                    bio: { type: 'string' },
                    company: { type: 'string' },
                    website: { type: 'string' },
                    role: { type: 'string' },
                    status: { type: 'string' },
                    emailVerified: { type: 'boolean' },
                    timezone: { type: 'string' },
                    language: { type: 'string' },
                    lastLoginAt: { type: 'string', format: 'date-time' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                },
                stats: {
                  type: 'object',
                  properties: {
                    totalCampaigns: { type: 'integer' },
                    activeCampaigns: { type: 'integer' },
                    totalAIGenerations: { type: 'integer' },
                    totalSpent: { type: 'number' },
                    joinedDays: { type: 'integer' },
                    lastLoginDays: { type: 'integer' }
                  }
                },
                preferences: {
                  type: 'object',
                  properties: {
                    emailNotifications: { type: 'boolean' },
                    smsNotifications: { type: 'boolean' },
                    marketingEmails: { type: 'boolean' },
                    theme: { type: 'string', enum: ['light', 'dark', 'auto'] },
                    language: { type: 'string' },
                    timezone: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, UserController.getProfile)

  // 更新用户资料
  fastify.put(`${prefix}/profile`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['users'],
      summary: '更新用户资料',
      description: '更新当前用户的资料信息',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          firstName: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
            description: '名字'
          },
          lastName: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
            description: '姓氏'
          },
          phone: {
            type: 'string',
            pattern: '^1[3-9]\\d{9}$',
            description: '手机号'
          },
          avatar: {
            type: 'string',
            format: 'uri',
            maxLength: 2048,
            description: '头像URL'
          },
          bio: {
            type: 'string',
            maxLength: 500,
            description: '个人简介'
          },
          company: {
            type: 'string',
            maxLength: 100,
            description: '公司名称'
          },
          website: {
            type: 'string',
            format: 'uri',
            maxLength: 2048,
            description: '个人网站'
          },
          timezone: {
            type: 'string',
            maxLength: 50,
            description: '时区'
          },
          language: {
            type: 'string',
            maxLength: 10,
            description: '语言'
          }
        }
      },
      response: {
        200: {
          description: '更新成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    fullName: { type: 'string' },
                    avatar: { type: 'string' },
                    phone: { type: 'string' },
                    bio: { type: 'string' },
                    company: { type: 'string' },
                    website: { type: 'string' },
                    timezone: { type: 'string' },
                    language: { type: 'string' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                },
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, UserController.updateProfile)

  // 获取用户偏好设置
  fastify.get(`${prefix}/preferences`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['users'],
      summary: '获取用户偏好设置',
      description: '获取当前用户的偏好设置',
      security: [{ Bearer: [] }],
      response: {
        200: {
          description: '获取成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                preferences: {
                  type: 'object',
                  properties: {
                    emailNotifications: { type: 'boolean' },
                    smsNotifications: { type: 'boolean' },
                    marketingEmails: { type: 'boolean' },
                    theme: { type: 'string', enum: ['light', 'dark', 'auto'] },
                    language: { type: 'string' },
                    timezone: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, UserController.getPreferences)

  // 更新用户偏好设置
  fastify.put(`${prefix}/preferences`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['users'],
      summary: '更新用户偏好设置',
      description: '更新当前用户的偏好设置',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          emailNotifications: {
            type: 'boolean',
            description: '邮件通知'
          },
          smsNotifications: {
            type: 'boolean',
            description: '短信通知'
          },
          marketingEmails: {
            type: 'boolean',
            description: '营销邮件'
          },
          theme: {
            type: 'string',
            enum: ['light', 'dark', 'auto'],
            description: '主题'
          },
          language: {
            type: 'string',
            maxLength: 10,
            description: '语言'
          },
          timezone: {
            type: 'string',
            maxLength: 50,
            description: '时区'
          }
        }
      },
      response: {
        200: {
          description: '更新成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                preferences: {
                  type: 'object',
                  properties: {
                    emailNotifications: { type: 'boolean' },
                    smsNotifications: { type: 'boolean' },
                    marketingEmails: { type: 'boolean' },
                    theme: { type: 'string' },
                    language: { type: 'string' },
                    timezone: { type: 'string' }
                  }
                },
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, UserController.updatePreferences)

  // 获取用户统计信息
  fastify.get(`${prefix}/stats`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['users'],
      summary: '获取用户统计信息',
      description: '获取当前用户的统计信息',
      security: [{ Bearer: [] }],
      response: {
        200: {
          description: '获取成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                stats: {
                  type: 'object',
                  properties: {
                    totalCampaigns: { type: 'integer' },
                    activeCampaigns: { type: 'integer' },
                    totalAIGenerations: { type: 'integer' },
                    totalSpent: { type: 'number' },
                    joinedDays: { type: 'integer' },
                    lastLoginDays: { type: 'integer' }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, UserController.getStats)

  // 删除用户账户
  fastify.delete(`${prefix}/account`, {
    preHandler: [jwtAuthMiddleware],
    schema: {
      tags: ['users'],
      summary: '删除用户账户',
      description: '删除当前用户账户（软删除）',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['password'],
        properties: {
          password: {
            type: 'string',
            description: '当前密码确认'
          }
        }
      },
      response: {
        200: {
          description: '删除成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' }
      }
    }
  }, UserController.deleteAccount)

  // 管理员功能 - 获取用户列表
  fastify.get(`${prefix}`, {
    preHandler: [jwtAuthMiddleware, requireRole('ADMIN', 'SUPER_ADMIN')],
    schema: {
      tags: ['users'],
      summary: '获取用户列表（管理员）',
      description: '获取系统中所有用户的列表（管理员功能）',
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          search: { type: 'string', description: '搜索关键词' },
          role: { type: 'string', enum: ['USER', 'ADMIN', 'SUPER_ADMIN'] },
          status: { type: 'string', enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED'] },
          sortBy: { type: 'string', enum: ['createdAt', 'updatedAt', 'lastLoginAt', 'email', 'firstName'] },
          sortOrder: { type: 'string', enum: ['asc', 'desc'] }
        }
      },
      response: {
        200: {
          description: '获取成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  email: { type: 'string' },
                  firstName: { type: 'string' },
                  lastName: { type: 'string' },
                  fullName: { type: 'string' },
                  avatar: { type: 'string' },
                  role: { type: 'string' },
                  status: { type: 'string' },
                  emailVerified: { type: 'boolean' },
                  lastLoginAt: { type: 'string', format: 'date-time' },
                  createdAt: { type: 'string', format: 'date-time' }
                }
              }
            },
            meta: {
              type: 'object',
              properties: {
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'integer' },
                    limit: { type: 'integer' },
                    total: { type: 'integer' },
                    totalPages: { type: 'integer' }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        403: { $ref: '#/components/responses/Forbidden' }
      }
    }
  }, UserController.getUserList)

  // 管理员功能 - 获取指定用户资料
  fastify.get(`${prefix}/:userId`, {
    preHandler: [jwtAuthMiddleware, requireRole('ADMIN', 'SUPER_ADMIN')],
    schema: {
      tags: ['users'],
      summary: '获取指定用户资料（管理员）',
      description: '获取指定用户的详细资料（管理员功能）',
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: {
            type: 'string',
            format: 'uuid',
            description: '用户ID'
          }
        }
      },
      response: {
        200: {
          description: '获取成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: { $ref: '#/components/schemas/User' },
                stats: {
                  type: 'object',
                  properties: {
                    totalCampaigns: { type: 'integer' },
                    activeCampaigns: { type: 'integer' },
                    totalAIGenerations: { type: 'integer' },
                    totalSpent: { type: 'number' },
                    joinedDays: { type: 'integer' },
                    lastLoginDays: { type: 'integer' }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        403: { $ref: '#/components/responses/Forbidden' },
        404: { $ref: '#/components/responses/NotFound' }
      }
    }
  }, UserController.getUserById)

  // 管理员功能 - 更新用户状态
  fastify.patch(`${prefix}/:userId/status`, {
    preHandler: [jwtAuthMiddleware, requireRole('ADMIN', 'SUPER_ADMIN')],
    schema: {
      tags: ['users'],
      summary: '更新用户状态（管理员）',
      description: '更新指定用户的状态（管理员功能）',
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: {
            type: 'string',
            format: 'uuid',
            description: '用户ID'
          }
        }
      },
      body: {
        type: 'object',
        required: ['status'],
        properties: {
          status: {
            type: 'string',
            enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED'],
            description: '用户状态'
          },
          reason: {
            type: 'string',
            maxLength: 500,
            description: '操作原因'
          }
        }
      },
      response: {
        200: {
          description: '更新成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    status: { type: 'string' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                },
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' },
        403: { $ref: '#/components/responses/Forbidden' },
        404: { $ref: '#/components/responses/NotFound' }
      }
    }
  }, UserController.updateUserStatus)

  // 超级管理员功能 - 更新用户角色
  fastify.patch(`${prefix}/:userId/role`, {
    preHandler: [jwtAuthMiddleware, requireRole('SUPER_ADMIN')],
    schema: {
      tags: ['users'],
      summary: '更新用户角色（超级管理员）',
      description: '更新指定用户的角色（超级管理员功能）',
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: {
            type: 'string',
            format: 'uuid',
            description: '用户ID'
          }
        }
      },
      body: {
        type: 'object',
        required: ['role'],
        properties: {
          role: {
            type: 'string',
            enum: ['USER', 'ADMIN', 'SUPER_ADMIN'],
            description: '用户角色'
          },
          reason: {
            type: 'string',
            maxLength: 500,
            description: '操作原因'
          }
        }
      },
      response: {
        200: {
          description: '更新成功',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    role: { type: 'string' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                },
                message: { type: 'string' }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' },
        403: { $ref: '#/components/responses/Forbidden' },
        404: { $ref: '#/components/responses/NotFound' }
      }
    }
  }, UserController.updateUserRole)
}
