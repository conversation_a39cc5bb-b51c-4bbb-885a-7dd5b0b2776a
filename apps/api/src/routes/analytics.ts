import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import { analyticsLogger } from '../utils/logger'
import { analyticsService } from '../services/analytics'

/**
 * 数据分析路由
 * 包括数据统计、报告生成等功能
 */

// 请求验证模式
const reportGenerationSchema = z.object({
  type: z.enum(['WEEKLY', 'MONTHLY', 'QUARTERLY']).default('WEEKLY'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  includeAI: z.boolean().default(true),
  includeCampaigns: z.boolean().default(true),
})

export default async function analyticsRoutes(fastify: FastifyInstance) {
  // 获取仪表板数据
  fastify.get('/dashboard', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['analytics'],
      summary: '获取仪表板数据',
      description: '获取用户仪表板的统计数据',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      analyticsLogger.info('获取仪表板数据', { userId })

      // 调用分析服务获取仪表板数据
      const dashboardData = await analyticsService.getDashboardData(userId)

      return reply.send({
        success: true,
        data: dashboardData,
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      analyticsLogger.error('获取仪表板数据失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 生成报告
  fastify.post('/reports', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['analytics'],
      summary: '生成分析报告',
      description: '生成指定时间范围的分析报告',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          type: { type: 'string', enum: ['WEEKLY', 'MONTHLY', 'QUARTERLY'], description: '报告类型' },
          startDate: { type: 'string', format: 'date-time', description: '开始日期' },
          endDate: { type: 'string', format: 'date-time', description: '结束日期' },
          includeAI: { type: 'boolean', description: '包含AI使用数据' },
          includeCampaigns: { type: 'boolean', description: '包含营销活动数据' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const validatedData = reportGenerationSchema.parse(request.body)

      analyticsLogger.info('生成分析报告请求', { userId, options: validatedData })

      // 调用分析服务生成报告
      const report = await analyticsService.generateReport(userId, {
        type: validatedData.type,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : undefined,
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : undefined,
        includeAI: validatedData.includeAI,
        includeCampaigns: validatedData.includeCampaigns,
      })

      return reply.send({
        success: true,
        data: { report },
        message: '报告生成成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      analyticsLogger.error('生成分析报告失败', { error, userId: request.user?.id })

      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 获取用户画像
  fastify.get('/profile', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['analytics'],
      summary: '获取用户画像',
      description: '获取用户行为分析和画像数据',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      analyticsLogger.info('获取用户画像', { userId })

      // 调用分析服务获取用户画像
      const profile = await analyticsService.getUserProfile(userId)

      return reply.send({
        success: true,
        data: { profile },
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      analyticsLogger.error('获取用户画像失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 获取实时数据
  fastify.get('/realtime', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['analytics'],
      summary: '获取实时数据',
      description: '获取实时的营销活动和AI使用数据',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      analyticsLogger.info('获取实时数据', { userId })

      // TODO: 实现实时数据获取
      const realtimeData = {
        activeCampaigns: 3,
        currentClicks: 156,
        todayConversions: 12,
        onlineUsers: 45,
        aiGenerationsToday: 8,
        lastUpdated: new Date().toISOString(),
      }

      return reply.send({
        success: true,
        data: realtimeData,
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      analyticsLogger.error('获取实时数据失败', { error, userId: request.user?.id })
      throw error
    }
  })
}
