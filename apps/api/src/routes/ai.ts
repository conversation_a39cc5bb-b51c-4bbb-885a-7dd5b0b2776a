import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import { aiLogger } from '../utils/logger'
import { openaiService } from '../services/openai'

/**
 * AI服务路由
 * 包括内容生成、图像生成等AI功能
 * 这里先创建基础结构，后续会实现具体功能
 */

// 请求验证模式
const textGenerationSchema = z.object({
  prompt: z.string().min(1, '提示词不能为空').max(2000, '提示词最多2000字符'),
  type: z.enum(['marketing_copy', 'social_media', 'email', 'blog']).default('marketing_copy'),
  tone: z.enum(['professional', 'casual', 'friendly', 'persuasive']).default('professional'),
  length: z.enum(['short', 'medium', 'long']).default('medium'),
})

const imageGenerationSchema = z.object({
  prompt: z.string().min(1, '提示词不能为空').max(1000, '提示词最多1000字符'),
  style: z.enum(['realistic', 'cartoon', 'abstract', 'minimalist']).default('realistic'),
  size: z.enum(['256x256', '512x512', '1024x1024']).default('512x512'),
})

const contentOptimizationSchema = z.object({
  content: z.string().min(1, '内容不能为空').max(5000, '内容最多5000字符'),
  type: z.enum(['grammar', 'seo', 'engagement', 'clarity']).default('clarity'),
  targetAudience: z.string().max(200, '目标受众描述最多200字符').optional(),
})

export default async function aiRoutes(fastify: FastifyInstance) {
  // 文本内容生成
  fastify.post('/generate/text', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['ai'],
      summary: 'AI文本内容生成',
      description: '使用AI生成营销文案、社交媒体内容等文本',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['prompt'],
        properties: {
          prompt: { type: 'string', description: '生成提示词' },
          type: { type: 'string', enum: ['marketing_copy', 'social_media', 'email', 'blog'], description: '内容类型' },
          tone: { type: 'string', enum: ['professional', 'casual', 'friendly', 'persuasive'], description: '语调风格' },
          length: { type: 'string', enum: ['short', 'medium', 'long'], description: '内容长度' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const userId = request.user?.id
      const validatedData = textGenerationSchema.parse(request.body)

      aiLogger.info('AI文本生成请求', { userId, type: validatedData.type })

      // 调用OpenAI服务生成内容
      const result = await openaiService.generateMarketingCopy(
        validatedData.prompt,
        {
          type: validatedData.type,
          tone: validatedData.tone,
          length: validatedData.length,
          userId,
        }
      )

      return reply.send({
        success: true,
        data: {
          content: result.content,
          metadata: {
            model: result.metadata.model,
            tokensUsed: result.metadata.tokensUsed,
            generationTime: `${result.metadata.generationTime}ms`,
          }
        },
        message: '文本生成成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      aiLogger.error('AI文本生成失败', { error, userId: request.user?.id })
      
      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 图像生成
  fastify.post('/generate/image', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['ai'],
      summary: 'AI图像生成',
      description: '使用AI生成营销图片、社交媒体图片等',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['prompt'],
        properties: {
          prompt: { type: 'string', description: '图像描述提示词' },
          style: { type: 'string', enum: ['realistic', 'cartoon', 'abstract', 'minimalist'], description: '图像风格' },
          size: { type: 'string', enum: ['256x256', '512x512', '1024x1024'], description: '图像尺寸' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const validatedData = imageGenerationSchema.parse(request.body)

      aiLogger.info('AI图像生成请求', { userId, style: validatedData.style })

      // 调用OpenAI服务生成图像
      const result = await openaiService.generateImage(
        validatedData.prompt,
        {
          style: validatedData.style,
          size: validatedData.size,
          userId,
        }
      )

      return reply.send({
        success: true,
        data: {
          imageUrl: result.imageUrl,
          metadata: {
            model: result.metadata.model,
            size: result.metadata.size,
            style: result.metadata.style,
            generationTime: `${result.metadata.generationTime}ms`,
          }
        },
        message: '图像生成成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      aiLogger.error('AI图像生成失败', { error, userId: request.user?.id })
      
      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 获取AI生成历史
  fastify.get('/history', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['ai'],
      summary: '获取AI生成历史',
      description: '获取用户的AI内容生成历史记录',
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          type: { type: 'string', enum: ['TEXT_GENERATION', 'IMAGE_GENERATION'], description: '生成类型' },
          page: { type: 'integer', minimum: 1, default: 1, description: '页码' },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20, description: '每页数量' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const { type, page = 1, limit = 20 } = request.query as any

      aiLogger.info('获取AI生成历史', { userId, type, page, limit })

      // 从数据库查询生成历史
      const history = await openaiService.getGenerationHistory(userId, {
        type,
        page: parseInt(page),
        limit: parseInt(limit),
      })

      return reply.send({
        success: true,
        data: history,
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      aiLogger.error('获取AI生成历史失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 内容优化
  fastify.post('/optimize', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['ai'],
      summary: 'AI内容优化',
      description: '使用AI优化现有内容，提高质量和效果',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['content'],
        properties: {
          content: { type: 'string', description: '需要优化的内容' },
          type: { type: 'string', enum: ['grammar', 'seo', 'engagement', 'clarity'], description: '优化类型' },
          targetAudience: { type: 'string', description: '目标受众' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const validatedData = contentOptimizationSchema.parse(request.body)

      aiLogger.info('AI内容优化请求', { userId, type: validatedData.type })

      // 调用OpenAI服务优化内容
      const result = await openaiService.optimizeContent(
        validatedData.content,
        {
          type: validatedData.type,
          targetAudience: validatedData.targetAudience,
          userId,
        }
      )

      return reply.send({
        success: true,
        data: {
          optimizedContent: result.optimizedContent,
          suggestions: result.suggestions,
          metadata: {
            model: result.metadata.model,
            tokensUsed: result.metadata.tokensUsed,
            generationTime: `${result.metadata.generationTime}ms`,
          }
        },
        message: '内容优化成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      aiLogger.error('AI内容优化失败', { error, userId: request.user?.id })

      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })
}
