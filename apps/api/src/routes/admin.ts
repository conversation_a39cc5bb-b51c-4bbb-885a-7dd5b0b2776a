import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { logger } from '../utils/logger'

/**
 * 系统管理路由
 * 包括用户管理、系统配置等管理功能
 * 这里先创建基础结构，后续会实现具体功能
 */

export default async function adminRoutes(fastify: FastifyInstance) {
  // 获取系统统计信息
  fastify.get('/stats', {
    preHandler: [fastify.authenticate, fastify.requireAdmin],
    schema: {
      tags: ['admin'],
      summary: '获取系统统计信息',
      description: '获取系统的统计数据（仅管理员）',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id

      logger.info('获取系统统计信息', { userId })

      // TODO: 实现系统统计信息查询
      const mockStats = {
        users: {
          total: 0,
          active: 0,
          newThisMonth: 0,
        },
        campaigns: {
          total: 0,
          active: 0,
          completed: 0,
        },
        revenue: {
          total: 0,
          thisMonth: 0,
          lastMonth: 0,
        },
      }

      return reply.send({
        success: true,
        data: mockStats,
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('获取系统统计信息失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 获取用户列表
  fastify.get('/users', {
    preHandler: [fastify.authenticate, fastify.requireAdmin],
    schema: {
      tags: ['admin'],
      summary: '获取用户列表',
      description: '获取系统中的用户列表（仅管理员）',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id

      logger.info('获取用户列表', { userId })

      // TODO: 从数据库查询用户列表
      const mockUsers = {
        items: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
        }
      }

      return reply.send({
        success: true,
        data: mockUsers,
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      logger.error('获取用户列表失败', { error, userId: request.user?.id })
      throw error
    }
  })
}
