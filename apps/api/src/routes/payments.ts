import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import { paymentLogger } from '../utils/logger'
import { stripeService } from '../services/stripe'
import { prisma } from '../utils/database'
import { config } from '../config'

/**
 * 支付订阅路由
 * 包括订阅管理、支付处理等功能
 */

// 请求验证模式
const createCheckoutSessionSchema = z.object({
  priceId: z.string().min(1, '价格ID不能为空'),
  trialPeriodDays: z.number().min(0).max(365).optional(),
  couponId: z.string().optional(),
})

const updateSubscriptionSchema = z.object({
  priceId: z.string().min(1, '价格ID不能为空'),
})

export default async function paymentRoutes(fastify: FastifyInstance) {
  // 获取订阅信息
  fastify.get('/subscription', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['payments'],
      summary: '获取用户订阅信息',
      description: '获取当前用户的订阅状态和详情',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      paymentLogger.info('获取订阅信息', { userId })

      // 从数据库获取用户的订阅信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          subscription: true
        }
      })

      if (!user) {
        return reply.code(404).send({
          success: false,
          error: { code: 'USER_NOT_FOUND', message: '用户不存在' },
          timestamp: new Date().toISOString(),
        })
      }

      let subscriptionData = null

      if (user.subscription?.stripeSubscriptionId) {
        try {
          // 从Stripe获取最新的订阅信息
          const stripeSubscription = await stripeService.getSubscription(
            user.subscription.stripeSubscriptionId
          )

          subscriptionData = {
            id: stripeSubscription.id,
            status: stripeSubscription.status,
            planId: stripeSubscription.items.data[0]?.price.id,
            currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
            currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
            cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
            trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000).toISOString() : null,
          }
        } catch (error) {
          paymentLogger.error('获取Stripe订阅信息失败', { error, userId })
          // 如果Stripe查询失败，使用数据库中的信息
          subscriptionData = {
            id: user.subscription.stripeSubscriptionId,
            status: user.subscription.status,
            planId: user.subscription.planId,
            currentPeriodStart: user.subscription.currentPeriodStart?.toISOString(),
            currentPeriodEnd: user.subscription.currentPeriodEnd?.toISOString(),
          }
        }
      }

      return reply.send({
        success: true,
        data: { subscription: subscriptionData },
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      paymentLogger.error('获取订阅信息失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 创建支付会话
  fastify.post('/create-checkout-session', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['payments'],
      summary: '创建支付会话',
      description: '创建Stripe支付会话',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['priceId'],
        properties: {
          priceId: { type: 'string', description: 'Stripe价格ID' },
          trialPeriodDays: { type: 'number', description: '试用期天数' },
          couponId: { type: 'string', description: '优惠券ID' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const validatedData = createCheckoutSessionSchema.parse(request.body)

      paymentLogger.info('创建支付会话请求', { userId, priceId: validatedData.priceId })

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          email: true,
          firstName: true,
          lastName: true,
          stripeCustomerId: true,
        }
      })

      if (!user) {
        return reply.code(404).send({
          success: false,
          error: { code: 'USER_NOT_FOUND', message: '用户不存在' },
          timestamp: new Date().toISOString(),
        })
      }

      // 如果用户没有Stripe客户ID，创建一个
      let customerId = user.stripeCustomerId
      if (!customerId) {
        const customer = await stripeService.createCustomer(
          user.email,
          `${user.firstName || ''} ${user.lastName || ''}`.trim()
        )
        customerId = customer.id

        // 更新用户的Stripe客户ID
        await prisma.user.update({
          where: { id: userId },
          data: { stripeCustomerId: customerId }
        })
      }

      // 创建支付会话
      const session = await stripeService.createCheckoutSession(
        customerId,
        validatedData.priceId,
        {
          successUrl: `${config.urls.frontend}/dashboard/billing?session_id={CHECKOUT_SESSION_ID}`,
          cancelUrl: `${config.urls.frontend}/dashboard/billing?cancelled=true`,
          trialPeriodDays: validatedData.trialPeriodDays,
          couponId: validatedData.couponId,
          metadata: {
            userId,
            source: 'ai-digital-marketing'
          }
        }
      )

      return reply.send({
        success: true,
        data: {
          sessionId: session.id,
          url: session.url
        },
        message: '支付会话创建成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      paymentLogger.error('创建支付会话失败', { error, userId: request.user?.id })

      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 取消订阅
  fastify.post('/cancel-subscription', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['payments'],
      summary: '取消订阅',
      description: '取消用户的当前订阅',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          immediately: { type: 'boolean', description: '是否立即取消', default: false },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const { immediately = false } = request.body as { immediately?: boolean }

      paymentLogger.info('取消订阅请求', { userId, immediately })

      // 获取用户的订阅信息
      const subscription = await prisma.subscription.findUnique({
        where: { userId },
        select: { stripeSubscriptionId: true }
      })

      if (!subscription?.stripeSubscriptionId) {
        return reply.code(404).send({
          success: false,
          error: { code: 'SUBSCRIPTION_NOT_FOUND', message: '未找到有效订阅' },
          timestamp: new Date().toISOString(),
        })
      }

      // 取消Stripe订阅
      const cancelledSubscription = await stripeService.cancelSubscription(
        subscription.stripeSubscriptionId,
        immediately
      )

      // 更新数据库中的订阅状态
      await prisma.subscription.update({
        where: { userId },
        data: {
          status: immediately ? 'CANCELLED' : 'ACTIVE',
          cancelAtPeriodEnd: !immediately,
          updatedAt: new Date(),
        }
      })

      return reply.send({
        success: true,
        data: {
          subscriptionId: cancelledSubscription.id,
          status: cancelledSubscription.status,
          cancelAtPeriodEnd: cancelledSubscription.cancel_at_period_end,
        },
        message: immediately ? '订阅已立即取消' : '订阅将在当前计费周期结束时取消',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      paymentLogger.error('取消订阅失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // 更新订阅
  fastify.post('/update-subscription', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['payments'],
      summary: '更新订阅',
      description: '更新用户的订阅计划',
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['priceId'],
        properties: {
          priceId: { type: 'string', description: '新的价格ID' },
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      const validatedData = updateSubscriptionSchema.parse(request.body)

      paymentLogger.info('更新订阅请求', { userId, priceId: validatedData.priceId })

      // 获取用户的订阅信息
      const subscription = await prisma.subscription.findUnique({
        where: { userId },
        select: { stripeSubscriptionId: true }
      })

      if (!subscription?.stripeSubscriptionId) {
        return reply.code(404).send({
          success: false,
          error: { code: 'SUBSCRIPTION_NOT_FOUND', message: '未找到有效订阅' },
          timestamp: new Date().toISOString(),
        })
      }

      // 更新Stripe订阅
      const updatedSubscription = await stripeService.updateSubscription(
        subscription.stripeSubscriptionId,
        validatedData.priceId
      )

      // 更新数据库中的订阅信息
      await prisma.subscription.update({
        where: { userId },
        data: {
          planId: validatedData.priceId,
          status: updatedSubscription.status.toUpperCase(),
          updatedAt: new Date(),
        }
      })

      return reply.send({
        success: true,
        data: {
          subscriptionId: updatedSubscription.id,
          status: updatedSubscription.status,
          planId: validatedData.priceId,
        },
        message: '订阅更新成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      paymentLogger.error('更新订阅失败', { error, userId: request.user?.id })

      if (error instanceof z.ZodError) {
        return reply.code(400).send({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: error.errors,
          },
          timestamp: new Date().toISOString(),
        })
      }

      throw error
    }
  })

  // 创建客户门户会话
  fastify.post('/create-portal-session', {
    preHandler: [fastify.authenticate],
    schema: {
      tags: ['payments'],
      summary: '创建客户门户会话',
      description: '创建Stripe客户门户会话，用于管理订阅和账单',
      security: [{ Bearer: [] }],
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.id
      if (!userId) {
        return reply.code(401).send({
          success: false,
          error: { code: 'UNAUTHORIZED', message: '未授权访问' },
          timestamp: new Date().toISOString(),
        })
      }

      paymentLogger.info('创建客户门户会话请求', { userId })

      // 获取用户的Stripe客户ID
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { stripeCustomerId: true }
      })

      if (!user?.stripeCustomerId) {
        return reply.code(404).send({
          success: false,
          error: { code: 'CUSTOMER_NOT_FOUND', message: '未找到客户信息' },
          timestamp: new Date().toISOString(),
        })
      }

      // 创建客户门户会话
      const session = await stripeService.createPortalSession(
        user.stripeCustomerId,
        `${config.urls.frontend}/dashboard/billing`
      )

      return reply.send({
        success: true,
        data: {
          sessionId: session.id,
          url: session.url,
        },
        message: '客户门户会话创建成功',
        timestamp: new Date().toISOString(),
      })

    } catch (error) {
      paymentLogger.error('创建客户门户会话失败', { error, userId: request.user?.id })
      throw error
    }
  })

  // Stripe Webhook处理
  fastify.post('/webhook', {
    config: {
      rawBody: true,
    },
    schema: {
      tags: ['payments'],
      summary: 'Stripe Webhook',
      description: '处理Stripe的Webhook事件',
      hide: true,
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const signature = request.headers['stripe-signature'] as string
      const payload = request.rawBody

      if (!signature || !payload) {
        return reply.code(400).send({
          success: false,
          error: { code: 'MISSING_SIGNATURE_OR_PAYLOAD', message: '缺少签名或请求体' },
          timestamp: new Date().toISOString(),
        })
      }

      // 处理Webhook事件
      await stripeService.handleWebhook(payload, signature)

      return reply.send({ received: true })

    } catch (error) {
      paymentLogger.error('处理Stripe Webhook失败', { error })
      return reply.code(400).send({
        success: false,
        error: { code: 'WEBHOOK_ERROR', message: 'Webhook处理失败' },
        timestamp: new Date().toISOString(),
      })
    }
  })
}
