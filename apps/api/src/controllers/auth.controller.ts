// 用户认证控制器
// 处理认证相关的HTTP请求和响应

import { FastifyRequest, FastifyReply } from 'fastify'
import { AuthService } from '../services/auth.service'
import { ResponseHelper } from '../utils/response'
import { ValidateBody, ValidateQuery, CommonSchemas } from '../utils/validation'
import { logger } from '../utils/logger'
import { prisma } from '../utils/database'
import { emailService } from '../services/email.service'
import { config } from '../config'
import { z } from 'zod'
import bcrypt from 'bcrypt'
import crypto from 'crypto'

/**
 * 用户认证控制器类
 */
export class AuthController {
  /**
   * 用户注册
   */
  @ValidateBody(CommonSchemas.userRegister)
  static async register(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userData = request.body as any
      const result = await AuthService.register(userData)

      logger.info('用户注册API调用成功', {
        email: userData.email,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result, 201)
    } catch (error) {
      logger.error('用户注册API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 用户登录
   */
  @ValidateBody(CommonSchemas.userLogin)
  static async login(request: FastifyRequest, reply: FastifyReply) {
    try {
      const loginData = request.body as any
      
      // 添加请求元数据
      const loginDataWithMeta = {
        ...loginData,
        userAgent: request.headers['user-agent'] || '',
        ip: request.ip
      }

      const result = await AuthService.login(loginDataWithMeta)

      // 设置HTTP-only cookie（可选）
      if (request.body.rememberMe) {
        reply.setCookie('refreshToken', result.tokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 30 * 24 * 60 * 60 * 1000 // 30天
        })
      }

      logger.info('用户登录API调用成功', {
        userId: result.user.id,
        email: result.user.email,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('用户登录API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 用户登出
   */
  static async logout(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const sessionId = (request as any).sessionId

      if (!user || !sessionId) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      const result = await AuthService.logout(sessionId, user.id)

      // 清除cookie
      reply.clearCookie('refreshToken')

      logger.info('用户登出API调用成功', {
        userId: user.id,
        sessionId,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('用户登出API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 刷新令牌
   */
  @ValidateBody(z.object({
    refreshToken: z.string().min(1, '刷新令牌不能为空')
  }))
  static async refreshToken(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { refreshToken } = request.body as any
      const result = await AuthService.refreshToken(refreshToken)

      logger.info('令牌刷新API调用成功', {
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('令牌刷新API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 发送密码重置邮件
   */
  @ValidateBody(z.object({
    email: z.string().email('邮箱格式不正确')
  }))
  static async sendPasswordResetEmail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { email } = request.body as any
      const result = await AuthService.sendPasswordResetEmail(email)

      logger.info('密码重置邮件发送API调用成功', {
        email,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('密码重置邮件发送API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 重置密码
   */
  @ValidateBody(z.object({
    token: z.string().min(1, '重置令牌不能为空'),
    newPassword: z.string()
      .min(8, '密码长度至少8个字符')
      .max(128, '密码长度不能超过128个字符')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字')
  }))
  static async resetPassword(request: FastifyRequest, reply: FastifyReply) {
    try {
      const resetData = request.body as any
      const result = await AuthService.resetPassword(resetData)

      logger.info('密码重置API调用成功', {
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('密码重置API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 验证邮箱
   */
  @ValidateQuery(z.object({
    token: z.string().min(1, '验证令牌不能为空')
  }))
  static async verifyEmail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { token } = request.query as any
      
      // 查找用户
      const user = await prisma.user.findFirst({
        where: {
          emailVerificationToken: token,
          emailVerified: false
        },
        select: {
          id: true,
          email: true,
          firstName: true
        }
      })

      if (!user) {
        return ResponseHelper.error(
          reply,
          'INVALID_TOKEN',
          '验证令牌无效或已过期',
          400
        )
      }

      // 更新用户邮箱验证状态
      await prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerified: true,
          emailVerifiedAt: new Date(),
          emailVerificationToken: null
        }
      })

      // 发送欢迎邮件
      try {
        await emailService.sendWelcomeEmail(user.email, user.firstName)
      } catch (emailError) {
        logger.warn('发送欢迎邮件失败', {
          userId: user.id,
          email: user.email,
          error: emailError instanceof Error ? emailError.message : String(emailError)
        })
      }

      logger.info('邮箱验证API调用成功', {
        userId: user.id,
        email: user.email,
        requestId: request.id
      })

      return ResponseHelper.success(reply, {
        message: '邮箱验证成功',
        user: {
          id: user.id,
          email: user.email,
          emailVerified: true
        }
      })
    } catch (error) {
      logger.error('邮箱验证API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 重新发送邮箱验证邮件
   */
  @ValidateBody(z.object({
    email: z.string().email('邮箱格式不正确')
  }))
  static async resendEmailVerification(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { email } = request.body as any
      
      // 查找用户
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          emailVerified: true,
          emailVerificationToken: true
        }
      })

      if (!user) {
        return ResponseHelper.notFound(reply, '用户不存在')
      }

      if (user.emailVerified) {
        return ResponseHelper.error(
          reply,
          'EMAIL_ALREADY_VERIFIED',
          '邮箱已验证',
          400
        )
      }

      // 生成新的验证令牌
      const newToken = crypto.randomBytes(32).toString('hex')
      
      await prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerificationToken: newToken
        }
      })

      // 发送验证邮件
      await emailService.sendEmailVerification(user.email, newToken)

      logger.info('重新发送邮箱验证邮件API调用成功', {
        userId: user.id,
        email: user.email,
        requestId: request.id
      })

      return ResponseHelper.success(reply, {
        message: '验证邮件已重新发送，请检查您的邮箱'
      })
    } catch (error) {
      logger.error('重新发送邮箱验证邮件API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      // 获取完整的用户信息
      const fullUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          role: true,
          status: true,
          emailVerified: true,
          phone: true,
          timezone: true,
          language: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true
        }
      })

      if (!fullUser) {
        return ResponseHelper.notFound(reply, '用户不存在')
      }

      logger.debug('获取当前用户信息API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, { user: fullUser })
    } catch (error) {
      logger.error('获取当前用户信息API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 修改密码
   */
  @ValidateBody(z.object({
    currentPassword: z.string().min(1, '当前密码不能为空'),
    newPassword: z.string()
      .min(8, '新密码长度至少8个字符')
      .max(128, '新密码长度不能超过128个字符')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '新密码必须包含大小写字母和数字')
  }))
  static async changePassword(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { currentPassword, newPassword } = request.body as any

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      // 获取用户当前密码
      const userWithPassword = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          firstName: true,
          password: true
        }
      })

      if (!userWithPassword) {
        return ResponseHelper.notFound(reply, '用户不存在')
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(
        currentPassword,
        userWithPassword.password
      )

      if (!isCurrentPasswordValid) {
        return ResponseHelper.error(
          reply,
          'INVALID_PASSWORD',
          '当前密码错误',
          400
        )
      }

      // 检查新密码是否与当前密码相同
      const isSamePassword = await bcrypt.compare(newPassword, userWithPassword.password)
      if (isSamePassword) {
        return ResponseHelper.error(
          reply,
          'SAME_PASSWORD',
          '新密码不能与当前密码相同',
          400
        )
      }

      // 加密新密码
      const hashedNewPassword = await bcrypt.hash(newPassword, config.security.bcryptRounds)

      // 更新密码
      await prisma.user.update({
        where: { id: user.id },
        data: {
          password: hashedNewPassword,
          passwordChangedAt: new Date()
        }
      })

      // 发送密码修改通知邮件
      try {
        await emailService.sendPasswordChangeNotification(
          userWithPassword.email,
          userWithPassword.firstName
        )
      } catch (emailError) {
        logger.warn('发送密码修改通知邮件失败', {
          userId: user.id,
          error: emailError instanceof Error ? emailError.message : String(emailError)
        })
      }

      logger.info('修改密码API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, {
        message: '密码修改成功'
      })
    } catch (error) {
      logger.error('修改密码API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }
}
