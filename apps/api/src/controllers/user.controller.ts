// 用户资料管理控制器
// 处理用户资料相关的HTTP请求和响应

import { FastifyRequest, FastifyReply } from 'fastify'
import { UserService } from '../services/user.service'
import { ResponseHelper } from '../utils/response'
import { ValidateBody, ValidateQuery, ValidateParams, PaginationSchema } from '../utils/validation'
import { requireRole } from '../middleware/auth'
import { logger } from '../utils/logger'
import { prisma } from '../utils/database'
import { z } from 'zod'

/**
 * 用户资料管理控制器类
 */
export class UserController {
  /**
   * 获取当前用户资料
   */
  static async getProfile(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      const result = await UserService.getUserProfile(user.id)

      logger.debug('获取用户资料API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('获取用户资料API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 更新用户资料
   */
  @ValidateBody(z.object({
    firstName: z.string().min(1).max(50).optional(),
    lastName: z.string().min(1).max(50).optional(),
    phone: z.string().regex(/^1[3-9]\d{9}$/).optional(),
    avatar: z.string().url().max(2048).optional(),
    bio: z.string().max(500).optional(),
    company: z.string().max(100).optional(),
    website: z.string().url().max(2048).optional(),
    timezone: z.string().max(50).optional(),
    language: z.string().max(10).optional()
  }))
  static async updateProfile(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const updateData = request.body as any

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      const result = await UserService.updateUserProfile(user.id, updateData)

      logger.info('更新用户资料API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('更新用户资料API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 获取用户偏好设置
   */
  static async getPreferences(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      const preferences = await UserService.getUserPreferences(user.id)

      logger.debug('获取用户偏好设置API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, { preferences })
    } catch (error) {
      logger.error('获取用户偏好设置API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 更新用户偏好设置
   */
  @ValidateBody(z.object({
    emailNotifications: z.boolean().optional(),
    smsNotifications: z.boolean().optional(),
    marketingEmails: z.boolean().optional(),
    theme: z.enum(['light', 'dark', 'auto']).optional(),
    language: z.string().max(10).optional(),
    timezone: z.string().max(50).optional()
  }))
  static async updatePreferences(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const preferences = request.body as any

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      const result = await UserService.updateUserPreferences(user.id, preferences)

      logger.info('更新用户偏好设置API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('更新用户偏好设置API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      const stats = await UserService.getUserStats(user.id)

      logger.debug('获取用户统计信息API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, { stats })
    } catch (error) {
      logger.error('获取用户统计信息API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 删除用户账户
   */
  @ValidateBody(z.object({
    password: z.string().min(1, '密码不能为空')
  }))
  static async deleteAccount(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { password } = request.body as any

      if (!user) {
        return ResponseHelper.unauthorized(reply, '未登录')
      }

      const result = await UserService.deleteUserAccount(user.id, password)

      logger.info('删除用户账户API调用成功', {
        userId: user.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('删除用户账户API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 获取指定用户资料（管理员功能）
   */
  @ValidateParams(z.object({
    userId: z.string().uuid('用户ID格式不正确')
  }))
  static async getUserById(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userId } = request.params as any

      const result = await UserService.getUserProfile(userId)

      logger.debug('获取指定用户资料API调用成功', {
        targetUserId: userId,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('获取指定用户资料API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 获取用户列表（管理员功能）
   */
  @ValidateQuery(PaginationSchema.extend({
    search: z.string().optional(),
    role: z.enum(['USER', 'ADMIN', 'SUPER_ADMIN']).optional(),
    status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED']).optional(),
    sortBy: z.enum(['createdAt', 'updatedAt', 'lastLoginAt', 'email', 'firstName']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional()
  }))
  static async getUserList(request: FastifyRequest, reply: FastifyReply) {
    try {
      const query = request.query as any

      const result = await UserService.getUserList(query)

      logger.debug('获取用户列表API调用成功', {
        page: query.page,
        limit: query.limit,
        requestId: request.id
      })

      return ResponseHelper.successWithPagination(
        reply,
        result.users,
        result.pagination
      )
    } catch (error) {
      logger.error('获取用户列表API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 更新用户状态（管理员功能）
   */
  @ValidateParams(z.object({
    userId: z.string().uuid('用户ID格式不正确')
  }))
  @ValidateBody(z.object({
    status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED']),
    reason: z.string().max(500).optional()
  }))
  static async updateUserStatus(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userId } = request.params as any
      const { status, reason } = request.body as any
      const currentUser = (request as any).user

      // 检查用户是否存在
      const targetUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          status: true,
          role: true
        }
      })

      if (!targetUser) {
        return ResponseHelper.notFound(reply, '用户不存在')
      }

      // 防止管理员修改超级管理员状态
      if (targetUser.role === 'SUPER_ADMIN' && currentUser.role !== 'SUPER_ADMIN') {
        return ResponseHelper.forbidden(reply, '无权限修改超级管理员状态')
      }

      // 更新用户状态
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          status,
          updatedAt: new Date()
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          status: true,
          updatedAt: true
        }
      })

      // 记录操作日志
      logger.info('管理员更新用户状态', {
        adminId: currentUser.id,
        targetUserId: userId,
        oldStatus: targetUser.status,
        newStatus: status,
        reason
      })

      return ResponseHelper.success(reply, {
        user: updatedUser,
        message: '用户状态更新成功'
      })
    } catch (error) {
      logger.error('更新用户状态API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 更新用户角色（超级管理员功能）
   */
  @ValidateParams(z.object({
    userId: z.string().uuid('用户ID格式不正确')
  }))
  @ValidateBody(z.object({
    role: z.enum(['USER', 'ADMIN', 'SUPER_ADMIN']),
    reason: z.string().max(500).optional()
  }))
  static async updateUserRole(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { userId } = request.params as any
      const { role, reason } = request.body as any
      const currentUser = (request as any).user

      // 只有超级管理员可以修改角色
      if (currentUser.role !== 'SUPER_ADMIN') {
        return ResponseHelper.forbidden(reply, '只有超级管理员可以修改用户角色')
      }

      // 检查用户是否存在
      const targetUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          firstName: true,
          role: true
        }
      })

      if (!targetUser) {
        return ResponseHelper.notFound(reply, '用户不存在')
      }

      // 防止修改自己的角色
      if (userId === currentUser.id) {
        return ResponseHelper.forbidden(reply, '不能修改自己的角色')
      }

      // 更新用户角色
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          role,
          updatedAt: new Date()
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          updatedAt: true
        }
      })

      // 记录操作日志
      logger.info('超级管理员更新用户角色', {
        adminId: currentUser.id,
        targetUserId: userId,
        oldRole: targetUser.role,
        newRole: role,
        reason
      })

      return ResponseHelper.success(reply, {
        user: updatedUser,
        message: '用户角色更新成功'
      })
    } catch (error) {
      logger.error('更新用户角色API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }
}
