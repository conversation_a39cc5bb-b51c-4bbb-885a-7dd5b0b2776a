// 邮箱验证控制器
// 处理邮箱验证相关的HTTP请求和响应

import { FastifyRequest, FastifyReply } from 'fastify'
import { EmailVerificationService } from '../services/email-verification.service'
import { ResponseHelper } from '../utils/response'
import { ValidateBody, ValidateQuery } from '../utils/validation'
import { logger } from '../utils/logger'
import { z } from 'zod'

/**
 * 邮箱验证控制器类
 */
export class EmailVerificationController {
  /**
   * 发送邮箱验证码
   */
  @ValidateBody(z.object({
    email: z.string().email('邮箱格式不正确')
  }))
  static async sendVerificationCode(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { email } = request.body as any
      const user = (request as any).user

      const result = await EmailVerificationService.sendVerificationCode(
        email,
        user?.id
      )

      logger.info('发送邮箱验证码API调用成功', {
        email,
        userId: user?.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('发送邮箱验证码API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 验证邮箱验证码
   */
  @ValidateBody(z.object({
    email: z.string().email('邮箱格式不正确'),
    code: z.string().length(6, '验证码必须是6位数字').regex(/^\d{6}$/, '验证码只能包含数字')
  }))
  static async verifyCode(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { email, code } = request.body as any
      const user = (request as any).user

      const result = await EmailVerificationService.verifyCode(
        email,
        code,
        user?.id
      )

      logger.info('验证邮箱验证码API调用成功', {
        email,
        userId: user?.id,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('验证邮箱验证码API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 通过令牌验证邮箱
   */
  @ValidateQuery(z.object({
    token: z.string().min(1, '验证令牌不能为空')
  }))
  static async verifyEmailByToken(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { token } = request.query as any

      const result = await EmailVerificationService.verifyEmailByToken(token)

      logger.info('邮箱令牌验证API调用成功', {
        token: token.substring(0, 8) + '...',
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('邮箱令牌验证API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 重新发送邮箱验证
   */
  @ValidateBody(z.object({
    email: z.string().email('邮箱格式不正确')
  }))
  static async resendEmailVerification(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { email } = request.body as any

      const result = await EmailVerificationService.resendEmailVerification(email)

      logger.info('重新发送邮箱验证API调用成功', {
        email,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('重新发送邮箱验证API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }

  /**
   * 检查邮箱验证状态
   */
  @ValidateQuery(z.object({
    email: z.string().email('邮箱格式不正确')
  }))
  static async checkVerificationStatus(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { email } = request.query as any

      const result = await EmailVerificationService.checkVerificationStatus(email)

      logger.debug('检查邮箱验证状态API调用成功', {
        email,
        requestId: request.id
      })

      return ResponseHelper.success(reply, result)
    } catch (error) {
      logger.error('检查邮箱验证状态API调用失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: request.id
      })
      throw error
    }
  }
}
