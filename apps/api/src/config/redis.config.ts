// Redis缓存配置
// 配置Redis连接、缓存策略、键命名规范

import Redis from 'ioredis'
import { logger } from '../utils/logger'

/**
 * Redis配置选项
 */
export interface RedisConfig {
  host: string
  port: number
  password?: string
  db: number
  keyPrefix: string
  retryDelayOnFailover: number
  maxRetriesPerRequest: number
  lazyConnect: boolean
  keepAlive: number
  family: number
  connectTimeout: number
  commandTimeout: number
}

/**
 * 缓存键配置
 */
export const CACHE_KEYS = {
  // 用户相关
  USER_PROFILE: 'user:profile:',
  USER_SUBSCRIPTION: 'user:subscription:',
  USER_PERMISSIONS: 'user:permissions:',
  
  // 会话相关
  SESSION: 'session:',
  REFRESH_TOKEN: 'refresh_token:',
  
  // 营销活动相关
  CAMPAIGN: 'campaign:',
  CAMPAIGN_STATS: 'campaign:stats:',
  USER_CAMPAIGNS: 'user:campaigns:',
  
  // AI生成相关
  AI_GENERATION: 'ai:generation:',
  AI_USAGE_LIMIT: 'ai:usage:',
  AI_GENERATION_QUEUE: 'ai:queue:',
  
  // 邮件营销相关
  EMAIL_TEMPLATE: 'email:template:',
  EMAIL_SEND_LIMIT: 'email:send_limit:',
  EMAIL_BATCH: 'email:batch:',
  
  // 分析数据相关
  ANALYTICS_CACHE: 'analytics:cache:',
  REALTIME_METRICS: 'realtime:metrics:',
  USER_ACTIVITY: 'user:activity:',
  
  // 报告相关
  REPORT_CACHE: 'report:cache:',
  REPORT_TEMPLATE: 'report:template:',
  
  // 系统相关
  RATE_LIMIT: 'rate_limit:',
  API_CACHE: 'api:cache:',
  SYSTEM_CONFIG: 'system:config:',
  
  // 锁相关
  LOCK: 'lock:',
  DISTRIBUTED_LOCK: 'distributed_lock:'
} as const

/**
 * 缓存TTL配置（秒）
 */
export const CACHE_TTL = {
  // 短期缓存（5分钟）
  SHORT: 300,
  
  // 中期缓存（1小时）
  MEDIUM: 3600,
  
  // 长期缓存（24小时）
  LONG: 86400,
  
  // 会话缓存（7天）
  SESSION: 604800,
  
  // 用户数据缓存（30分钟）
  USER_DATA: 1800,
  
  // API响应缓存（10分钟）
  API_RESPONSE: 600,
  
  // 实时数据缓存（1分钟）
  REALTIME: 60,
  
  // 报告缓存（2小时）
  REPORT: 7200,
  
  // 限流窗口（1小时）
  RATE_LIMIT: 3600
} as const

/**
 * 获取Redis配置
 */
export function getRedisConfig(): RedisConfig {
  return {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'ai_marketing:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    family: 4,
    connectTimeout: 10000,
    commandTimeout: 5000
  }
}

/**
 * 创建Redis实例
 */
export function createRedisInstance(config?: Partial<RedisConfig>): Redis {
  const redisConfig = { ...getRedisConfig(), ...config }
  
  const redis = new Redis({
    host: redisConfig.host,
    port: redisConfig.port,
    password: redisConfig.password,
    db: redisConfig.db,
    keyPrefix: redisConfig.keyPrefix,
    retryDelayOnFailover: redisConfig.retryDelayOnFailover,
    maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
    lazyConnect: redisConfig.lazyConnect,
    keepAlive: redisConfig.keepAlive,
    family: redisConfig.family,
    connectTimeout: redisConfig.connectTimeout,
    commandTimeout: redisConfig.commandTimeout,
    
    // 重连策略
    retryStrategy: (times) => {
      const delay = Math.min(times * 50, 2000)
      logger.warn(`Redis重连尝试 ${times}, 延迟 ${delay}ms`)
      return delay
    },
    
    // 重连失败策略
    reconnectOnError: (err) => {
      const targetError = 'READONLY'
      return err.message.includes(targetError)
    }
  })

  // 连接事件监听
  redis.on('connect', () => {
    logger.info('Redis连接成功')
  })

  redis.on('ready', () => {
    logger.info('Redis准备就绪')
  })

  redis.on('error', (err) => {
    logger.error('Redis连接错误:', err)
  })

  redis.on('close', () => {
    logger.warn('Redis连接关闭')
  })

  redis.on('reconnecting', () => {
    logger.info('Redis重新连接中...')
  })

  return redis
}

/**
 * Redis缓存工具类
 */
export class RedisCache {
  private redis: Redis

  constructor(redis: Redis) {
    this.redis = redis
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value)
      if (ttl) {
        await this.redis.setex(key, ttl, serializedValue)
      } else {
        await this.redis.set(key, serializedValue)
      }
    } catch (error) {
      logger.error('Redis设置缓存失败:', { key, error })
      throw error
    }
  }

  /**
   * 获取缓存
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key)
      if (value === null) {
        return null
      }
      return JSON.parse(value) as T
    } catch (error) {
      logger.error('Redis获取缓存失败:', { key, error })
      return null
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key)
    } catch (error) {
      logger.error('Redis删除缓存失败:', { key, error })
      throw error
    }
  }

  /**
   * 批量删除缓存（支持模式匹配）
   */
  async delPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.redis.keys(pattern)
      if (keys.length === 0) {
        return 0
      }
      return await this.redis.del(...keys)
    } catch (error) {
      logger.error('Redis批量删除缓存失败:', { pattern, error })
      throw error
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key)
      return result === 1
    } catch (error) {
      logger.error('Redis检查键存在失败:', { key, error })
      return false
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.redis.expire(key, ttl)
    } catch (error) {
      logger.error('Redis设置过期时间失败:', { key, ttl, error })
      throw error
    }
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key)
    } catch (error) {
      logger.error('Redis获取过期时间失败:', { key, error })
      return -1
    }
  }

  /**
   * 原子递增
   */
  async incr(key: string): Promise<number> {
    try {
      return await this.redis.incr(key)
    } catch (error) {
      logger.error('Redis递增失败:', { key, error })
      throw error
    }
  }

  /**
   * 原子递增指定值
   */
  async incrby(key: string, increment: number): Promise<number> {
    try {
      return await this.redis.incrby(key, increment)
    } catch (error) {
      logger.error('Redis递增指定值失败:', { key, increment, error })
      throw error
    }
  }

  /**
   * 原子递减
   */
  async decr(key: string): Promise<number> {
    try {
      return await this.redis.decr(key)
    } catch (error) {
      logger.error('Redis递减失败:', { key, error })
      throw error
    }
  }

  /**
   * 哈希表操作 - 设置字段
   */
  async hset(key: string, field: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value)
      await this.redis.hset(key, field, serializedValue)
    } catch (error) {
      logger.error('Redis哈希设置失败:', { key, field, error })
      throw error
    }
  }

  /**
   * 哈希表操作 - 获取字段
   */
  async hget<T = any>(key: string, field: string): Promise<T | null> {
    try {
      const value = await this.redis.hget(key, field)
      if (value === null) {
        return null
      }
      return JSON.parse(value) as T
    } catch (error) {
      logger.error('Redis哈希获取失败:', { key, field, error })
      return null
    }
  }

  /**
   * 哈希表操作 - 获取所有字段
   */
  async hgetall<T = Record<string, any>>(key: string): Promise<T> {
    try {
      const hash = await this.redis.hgetall(key)
      const result: any = {}
      
      for (const [field, value] of Object.entries(hash)) {
        try {
          result[field] = JSON.parse(value)
        } catch {
          result[field] = value
        }
      }
      
      return result as T
    } catch (error) {
      logger.error('Redis哈希获取所有字段失败:', { key, error })
      return {} as T
    }
  }

  /**
   * 哈希表操作 - 递增字段值
   */
  async hincrby(key: string, field: string, increment: number): Promise<number> {
    try {
      return await this.redis.hincrby(key, field, increment)
    } catch (error) {
      logger.error('Redis哈希递增失败:', { key, field, increment, error })
      throw error
    }
  }

  /**
   * 集合操作 - 添加成员
   */
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.redis.sadd(key, ...members)
    } catch (error) {
      logger.error('Redis集合添加失败:', { key, members, error })
      throw error
    }
  }

  /**
   * 集合操作 - 获取所有成员
   */
  async smembers(key: string): Promise<string[]> {
    try {
      return await this.redis.smembers(key)
    } catch (error) {
      logger.error('Redis集合获取成员失败:', { key, error })
      return []
    }
  }

  /**
   * 集合操作 - 检查成员是否存在
   */
  async sismember(key: string, member: string): Promise<boolean> {
    try {
      const result = await this.redis.sismember(key, member)
      return result === 1
    } catch (error) {
      logger.error('Redis集合检查成员失败:', { key, member, error })
      return false
    }
  }

  /**
   * 有序集合操作 - 添加成员
   */
  async zadd(key: string, score: number, member: string): Promise<number> {
    try {
      return await this.redis.zadd(key, score, member)
    } catch (error) {
      logger.error('Redis有序集合添加失败:', { key, score, member, error })
      throw error
    }
  }

  /**
   * 有序集合操作 - 获取范围内成员
   */
  async zrange(key: string, start: number, stop: number): Promise<string[]> {
    try {
      return await this.redis.zrange(key, start, stop)
    } catch (error) {
      logger.error('Redis有序集合获取范围失败:', { key, start, stop, error })
      return []
    }
  }

  /**
   * 分布式锁
   */
  async lock(key: string, ttl: number = 30): Promise<boolean> {
    try {
      const lockKey = `${CACHE_KEYS.DISTRIBUTED_LOCK}${key}`
      const result = await this.redis.set(lockKey, '1', 'EX', ttl, 'NX')
      return result === 'OK'
    } catch (error) {
      logger.error('Redis获取分布式锁失败:', { key, ttl, error })
      return false
    }
  }

  /**
   * 释放分布式锁
   */
  async unlock(key: string): Promise<void> {
    try {
      const lockKey = `${CACHE_KEYS.DISTRIBUTED_LOCK}${key}`
      await this.redis.del(lockKey)
    } catch (error) {
      logger.error('Redis释放分布式锁失败:', { key, error })
      throw error
    }
  }

  /**
   * 获取Redis实例（用于复杂操作）
   */
  getRedisInstance(): Redis {
    return this.redis
  }

  /**
   * 关闭连接
   */
  async disconnect(): Promise<void> {
    try {
      await this.redis.quit()
      logger.info('Redis连接已关闭')
    } catch (error) {
      logger.error('Redis关闭连接失败:', error)
    }
  }
}

// 创建默认Redis实例
export const redis = createRedisInstance()
export const redisCache = new RedisCache(redis)

// 导出类型
export type { Redis }
export { Redis as RedisClient }
