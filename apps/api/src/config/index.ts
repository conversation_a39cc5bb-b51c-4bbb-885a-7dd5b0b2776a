import dotenv from 'dotenv'
import { z } from 'zod'

// 加载环境变量
dotenv.config()

/**
 * 环境变量验证模式
 * 使用Zod进行类型安全的环境变量验证
 */
const envSchema = z.object({
  // 服务器配置
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  HOST: z.string().default('0.0.0.0'),
  
  // 数据库配置
  DATABASE_URL: z.string().min(1, '数据库连接字符串不能为空'),
  
  // Redis配置
  REDIS_URL: z.string().optional(),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().transform(Number).default('6379'),
  REDIS_PASSWORD: z.string().optional(),
  
  // JWT配置
  JWT_SECRET: z.string().min(32, 'JWT密钥长度至少32位'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('30d'),
  
  // OpenAI配置
  OPENAI_API_KEY: z.string().min(1, 'OpenAI API密钥不能为空'),
  OPENAI_ORGANIZATION: z.string().optional(),
  
  // Stripe配置
  STRIPE_SECRET_KEY: z.string().min(1, 'Stripe密钥不能为空'),
  STRIPE_WEBHOOK_SECRET: z.string().min(1, 'Stripe Webhook密钥不能为空'),
  STRIPE_PUBLISHABLE_KEY: z.string().min(1, 'Stripe公钥不能为空'),
  
  // 邮件配置
  SMTP_HOST: z.string().default('smtp.gmail.com'),
  SMTP_PORT: z.string().transform(Number).default('587'),
  SMTP_USER: z.string().min(1, 'SMTP用户名不能为空'),
  SMTP_PASS: z.string().min(1, 'SMTP密码不能为空'),
  SMTP_FROM: z.string().email('发件人邮箱格式不正确'),
  
  // 文件存储配置
  UPLOAD_DIR: z.string().default('./uploads'),
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  
  // 安全配置
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default('900000'), // 15分钟
  
  // 日志配置
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),
  
  // 前端URL配置
  FRONTEND_URL: z.string().url().default('http://localhost:3000'),
  
  // API配置
  API_PREFIX: z.string().default('/api/v1'),
  API_DOCS_PATH: z.string().default('/docs'),
  
  // 监控配置
  ENABLE_METRICS: z.string().transform(Boolean).default('true'),
  METRICS_PATH: z.string().default('/metrics'),
})

/**
 * 验证环境变量
 */
const parseEnv = () => {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      throw new Error(`环境变量验证失败:\n${errorMessages.join('\n')}`)
    }
    throw error
  }
}

const env = parseEnv()

/**
 * 应用配置对象
 * 包含所有应用运行所需的配置信息
 */
export const config = {
  // 环境信息
  env: env.NODE_ENV,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  isTest: env.NODE_ENV === 'test',
  
  // 服务器配置
  port: env.PORT,
  host: env.HOST,
  
  // 数据库配置
  database: {
    url: env.DATABASE_URL,
  },
  
  // Redis配置
  redis: {
    url: env.REDIS_URL,
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
  },
  
  // JWT配置
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },
  
  // OpenAI配置
  openai: {
    apiKey: env.OPENAI_API_KEY,
    organization: env.OPENAI_ORGANIZATION,
  },
  
  // Stripe配置
  stripe: {
    secretKey: env.STRIPE_SECRET_KEY,
    webhookSecret: env.STRIPE_WEBHOOK_SECRET,
    publishableKey: env.STRIPE_PUBLISHABLE_KEY,
  },
  
  // 邮件配置
  smtp: {
    host: env.SMTP_HOST,
    port: env.SMTP_PORT,
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
    from: env.SMTP_FROM,
  },
  
  // 文件上传配置
  upload: {
    dir: env.UPLOAD_DIR,
    maxFileSize: env.MAX_FILE_SIZE,
  },
  
  // 安全配置
  security: {
    bcryptRounds: env.BCRYPT_ROUNDS,
    rateLimit: {
      max: env.RATE_LIMIT_MAX,
      window: env.RATE_LIMIT_WINDOW,
    },
  },
  
  // 日志配置
  logging: {
    level: env.LOG_LEVEL,
    file: env.LOG_FILE,
  },
  
  // URL配置
  urls: {
    frontend: env.FRONTEND_URL,
    api: env.API_PREFIX,
    docs: env.API_DOCS_PATH,
  },
  
  // 监控配置
  monitoring: {
    enabled: env.ENABLE_METRICS,
    path: env.METRICS_PATH,
  },
} as const

/**
 * 数据库配置
 * 用于Prisma客户端
 */
export const databaseConfig = {
  url: config.database.url,
  // 连接池配置
  connectionLimit: config.isProduction ? 20 : 5,
  // 查询超时时间
  queryTimeout: 30000,
  // 事务超时时间
  transactionTimeout: 60000,
}

/**
 * CORS配置
 * 跨域资源共享设置
 */
export const corsConfig = {
  origin: config.isDevelopment 
    ? true 
    : [config.urls.frontend],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
  ],
}

/**
 * Swagger配置
 * API文档生成配置
 */
export const swaggerConfig = {
  swagger: {
    info: {
      title: 'AI数字营销平台API',
      description: '基于AI技术的数字营销平台后端API接口文档',
      version: '1.0.0',
      contact: {
        name: 'AI数字营销团队',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    host: `${config.host}:${config.port}`,
    schemes: config.isProduction ? ['https'] : ['http'],
    consumes: ['application/json'],
    produces: ['application/json'],
    securityDefinitions: {
      Bearer: {
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
        description: 'JWT令牌认证，格式: Bearer <token>',
      },
    },
    tags: [
      { name: 'auth', description: '用户认证相关接口' },
      { name: 'users', description: '用户管理相关接口' },
      { name: 'ai', description: 'AI内容生成相关接口' },
      { name: 'campaigns', description: '营销活动相关接口' },
      { name: 'analytics', description: '数据分析相关接口' },
      { name: 'payments', description: '支付订阅相关接口' },
    ],
  },
  uiConfig: {
    docExpansion: 'list',
    deepLinking: false,
  },
  staticCSP: true,
  transformStaticCSP: (header: string) => header,
}

/**
 * 验证配置是否正确加载
 */
export function validateConfig() {
  const requiredConfigs = [
    'database.url',
    'jwt.secret',
    'openai.apiKey',
    'stripe.secretKey',
    'smtp.user',
    'smtp.pass',
  ]
  
  for (const configPath of requiredConfigs) {
    const value = configPath.split('.').reduce((obj, key) => obj?.[key], config as any)
    if (!value) {
      throw new Error(`必需的配置项缺失: ${configPath}`)
    }
  }
  
  console.log('✅ 配置验证通过')
}
