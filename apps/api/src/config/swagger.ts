// Swagger API文档配置
// 自动生成API文档和交互式API测试界面

import { FastifyDynamicSwaggerOptions } from '@fastify/swagger'
import { FastifySwaggerUiOptions } from '@fastify/swagger-ui'
import { config } from './index'

/**
 * Swagger文档配置
 */
export const swaggerConfig: FastifyDynamicSwaggerOptions = {
  openapi: {
    openapi: '3.0.0',
    info: {
      title: 'AI数字营销平台API',
      description: `
# AI数字营销平台API文档

基于AI技术的智能数字营销平台后端API服务。

## 功能特性

- **用户管理** - 用户注册、登录、资料管理
- **AI内容生成** - 智能生成营销文案、图片等内容
- **营销活动管理** - 创建和管理各类营销活动
- **数据分析** - 用户行为分析、营销效果分析
- **支付订阅** - 订阅计划管理、支付处理

## 认证方式

API使用JWT Bearer Token进行认证。在请求头中添加：
\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## API版本

当前API版本：v1.0.0
支持的版本：v1

## 响应格式

所有API响应都遵循统一的格式：

### 成功响应
\`\`\`json
{
  "success": true,
  "data": {},
  "meta": {
    "timestamp": "2025-01-28T10:00:00.000Z",
    "requestId": "req_123456"
  }
}
\`\`\`

### 错误响应
\`\`\`json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "meta": {
    "timestamp": "2025-01-28T10:00:00.000Z",
    "requestId": "req_123456"
  }
}
\`\`\`

## 分页

支持分页的接口使用以下参数：
- \`page\`: 页码（从1开始）
- \`limit\`: 每页数量（默认20，最大100）

分页响应包含额外的meta信息：
\`\`\`json
{
  "success": true,
  "data": [],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    },
    "timestamp": "2025-01-28T10:00:00.000Z",
    "requestId": "req_123456"
  }
}
\`\`\`

## 错误代码

常见错误代码说明：
- \`VALIDATION_ERROR\`: 请求参数验证失败
- \`UNAUTHORIZED\`: 未授权访问
- \`FORBIDDEN\`: 禁止访问
- \`NOT_FOUND\`: 资源未找到
- \`CONFLICT\`: 资源冲突
- \`TOO_MANY_REQUESTS\`: 请求过于频繁
- \`INTERNAL_ERROR\`: 服务器内部错误

## 限流

API实施限流保护：
- 每个IP每15分钟最多100个请求
- 认证用户每15分钟最多1000个请求
- 超出限制将返回429状态码

## 联系方式

- 技术支持：<EMAIL>
- API问题：<EMAIL>
- 文档反馈：<EMAIL>
      `,
      version: '1.0.0',
      contact: {
        name: 'AI数字营销平台技术团队',
        email: '<EMAIL>',
        url: 'https://ai-marketing.com'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      },
      termsOfService: 'https://ai-marketing.com/terms'
    },
    servers: [
      {
        url: config.isDevelopment ? `http://localhost:${config.port}` : 'https://api.ai-marketing.com',
        description: config.isDevelopment ? '开发环境' : '生产环境'
      }
    ],
    components: {
      securitySchemes: {
        Bearer: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT认证令牌'
        },
        ApiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API密钥认证'
        }
      },
      schemas: {
        // 通用响应模式
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: '请求是否成功'
            },
            data: {
              description: '响应数据'
            },
            error: {
              type: 'object',
              properties: {
                code: {
                  type: 'string',
                  description: '错误代码'
                },
                message: {
                  type: 'string',
                  description: '错误消息'
                },
                details: {
                  description: '错误详情'
                }
              }
            },
            meta: {
              type: 'object',
              properties: {
                timestamp: {
                  type: 'string',
                  format: 'date-time',
                  description: '响应时间戳'
                },
                requestId: {
                  type: 'string',
                  description: '请求ID'
                },
                pagination: {
                  type: 'object',
                  properties: {
                    page: {
                      type: 'integer',
                      description: '当前页码'
                    },
                    limit: {
                      type: 'integer',
                      description: '每页数量'
                    },
                    total: {
                      type: 'integer',
                      description: '总记录数'
                    },
                    totalPages: {
                      type: 'integer',
                      description: '总页数'
                    }
                  }
                }
              }
            }
          }
        },
        
        // 分页参数
        PaginationQuery: {
          type: 'object',
          properties: {
            page: {
              type: 'integer',
              minimum: 1,
              default: 1,
              description: '页码'
            },
            limit: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 20,
              description: '每页数量'
            }
          }
        },

        // 用户模式
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: '用户ID'
            },
            email: {
              type: 'string',
              format: 'email',
              description: '邮箱地址'
            },
            firstName: {
              type: 'string',
              description: '名字'
            },
            lastName: {
              type: 'string',
              description: '姓氏'
            },
            avatar: {
              type: 'string',
              format: 'uri',
              description: '头像URL'
            },
            role: {
              type: 'string',
              enum: ['USER', 'ADMIN', 'SUPER_ADMIN'],
              description: '用户角色'
            },
            status: {
              type: 'string',
              enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED'],
              description: '用户状态'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: '创建时间'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: '更新时间'
            }
          }
        },

        // 错误响应模式
        ErrorResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            error: {
              type: 'object',
              properties: {
                code: {
                  type: 'string',
                  example: 'VALIDATION_ERROR'
                },
                message: {
                  type: 'string',
                  example: '请求参数验证失败'
                },
                details: {
                  type: 'object'
                }
              }
            },
            meta: {
              type: 'object',
              properties: {
                timestamp: {
                  type: 'string',
                  format: 'date-time'
                },
                requestId: {
                  type: 'string'
                }
              }
            }
          }
        }
      },
      
      responses: {
        // 通用错误响应
        BadRequest: {
          description: '请求参数错误',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        },
        Unauthorized: {
          description: '未授权访问',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        },
        Forbidden: {
          description: '禁止访问',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        },
        NotFound: {
          description: '资源未找到',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        },
        TooManyRequests: {
          description: '请求过于频繁',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        },
        InternalServerError: {
          description: '服务器内部错误',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse'
              }
            }
          }
        }
      }
    },
    security: [
      {
        Bearer: []
      }
    ],
    tags: [
      {
        name: 'auth',
        description: '认证相关接口'
      },
      {
        name: 'users',
        description: '用户管理接口'
      },
      {
        name: 'ai',
        description: 'AI内容生成接口'
      },
      {
        name: 'campaigns',
        description: '营销活动管理接口'
      },
      {
        name: 'analytics',
        description: '数据分析接口'
      },
      {
        name: 'payments',
        description: '支付订阅接口'
      },
      {
        name: 'uploads',
        description: '文件上传接口'
      },
      {
        name: 'admin',
        description: '系统管理接口'
      }
    ]
  }
}

/**
 * Swagger UI配置
 */
export const swaggerUiConfig: FastifySwaggerUiOptions = {
  routePrefix: config.urls.docs,
  uiConfig: {
    docExpansion: 'list',
    deepLinking: true,
    defaultModelsExpandDepth: 2,
    defaultModelExpandDepth: 2,
    displayOperationId: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    tryItOutEnabled: true
  },
  uiHooks: {
    onRequest: function (request, reply, next) {
      // 可以在这里添加访问控制
      next()
    }
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
  theme: {
    title: 'AI数字营销平台API文档'
  }
}
