{"name": "api", "version": "1.0.0", "description": "AI数字营销平台后端API服务", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force", "db:deploy": "prisma migrate deploy"}, "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "@fastify/jwt": "^7.2.4", "@fastify/cookie": "^9.2.0", "@fastify/multipart": "^8.0.0", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^2.1.0", "@prisma/client": "^5.7.1", "prisma": "^5.7.1", "bcryptjs": "^2.4.3", "zod": "^3.22.4", "openai": "^4.20.1", "stripe": "^14.9.0", "nodemailer": "^6.9.7", "redis": "^4.6.11", "bull": "^4.12.2", "winston": "^3.11.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "date-fns": "^3.0.6", "lodash": "^4.17.21"}, "devDependencies": {"@types/node": "^20.10.5", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*"}, "keywords": ["ai", "marketing", "api", "fastify", "typescript"], "author": "AI数字营销团队", "license": "MIT"}