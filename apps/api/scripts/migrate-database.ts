// 数据库迁移脚本
// 用于初始化数据库、创建表结构、插入初始数据

import { PrismaClient } from '@prisma/client'
import { logger } from '../src/utils/logger'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

/**
 * 主迁移函数
 */
async function main() {
  logger.info('开始数据库迁移...')

  try {
    // 1. 创建管理员用户
    await createAdminUser()

    // 2. 创建默认邮件模板
    await createDefaultEmailTemplates()

    // 3. 创建默认报告模板
    await createDefaultReportTemplates()

    // 4. 创建示例数据（开发环境）
    if (process.env.NODE_ENV === 'development') {
      await createSampleData()
    }

    logger.info('数据库迁移完成')
  } catch (error) {
    logger.error('数据库迁移失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * 创建管理员用户
 */
async function createAdminUser() {
  logger.info('创建管理员用户...')

  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456'

  // 检查管理员是否已存在
  const existingAdmin = await prisma.user.findUnique({
    where: { email: adminEmail }
  })

  if (existingAdmin) {
    logger.info('管理员用户已存在，跳过创建')
    return
  }

  // 创建管理员用户
  const hashedPassword = await bcrypt.hash(adminPassword, 12)

  const admin = await prisma.user.create({
    data: {
      email: adminEmail,
      firstName: 'Admin',
      lastName: 'User',
      role: 'SUPER_ADMIN',
      emailVerified: new Date(),
      profile: {
        create: {
          company: 'AI数字营销平台',
          industry: 'Technology',
          language: 'zh-CN',
          timezone: 'Asia/Shanghai',
          preferences: {
            theme: 'light',
            notifications: {
              email: true,
              push: true,
              marketing: false
            }
          }
        }
      }
    }
  })

  logger.info(`管理员用户创建成功: ${admin.email}`)
}

/**
 * 创建默认邮件模板
 */
async function createDefaultEmailTemplates() {
  logger.info('创建默认邮件模板...')

  const templates = [
    {
      name: '欢迎邮件',
      description: '新用户注册后的欢迎邮件',
      type: 'WELCOME',
      subject: '欢迎加入AI数字营销平台！',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">欢迎加入AI数字营销平台！</h1>
          <p>亲爱的 {{user_name}}，</p>
          <p>感谢您注册我们的AI数字营销平台。我们很高兴为您提供强大的AI营销工具。</p>
          <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>您可以开始：</h3>
            <ul>
              <li>使用AI生成营销文案</li>
              <li>创建智能营销活动</li>
              <li>分析营销数据</li>
              <li>自动化营销流程</li>
            </ul>
          </div>
          <p>
            <a href="{{dashboard_url}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              开始使用
            </a>
          </p>
          <p>如有任何问题，请随时联系我们的客服团队。</p>
          <p>祝您使用愉快！</p>
          <p>AI数字营销平台团队</p>
        </div>
      `,
      textContent: `
        欢迎加入AI数字营销平台！
        
        亲爱的 {{user_name}}，
        
        感谢您注册我们的AI数字营销平台。我们很高兴为您提供强大的AI营销工具。
        
        您可以开始：
        - 使用AI生成营销文案
        - 创建智能营销活动
        - 分析营销数据
        - 自动化营销流程
        
        访问您的仪表板：{{dashboard_url}}
        
        如有任何问题，请随时联系我们的客服团队。
        
        祝您使用愉快！
        AI数字营销平台团队
      `,
      variables: ['user_name', 'dashboard_url'],
      tags: ['welcome', 'onboarding']
    },
    {
      name: '密码重置',
      description: '用户请求重置密码时发送的邮件',
      type: 'TRANSACTIONAL',
      subject: '重置您的密码',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">重置您的密码</h1>
          <p>您好，</p>
          <p>我们收到了重置您账户密码的请求。如果这不是您的操作，请忽略此邮件。</p>
          <p>要重置您的密码，请点击下面的按钮：</p>
          <p>
            <a href="{{reset_url}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              重置密码
            </a>
          </p>
          <p>此链接将在24小时后失效。</p>
          <p>如果按钮无法点击，请复制以下链接到浏览器：</p>
          <p style="word-break: break-all; color: #6b7280;">{{reset_url}}</p>
          <p>AI数字营销平台团队</p>
        </div>
      `,
      textContent: `
        重置您的密码
        
        您好，
        
        我们收到了重置您账户密码的请求。如果这不是您的操作，请忽略此邮件。
        
        要重置您的密码，请访问以下链接：
        {{reset_url}}
        
        此链接将在24小时后失效。
        
        AI数字营销平台团队
      `,
      variables: ['reset_url'],
      tags: ['password', 'security']
    },
    {
      name: '营销周报',
      description: '每周营销数据报告邮件',
      type: 'NEWSLETTER',
      subject: '您的营销周报 - {{week_range}}',
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">营销周报</h1>
          <p style="color: #6b7280;">{{week_range}}</p>
          
          <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="margin-top: 0;">本周亮点</h2>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
              <div style="text-align: center;">
                <div style="font-size: 2em; font-weight: bold; color: #059669;">{{page_views}}</div>
                <div style="color: #6b7280;">页面浏览量</div>
              </div>
              <div style="text-align: center;">
                <div style="font-size: 2em; font-weight: bold; color: #2563eb;">{{conversions}}</div>
                <div style="color: #6b7280;">转化次数</div>
              </div>
            </div>
          </div>
          
          <h3>活动表现</h3>
          <p>{{campaign_summary}}</p>
          
          <p>
            <a href="{{dashboard_url}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              查看详细报告
            </a>
          </p>
          
          <p>继续保持优秀的营销表现！</p>
          <p>AI数字营销平台团队</p>
        </div>
      `,
      textContent: `
        营销周报
        {{week_range}}
        
        本周亮点：
        - 页面浏览量：{{page_views}}
        - 转化次数：{{conversions}}
        
        活动表现：
        {{campaign_summary}}
        
        查看详细报告：{{dashboard_url}}
        
        继续保持优秀的营销表现！
        AI数字营销平台团队
      `,
      variables: ['week_range', 'page_views', 'conversions', 'campaign_summary', 'dashboard_url'],
      tags: ['report', 'weekly']
    }
  ]

  // 获取管理员用户ID
  const admin = await prisma.user.findFirst({
    where: { role: 'SUPER_ADMIN' }
  })

  if (!admin) {
    logger.warn('未找到管理员用户，跳过创建邮件模板')
    return
  }

  for (const template of templates) {
    const existing = await prisma.emailTemplate.findFirst({
      where: {
        name: template.name,
        userId: admin.id
      }
    })

    if (!existing) {
      await prisma.emailTemplate.create({
        data: {
          ...template,
          userId: admin.id,
          type: template.type as any
        }
      })
      logger.info(`创建邮件模板: ${template.name}`)
    }
  }
}

/**
 * 创建默认报告模板
 */
async function createDefaultReportTemplates() {
  logger.info('创建默认报告模板...')

  const templates = [
    {
      name: '营销概览报告',
      description: '全面的营销数据概览报告',
      type: 'ANALYTICS_SUMMARY',
      format: 'PDF',
      sections: [
        {
          id: 'overview',
          name: '数据概览',
          type: 'metric',
          config: {
            dataSource: 'analytics',
            metrics: ['totalEvents', 'uniqueUsers', 'conversionRate']
          }
        },
        {
          id: 'trends',
          name: '趋势分析',
          type: 'chart',
          config: {
            dataSource: 'analytics',
            groupBy: 'day',
            visualization: 'line'
          }
        },
        {
          id: 'campaigns',
          name: '活动表现',
          type: 'table',
          config: {
            dataSource: 'campaigns'
          }
        }
      ],
      styling: {
        theme: 'professional',
        colors: ['#2563eb', '#059669', '#dc2626'],
        fonts: ['Arial', 'sans-serif'],
        layout: 'standard'
      }
    },
    {
      name: '活动效果报告',
      description: '详细的营销活动效果分析报告',
      type: 'CAMPAIGN_PERFORMANCE',
      format: 'EXCEL',
      sections: [
        {
          id: 'campaign_list',
          name: '活动列表',
          type: 'table',
          config: {
            dataSource: 'campaigns'
          }
        },
        {
          id: 'performance_metrics',
          name: '效果指标',
          type: 'metric',
          config: {
            dataSource: 'campaigns',
            metrics: ['impressions', 'clicks', 'conversions', 'cost']
          }
        }
      ],
      styling: {
        theme: 'modern',
        colors: ['#7c3aed', '#059669', '#ea580c'],
        fonts: ['Helvetica', 'sans-serif'],
        layout: 'grid'
      }
    }
  ]

  // 获取管理员用户ID
  const admin = await prisma.user.findFirst({
    where: { role: 'SUPER_ADMIN' }
  })

  if (!admin) {
    logger.warn('未找到管理员用户，跳过创建报告模板')
    return
  }

  for (const template of templates) {
    const existing = await prisma.reportTemplate.findFirst({
      where: {
        name: template.name,
        userId: admin.id
      }
    })

    if (!existing) {
      await prisma.reportTemplate.create({
        data: {
          ...template,
          userId: admin.id,
          type: template.type as any,
          format: template.format as any,
          createdBy: admin.id
        }
      })
      logger.info(`创建报告模板: ${template.name}`)
    }
  }
}

/**
 * 创建示例数据（仅开发环境）
 */
async function createSampleData() {
  logger.info('创建示例数据...')

  // 创建测试用户
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'USER',
      emailVerified: new Date(),
      profile: {
        create: {
          company: '测试公司',
          industry: 'Technology',
          language: 'zh-CN',
          timezone: 'Asia/Shanghai'
        }
      }
    }
  })

  // 创建示例营销活动
  const sampleCampaign = await prisma.campaign.create({
    data: {
      userId: testUser.id,
      name: '春季促销活动',
      description: '针对春季产品的促销营销活动',
      type: 'EMAIL',
      status: 'ACTIVE',
      targetAudience: {
        demographics: {
          ageRange: '25-45',
          interests: ['technology', 'marketing']
        }
      },
      budget: {
        total: 10000,
        daily: 500,
        currency: 'CNY'
      },
      schedule: {
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        timezone: 'Asia/Shanghai'
      },
      content: {
        subject: '春季特惠，限时优惠！',
        message: '我们的春季促销活动正式开始，享受高达50%的折扣！'
      },
      settings: {
        autoOptimize: true,
        trackingEnabled: true
      },
      tags: ['promotion', 'spring', 'discount']
    }
  })

  // 创建示例分析事件
  const events = [
    {
      type: 'PAGE_VIEW',
      properties: { page: '/dashboard' },
      context: { userAgent: 'Mozilla/5.0...', ip: '***********' }
    },
    {
      type: 'BUTTON_CLICK',
      properties: { button: 'create-campaign' },
      context: { userAgent: 'Mozilla/5.0...', ip: '***********' }
    },
    {
      type: 'SIGNUP',
      properties: { source: 'organic' },
      context: { userAgent: 'Mozilla/5.0...', ip: '***********' }
    }
  ]

  for (const event of events) {
    await prisma.analyticsEvent.create({
      data: {
        userId: testUser.id,
        sessionId: `session_${Date.now()}`,
        type: event.type as any,
        timestamp: new Date(),
        properties: event.properties,
        context: event.context,
        source: 'WEB_ANALYTICS',
        campaignId: sampleCampaign.id
      }
    })
  }

  logger.info('示例数据创建完成')
}

// 执行迁移
if (require.main === module) {
  main()
    .then(() => {
      logger.info('数据库迁移脚本执行完成')
      process.exit(0)
    })
    .catch((error) => {
      logger.error('数据库迁移脚本执行失败:', error)
      process.exit(1)
    })
}

export { main as migrate }
