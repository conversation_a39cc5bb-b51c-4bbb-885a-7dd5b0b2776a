// API测试设置文件
// 配置测试环境、数据库连接、模拟服务等

import { PrismaClient } from '@prisma/client'
import { Redis } from 'ioredis'
import { execSync } from 'child_process'
import dotenv from 'dotenv'

// 加载测试环境变量
dotenv.config({ path: '.env.test' })

// 全局测试配置
declare global {
  var __PRISMA__: PrismaClient
  var __REDIS__: Redis
  var __TEST_USER__: any
  var __TEST_ADMIN__: any
}

/**
 * 测试数据库设置
 */
export class TestDatabase {
  private static prisma: PrismaClient

  /**
   * 初始化测试数据库
   */
  static async setup() {
    // 创建Prisma客户端
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL
        }
      }
    })

    // 连接数据库
    await this.prisma.$connect()

    // 运行数据库迁移
    try {
      execSync('npx prisma migrate deploy', {
        env: {
          ...process.env,
          DATABASE_URL: process.env.TEST_DATABASE_URL
        }
      })
    } catch (error) {
      console.warn('数据库迁移失败，可能是首次运行:', error)
    }

    // 清理数据库
    await this.cleanup()

    // 创建测试数据
    await this.seedTestData()

    // 设置全局变量
    global.__PRISMA__ = this.prisma

    console.log('测试数据库设置完成')
  }

  /**
   * 清理数据库
   */
  static async cleanup() {
    const tablenames = await this.prisma.$queryRaw<
      Array<{ tablename: string }>
    >`SELECT tablename FROM pg_tables WHERE schemaname='public'`

    const tables = tablenames
      .map(({ tablename }) => tablename)
      .filter((name) => name !== '_prisma_migrations')
      .map((name) => `"public"."${name}"`)
      .join(', ')

    try {
      await this.prisma.$executeRawUnsafe(`TRUNCATE TABLE ${tables} CASCADE;`)
    } catch (error) {
      console.log('清理数据库失败:', error)
    }
  }

  /**
   * 创建测试数据
   */
  static async seedTestData() {
    // 创建测试用户
    const testUser = await this.prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'USER',
        emailVerified: new Date(),
        profile: {
          create: {
            company: '测试公司',
            industry: 'Technology',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai'
          }
        }
      }
    })

    // 创建测试管理员
    const testAdmin = await this.prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        emailVerified: new Date(),
        profile: {
          create: {
            company: '管理公司',
            industry: 'Technology',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai'
          }
        }
      }
    })

    // 设置全局测试用户
    global.__TEST_USER__ = testUser
    global.__TEST_ADMIN__ = testAdmin

    console.log('测试数据创建完成')
  }

  /**
   * 关闭数据库连接
   */
  static async teardown() {
    await this.cleanup()
    await this.prisma.$disconnect()
    console.log('测试数据库连接已关闭')
  }

  /**
   * 获取Prisma客户端
   */
  static getPrisma() {
    return this.prisma
  }
}

/**
 * 测试Redis设置
 */
export class TestRedis {
  private static redis: Redis

  /**
   * 初始化测试Redis
   */
  static async setup() {
    this.redis = new Redis({
      host: process.env.TEST_REDIS_HOST || 'localhost',
      port: parseInt(process.env.TEST_REDIS_PORT || '6379'),
      db: parseInt(process.env.TEST_REDIS_DB || '15'), // 使用专用测试数据库
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    })

    // 连接Redis
    await this.redis.connect()

    // 清空测试数据库
    await this.redis.flushdb()

    // 设置全局变量
    global.__REDIS__ = this.redis

    console.log('测试Redis设置完成')
  }

  /**
   * 清理Redis数据
   */
  static async cleanup() {
    await this.redis.flushdb()
  }

  /**
   * 关闭Redis连接
   */
  static async teardown() {
    await this.cleanup()
    await this.redis.quit()
    console.log('测试Redis连接已关闭')
  }

  /**
   * 获取Redis客户端
   */
  static getRedis() {
    return this.redis
  }
}

/**
 * 测试工具类
 */
export class TestUtils {
  /**
   * 生成测试JWT令牌
   */
  static generateTestToken(userId: string, role: string = 'USER'): string {
    const jwt = require('jsonwebtoken')
    return jwt.sign(
      { userId, role },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    )
  }

  /**
   * 创建测试请求头
   */
  static createAuthHeaders(userId: string, role: string = 'USER') {
    const token = this.generateTestToken(userId, role)
    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }

  /**
   * 等待指定时间
   */
  static async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 生成随机字符串
   */
  static randomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 生成随机邮箱
   */
  static randomEmail(): string {
    return `test_${this.randomString(8)}@example.com`
  }

  /**
   * 模拟文件上传
   */
  static createMockFile(
    filename: string = 'test.jpg',
    mimetype: string = 'image/jpeg',
    size: number = 1024
  ) {
    return {
      originalname: filename,
      mimetype,
      size,
      buffer: Buffer.alloc(size),
      fieldname: 'file',
      encoding: '7bit'
    }
  }
}

/**
 * 模拟外部服务
 */
export class MockServices {
  /**
   * 模拟OpenAI API
   */
  static mockOpenAI() {
    jest.mock('openai', () => {
      return {
        OpenAI: jest.fn().mockImplementation(() => ({
          chat: {
            completions: {
              create: jest.fn().mockResolvedValue({
                choices: [{
                  message: {
                    content: '这是模拟的AI生成内容'
                  }
                }],
                usage: {
                  total_tokens: 100
                }
              })
            }
          },
          images: {
            generate: jest.fn().mockResolvedValue({
              data: [{
                url: 'https://example.com/mock-image.jpg'
              }]
            })
          }
        }))
      }
    })
  }

  /**
   * 模拟Stripe API
   */
  static mockStripe() {
    jest.mock('stripe', () => {
      return jest.fn().mockImplementation(() => ({
        customers: {
          create: jest.fn().mockResolvedValue({
            id: 'cus_mock_customer',
            email: '<EMAIL>'
          }),
          retrieve: jest.fn().mockResolvedValue({
            id: 'cus_mock_customer',
            email: '<EMAIL>'
          })
        },
        paymentIntents: {
          create: jest.fn().mockResolvedValue({
            id: 'pi_mock_payment_intent',
            client_secret: 'pi_mock_client_secret',
            status: 'requires_payment_method'
          }),
          confirm: jest.fn().mockResolvedValue({
            id: 'pi_mock_payment_intent',
            status: 'succeeded'
          })
        },
        subscriptions: {
          create: jest.fn().mockResolvedValue({
            id: 'sub_mock_subscription',
            status: 'active',
            current_period_start: Math.floor(Date.now() / 1000),
            current_period_end: Math.floor(Date.now() / 1000) + 2592000
          })
        }
      }))
    })
  }

  /**
   * 模拟邮件服务
   */
  static mockEmailService() {
    jest.mock('nodemailer', () => ({
      createTransport: jest.fn().mockReturnValue({
        sendMail: jest.fn().mockResolvedValue({
          messageId: 'mock-message-id',
          response: '250 OK'
        })
      })
    }))
  }
}

// Jest钩子函数
beforeAll(async () => {
  console.log('开始设置测试环境...')
  
  // 设置测试超时
  jest.setTimeout(30000)
  
  // 初始化数据库
  await TestDatabase.setup()
  
  // 初始化Redis
  await TestRedis.setup()
  
  // 模拟外部服务
  MockServices.mockOpenAI()
  MockServices.mockStripe()
  MockServices.mockEmailService()
  
  console.log('测试环境设置完成')
})

afterAll(async () => {
  console.log('开始清理测试环境...')
  
  // 清理数据库
  await TestDatabase.teardown()
  
  // 清理Redis
  await TestRedis.teardown()
  
  console.log('测试环境清理完成')
})

beforeEach(async () => {
  // 每个测试前清理数据
  await TestDatabase.cleanup()
  await TestRedis.cleanup()
  
  // 重新创建测试数据
  await TestDatabase.seedTestData()
})

afterEach(() => {
  // 清理模拟
  jest.clearAllMocks()
})

// 导出测试工具
export { TestDatabase, TestRedis, TestUtils, MockServices }
