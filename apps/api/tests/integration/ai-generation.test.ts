// AI生成相关API集成测试
// 测试文本生成、图像生成、历史记录等功能

import request from 'supertest'
import { app } from '../../src/app'
import { prisma } from '../../src/utils/database'
import { redis } from '../../src/utils/redis'

describe('AI Generation API', () => {
  let accessToken: string
  let userId: string

  // 测试用户数据
  const testUser = {
    email: '<EMAIL>',
    password: 'Test123456!',
    firstName: 'AI',
    lastName: 'Tester'
  }

  beforeAll(async () => {
    // 创建测试用户并获取访问令牌
    const registerResponse = await request(app)
      .post('/api/auth/register')
      .send(testUser)

    accessToken = registerResponse.body.data.tokens.accessToken
    userId = registerResponse.body.data.user.id
  })

  beforeEach(async () => {
    // 清理AI生成历史
    await prisma.aIGenerationHistory.deleteMany({
      where: { userId }
    })
    await redis.flushdb()
  })

  afterAll(async () => {
    await prisma.aIGenerationHistory.deleteMany({
      where: { userId }
    })
    await prisma.user.deleteMany({
      where: { email: testUser.email }
    })
    await redis.flushdb()
    await prisma.$disconnect()
    await redis.quit()
  })

  describe('POST /api/ai/generate/text', () => {
    const textGenerationRequest = {
      prompt: '写一篇关于AI营销的文章',
      type: 'article',
      tone: 'professional',
      length: 'medium',
      language: 'zh-CN'
    }

    it('应该成功生成文本内容', async () => {
      const response = await request(app)
        .post('/api/ai/generate/text')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(textGenerationRequest)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: expect.any(String),
          type: 'TEXT_GENERATION',
          prompt: textGenerationRequest.prompt,
          result: expect.any(String),
          metadata: expect.any(Object)
        }
      })

      // 验证历史记录已保存
      const history = await prisma.aIGenerationHistory.findFirst({
        where: {
          userId,
          type: 'TEXT_GENERATION',
          prompt: textGenerationRequest.prompt
        }
      })
      expect(history).toBeTruthy()
    })

    it('应该验证必需参数', async () => {
      const response = await request(app)
        .post('/api/ai/generate/text')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({})
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: expect.any(String)
        }
      })
    })

    it('应该检查使用限制', async () => {
      // 模拟达到使用限制
      await redis.set(`ai_usage:${userId}:daily`, '100')

      const response = await request(app)
        .post('/api/ai/generate/text')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(textGenerationRequest)
        .expect(429)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'USAGE_LIMIT_EXCEEDED',
          message: expect.any(String)
        }
      })
    })

    it('应该要求认证', async () => {
      const response = await request(app)
        .post('/api/ai/generate/text')
        .send(textGenerationRequest)
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: expect.any(String)
        }
      })
    })
  })

  describe('POST /api/ai/generate/image', () => {
    const imageGenerationRequest = {
      prompt: '一个现代化的办公室，充满科技感',
      style: 'realistic',
      size: '1024x1024',
      quality: 'standard'
    }

    it('应该成功生成图像', async () => {
      const response = await request(app)
        .post('/api/ai/generate/image')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(imageGenerationRequest)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: expect.any(String),
          type: 'IMAGE_GENERATION',
          prompt: imageGenerationRequest.prompt,
          result: expect.any(String), // JSON字符串包含图像URL
          metadata: expect.any(Object)
        }
      })

      // 验证结果包含图像URL
      const result = JSON.parse(response.body.data.result)
      expect(result).toHaveProperty('images')
      expect(Array.isArray(result.images)).toBe(true)
      expect(result.images.length).toBeGreaterThan(0)
    })

    it('应该验证图像尺寸', async () => {
      const response = await request(app)
        .post('/api/ai/generate/image')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          ...imageGenerationRequest,
          size: 'invalid-size'
        })
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: expect.any(String)
        }
      })
    })
  })

  describe('GET /api/ai/history', () => {
    beforeEach(async () => {
      // 创建测试历史记录
      await prisma.aIGenerationHistory.createMany({
        data: [
          {
            userId,
            type: 'TEXT_GENERATION',
            prompt: '测试提示词1',
            result: '测试结果1',
            metadata: { model: 'gpt-3.5-turbo' }
          },
          {
            userId,
            type: 'IMAGE_GENERATION',
            prompt: '测试提示词2',
            result: JSON.stringify({ images: [{ url: 'test.jpg' }] }),
            metadata: { model: 'dall-e-3' }
          },
          {
            userId,
            type: 'TEXT_GENERATION',
            prompt: '测试提示词3',
            result: '测试结果3',
            metadata: { model: 'gpt-3.5-turbo' },
            isFavorite: true
          }
        ]
      })
    })

    it('应该返回用户的生成历史', async () => {
      const response = await request(app)
        .get('/api/ai/history')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: expect.arrayContaining([
          expect.objectContaining({
            type: 'TEXT_GENERATION',
            prompt: '测试提示词1'
          }),
          expect.objectContaining({
            type: 'IMAGE_GENERATION',
            prompt: '测试提示词2'
          })
        ]),
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      })
    })

    it('应该支持类型筛选', async () => {
      const response = await request(app)
        .get('/api/ai/history?type=TEXT_GENERATION')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body.data).toHaveLength(2)
      expect(response.body.data.every((item: any) => item.type === 'TEXT_GENERATION')).toBe(true)
    })

    it('应该支持收藏筛选', async () => {
      const response = await request(app)
        .get('/api/ai/history?favoriteOnly=true')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body.data).toHaveLength(1)
      expect(response.body.data[0].isFavorite).toBe(true)
    })

    it('应该支持搜索', async () => {
      const response = await request(app)
        .get('/api/ai/history?search=测试提示词1')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body.data).toHaveLength(1)
      expect(response.body.data[0].prompt).toBe('测试提示词1')
    })

    it('应该支持分页', async () => {
      const response = await request(app)
        .get('/api/ai/history?page=1&limit=2')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body.data).toHaveLength(2)
      expect(response.body.pagination).toMatchObject({
        page: 1,
        limit: 2,
        total: 3,
        totalPages: 2
      })
    })
  })

  describe('PUT /api/ai/history/:id/favorite', () => {
    let historyId: string

    beforeEach(async () => {
      // 创建测试历史记录
      const history = await prisma.aIGenerationHistory.create({
        data: {
          userId,
          type: 'TEXT_GENERATION',
          prompt: '测试提示词',
          result: '测试结果',
          metadata: {}
        }
      })
      historyId = history.id
    })

    it('应该成功切换收藏状态', async () => {
      const response = await request(app)
        .put(`/api/ai/history/${historyId}/favorite`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: historyId,
          isFavorite: true
        }
      })

      // 验证数据库中的状态已更新
      const updatedHistory = await prisma.aIGenerationHistory.findUnique({
        where: { id: historyId }
      })
      expect(updatedHistory?.isFavorite).toBe(true)
    })

    it('应该拒绝访问其他用户的记录', async () => {
      // 创建另一个用户的记录
      const otherUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'Other',
          lastName: 'User',
          role: 'USER'
        }
      })

      const otherHistory = await prisma.aIGenerationHistory.create({
        data: {
          userId: otherUser.id,
          type: 'TEXT_GENERATION',
          prompt: '其他用户的提示词',
          result: '其他用户的结果',
          metadata: {}
        }
      })

      const response = await request(app)
        .put(`/api/ai/history/${otherHistory.id}/favorite`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: expect.any(String)
        }
      })

      // 清理
      await prisma.aIGenerationHistory.delete({ where: { id: otherHistory.id } })
      await prisma.user.delete({ where: { id: otherUser.id } })
    })
  })

  describe('DELETE /api/ai/history/:id', () => {
    let historyId: string

    beforeEach(async () => {
      // 创建测试历史记录
      const history = await prisma.aIGenerationHistory.create({
        data: {
          userId,
          type: 'TEXT_GENERATION',
          prompt: '测试提示词',
          result: '测试结果',
          metadata: {}
        }
      })
      historyId = history.id
    })

    it('应该成功删除历史记录', async () => {
      const response = await request(app)
        .delete(`/api/ai/history/${historyId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.any(String)
      })

      // 验证记录已被软删除
      const deletedHistory = await prisma.aIGenerationHistory.findUnique({
        where: { id: historyId }
      })
      expect(deletedHistory?.deletedAt).toBeTruthy()
    })

    it('应该拒绝删除不存在的记录', async () => {
      const response = await request(app)
        .delete('/api/ai/history/non-existent-id')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: expect.any(String)
        }
      })
    })
  })

  describe('GET /api/ai/usage', () => {
    it('应该返回用户的使用统计', async () => {
      // 设置一些使用数据
      await redis.set(`ai_usage:${userId}:daily`, '5')
      await redis.set(`ai_usage:${userId}:monthly`, '50')

      const response = await request(app)
        .get('/api/ai/usage')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          daily: {
            used: expect.any(Number),
            limit: expect.any(Number),
            remaining: expect.any(Number)
          },
          monthly: {
            used: expect.any(Number),
            limit: expect.any(Number),
            remaining: expect.any(Number)
          }
        }
      })
    })
  })
})
