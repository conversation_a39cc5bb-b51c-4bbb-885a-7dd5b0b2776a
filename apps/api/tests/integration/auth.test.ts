// 认证相关API集成测试
// 测试用户注册、登录、令牌刷新等认证功能

import request from 'supertest'
import { app } from '../../src/app'
import { prisma } from '../../src/utils/database'
import { redis } from '../../src/utils/redis'

describe('Authentication API', () => {
  // 测试数据
  const testUser = {
    email: '<EMAIL>',
    password: 'Test123456!',
    firstName: 'Test',
    lastName: 'User'
  }

  // 清理测试数据
  beforeEach(async () => {
    await prisma.user.deleteMany({
      where: { email: testUser.email }
    })
    await redis.flushdb()
  })

  afterAll(async () => {
    await prisma.user.deleteMany({
      where: { email: testUser.email }
    })
    await redis.flushdb()
    await prisma.$disconnect()
    await redis.quit()
  })

  describe('POST /api/auth/register', () => {
    it('应该成功注册新用户', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(testUser)
        .expect(201)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: {
            email: testUser.email,
            firstName: testUser.firstName,
            lastName: testUser.lastName,
            role: 'USER',
            isActive: true
          },
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String)
          }
        }
      })

      // 验证用户已创建
      const user = await prisma.user.findUnique({
        where: { email: testUser.email }
      })
      expect(user).toBeTruthy()
      expect(user?.email).toBe(testUser.email)
    })

    it('应该拒绝重复邮箱注册', async () => {
      // 先注册一个用户
      await request(app)
        .post('/api/auth/register')
        .send(testUser)
        .expect(201)

      // 尝试用相同邮箱再次注册
      const response = await request(app)
        .post('/api/auth/register')
        .send(testUser)
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'EMAIL_ALREADY_EXISTS',
          message: expect.any(String)
        }
      })
    })

    it('应该验证邮箱格式', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...testUser,
          email: 'invalid-email'
        })
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: expect.any(String)
        }
      })
    })

    it('应该验证密码强度', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...testUser,
          password: '123'
        })
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: expect.any(String)
        }
      })
    })
  })

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // 创建测试用户
      await request(app)
        .post('/api/auth/register')
        .send(testUser)
    })

    it('应该成功登录', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: {
            email: testUser.email,
            firstName: testUser.firstName,
            lastName: testUser.lastName
          },
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String)
          }
        }
      })
    })

    it('应该拒绝错误密码', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'INVALID_CREDENTIALS',
          message: expect.any(String)
        }
      })
    })

    it('应该拒绝不存在的用户', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: testUser.password
        })
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'INVALID_CREDENTIALS',
          message: expect.any(String)
        }
      })
    })
  })

  describe('POST /api/auth/refresh', () => {
    let refreshToken: string

    beforeEach(async () => {
      // 注册并登录获取刷新令牌
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(testUser)

      refreshToken = registerResponse.body.data.tokens.refreshToken
    })

    it('应该成功刷新令牌', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String)
          }
        }
      })
    })

    it('应该拒绝无效的刷新令牌', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          message: expect.any(String)
        }
      })
    })
  })

  describe('POST /api/auth/logout', () => {
    let accessToken: string
    let refreshToken: string

    beforeEach(async () => {
      // 注册并登录获取令牌
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(testUser)

      accessToken = registerResponse.body.data.tokens.accessToken
      refreshToken = registerResponse.body.data.tokens.refreshToken
    })

    it('应该成功登出', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ refreshToken })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.any(String)
      })

      // 验证令牌已失效
      await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(401)
    })

    it('应该要求认证', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .send({ refreshToken })
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: expect.any(String)
        }
      })
    })
  })

  describe('GET /api/auth/me', () => {
    let accessToken: string

    beforeEach(async () => {
      // 注册并登录获取访问令牌
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(testUser)

      accessToken = registerResponse.body.data.tokens.accessToken
    })

    it('应该返回当前用户信息', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: {
            email: testUser.email,
            firstName: testUser.firstName,
            lastName: testUser.lastName,
            role: 'USER',
            isActive: true
          }
        }
      })
    })

    it('应该要求认证', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: expect.any(String)
        }
      })
    })

    it('应该拒绝无效令牌', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: expect.any(String)
        }
      })
    })
  })

  describe('POST /api/auth/forgot-password', () => {
    beforeEach(async () => {
      // 创建测试用户
      await request(app)
        .post('/api/auth/register')
        .send(testUser)
    })

    it('应该发送重置密码邮件', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: testUser.email })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.any(String)
      })
    })

    it('应该处理不存在的邮箱', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200)

      // 出于安全考虑，即使邮箱不存在也返回成功
      expect(response.body).toMatchObject({
        success: true,
        message: expect.any(String)
      })
    })
  })

  describe('POST /api/auth/reset-password', () => {
    let resetToken: string

    beforeEach(async () => {
      // 创建测试用户
      await request(app)
        .post('/api/auth/register')
        .send(testUser)

      // 请求重置密码获取令牌
      await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: testUser.email })

      // 模拟获取重置令牌（实际应用中通过邮件获取）
      resetToken = 'mock-reset-token'
    })

    it('应该成功重置密码', async () => {
      const newPassword = 'NewPassword123!'

      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          password: newPassword
        })
        .expect(200)

      expect(response.body).toMatchObject({
        success: true,
        message: expect.any(String)
      })

      // 验证可以用新密码登录
      await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: newPassword
        })
        .expect(200)
    })

    it('应该拒绝无效的重置令牌', async () => {
      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: 'invalid-token',
          password: 'NewPassword123!'
        })
        .expect(400)

      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'INVALID_RESET_TOKEN',
          message: expect.any(String)
        }
      })
    })
  })
})
