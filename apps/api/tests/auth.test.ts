import { test, expect, describe, beforeAll, afterAll } from '@jest/globals'
import { build } from '../src/app'
import { FastifyInstance } from 'fastify'

/**
 * 用户认证API测试
 * 测试用户注册、登录、登出等功能
 */

describe('用户认证API测试', () => {
  let app: FastifyInstance

  beforeAll(async () => {
    app = build({ logger: false })
    await app.ready()
  })

  afterAll(async () => {
    await app.close()
  })

  describe('POST /api/auth/register', () => {
    test('应该成功注册新用户', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/register',
        payload: {
          email: '<EMAIL>',
          password: 'password123',
          firstName: '测试',
          lastName: '用户'
        }
      })

      expect(response.statusCode).toBe(201)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(true)
      expect(body.data.user.email).toBe('<EMAIL>')
      expect(body.data.accessToken).toBeDefined()
      expect(body.data.refreshToken).toBeDefined()
    })

    test('应该拒绝重复邮箱注册', async () => {
      // 先注册一个用户
      await app.inject({
        method: 'POST',
        url: '/api/auth/register',
        payload: {
          email: '<EMAIL>',
          password: 'password123',
          firstName: '重复',
          lastName: '用户'
        }
      })

      // 尝试用相同邮箱再次注册
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/register',
        payload: {
          email: '<EMAIL>',
          password: 'password456',
          firstName: '另一个',
          lastName: '用户'
        }
      })

      expect(response.statusCode).toBe(409)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('EMAIL_EXISTS')
    })

    test('应该验证必填字段', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/register',
        payload: {
          email: 'invalid-email',
          password: '123' // 密码太短
        }
      })

      expect(response.statusCode).toBe(400)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('POST /api/auth/login', () => {
    beforeAll(async () => {
      // 创建测试用户
      await app.inject({
        method: 'POST',
        url: '/api/auth/register',
        payload: {
          email: '<EMAIL>',
          password: 'password123',
          firstName: '登录',
          lastName: '测试'
        }
      })
    })

    test('应该成功登录', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      })

      expect(response.statusCode).toBe(200)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(true)
      expect(body.data.user.email).toBe('<EMAIL>')
      expect(body.data.accessToken).toBeDefined()
      expect(body.data.refreshToken).toBeDefined()
    })

    test('应该拒绝错误密码', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'wrongpassword'
        }
      })

      expect(response.statusCode).toBe(401)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('INVALID_CREDENTIALS')
    })

    test('应该拒绝不存在的用户', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      })

      expect(response.statusCode).toBe(401)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('INVALID_CREDENTIALS')
    })
  })

  describe('POST /api/auth/refresh', () => {
    let refreshToken: string

    beforeAll(async () => {
      // 登录获取refresh token
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      })

      const loginBody = JSON.parse(loginResponse.body)
      refreshToken = loginBody.data.refreshToken
    })

    test('应该成功刷新令牌', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/refresh',
        payload: {
          refreshToken
        }
      })

      expect(response.statusCode).toBe(200)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(true)
      expect(body.data.accessToken).toBeDefined()
    })

    test('应该拒绝无效的refresh token', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/refresh',
        payload: {
          refreshToken: 'invalid-token'
        }
      })

      expect(response.statusCode).toBe(401)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('TOKEN_REFRESH_FAILED')
    })
  })

  describe('POST /api/auth/logout', () => {
    let accessToken: string

    beforeAll(async () => {
      // 登录获取access token
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      })

      const loginBody = JSON.parse(loginResponse.body)
      accessToken = loginBody.data.accessToken
    })

    test('应该成功登出', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/logout',
        headers: {
          authorization: `Bearer ${accessToken}`
        }
      })

      expect(response.statusCode).toBe(200)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(true)
      expect(body.message).toBe('登出成功')
    })

    test('应该拒绝无效的access token', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/logout',
        headers: {
          authorization: 'Bearer invalid-token'
        }
      })

      expect(response.statusCode).toBe(401)
      const body = JSON.parse(response.body)
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('INVALID_TOKEN')
    })
  })
})
