// Prisma 数据库种子文件
// 用于填充初始数据和测试数据

import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcrypt'

const prisma = new PrismaClient()

/**
 * 主种子函数
 */
async function main() {
  console.log('🌱 开始数据库种子填充...')

  try {
    // 清理现有数据（开发环境）
    if (process.env.NODE_ENV === 'development') {
      await cleanupExistingData()
    }

    // 创建系统配置
    await createSystemConfigs()

    // 创建用户数据
    await createUsers()

    // 创建订阅计划
    await createSubscriptionPlans()

    // 创建示例营销活动
    await createSampleCampaigns()

    // 创建示例内容
    await createSampleContent()

    // 创建示例AI生成历史
    await createSampleAIHistory()

    console.log('✅ 数据库种子填充完成')
  } catch (error) {
    console.error('❌ 数据库种子填充失败:', error)
    throw error
  }
}

/**
 * 清理现有数据
 */
async function cleanupExistingData() {
  console.log('🧹 清理现有数据...')

  // 按依赖关系顺序删除
  await prisma.aIGenerationHistory.deleteMany()
  await prisma.content.deleteMany()
  await prisma.campaign.deleteMany()
  await prisma.subscription.deleteMany()
  await prisma.subscriptionPlan.deleteMany()
  await prisma.user.deleteMany()
  await prisma.systemConfig.deleteMany()

  console.log('✅ 数据清理完成')
}

/**
 * 创建系统配置
 */
async function createSystemConfigs() {
  console.log('⚙️ 创建系统配置...')

  const configs = [
    {
      key: 'app_name',
      value: 'AI数字营销平台',
      category: 'general',
      description: '应用程序名称'
    },
    {
      key: 'app_version',
      value: '1.0.0',
      category: 'general',
      description: '应用程序版本'
    },
    {
      key: 'maintenance_mode',
      value: 'false',
      category: 'general',
      description: '维护模式开关'
    },
    {
      key: 'max_file_upload_size',
      value: '10485760',
      category: 'upload',
      description: '最大文件上传大小（字节）'
    },
    {
      key: 'email_verification_required',
      value: 'true',
      category: 'auth',
      description: '是否需要邮箱验证'
    },
    {
      key: 'openai_default_model',
      value: 'gpt-3.5-turbo',
      category: 'ai',
      description: 'OpenAI默认模型'
    }
  ]

  for (const config of configs) {
    await prisma.systemConfig.upsert({
      where: { key: config.key },
      update: config,
      create: config
    })
  }

  console.log('✅ 系统配置创建完成')
}

/**
 * 创建用户数据
 */
async function createUsers() {
  console.log('👥 创建用户数据...')

  // 创建管理员用户
  const adminPassword = await bcrypt.hash('admin123', 12)
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: '系统',
      lastName: '管理员',
      role: 'SUPER_ADMIN',
      status: 'ACTIVE',
      emailVerified: true,
      emailVerifiedAt: new Date(),
      timezone: 'Asia/Shanghai',
      language: 'zh-CN'
    }
  })

  // 创建测试用户
  const testPassword = await bcrypt.hash('test123', 12)
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: testPassword,
      firstName: '测试',
      lastName: '用户',
      role: 'USER',
      status: 'ACTIVE',
      emailVerified: true,
      emailVerifiedAt: new Date(),
      timezone: 'Asia/Shanghai',
      language: 'zh-CN'
    }
  })

  // 创建演示用户
  const demoPassword = await bcrypt.hash('demo123', 12)
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: demoPassword,
      firstName: '演示',
      lastName: '用户',
      role: 'USER',
      status: 'ACTIVE',
      emailVerified: true,
      emailVerifiedAt: new Date(),
      timezone: 'Asia/Shanghai',
      language: 'zh-CN'
    }
  })

  console.log('✅ 用户数据创建完成')
}

/**
 * 创建订阅计划
 */
async function createSubscriptionPlans() {
  console.log('💳 创建订阅计划...')

  const plans = [
    {
      name: '基础版',
      description: '适合个人用户和小型团队',
      price: 99.00,
      currency: 'CNY',
      interval: 'MONTHLY',
      features: {
        aiGenerations: 100,
        campaigns: 5,
        storage: '1GB',
        support: '邮件支持'
      },
      isActive: true
    },
    {
      name: '专业版',
      description: '适合中小型企业',
      price: 299.00,
      currency: 'CNY',
      interval: 'MONTHLY',
      features: {
        aiGenerations: 500,
        campaigns: 20,
        storage: '10GB',
        support: '优先支持'
      },
      isActive: true
    },
    {
      name: '企业版',
      description: '适合大型企业',
      price: 999.00,
      currency: 'CNY',
      interval: 'MONTHLY',
      features: {
        aiGenerations: -1, // 无限制
        campaigns: -1, // 无限制
        storage: '100GB',
        support: '专属客服'
      },
      isActive: true
    }
  ]

  for (const plan of plans) {
    await prisma.subscriptionPlan.upsert({
      where: { name: plan.name },
      update: plan,
      create: plan
    })
  }

  console.log('✅ 订阅计划创建完成')
}

/**
 * 创建示例营销活动
 */
async function createSampleCampaigns() {
  console.log('📢 创建示例营销活动...')

  const testUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (!testUser) return

  const campaigns = [
    {
      name: '春季新品推广',
      description: '推广春季新品系列，提升品牌知名度',
      type: 'EMAIL',
      status: 'RUNNING',
      budget: 5000.00,
      targetAudience: {
        ageRange: '25-45',
        interests: ['时尚', '购物'],
        location: '一线城市'
      },
      userId: testUser.id
    },
    {
      name: '会员专享活动',
      description: '针对VIP会员的专享优惠活动',
      type: 'SMS',
      status: 'SCHEDULED',
      budget: 2000.00,
      targetAudience: {
        memberLevel: 'VIP',
        purchaseHistory: '高价值客户'
      },
      userId: testUser.id
    }
  ]

  for (const campaign of campaigns) {
    await prisma.campaign.create({
      data: campaign
    })
  }

  console.log('✅ 示例营销活动创建完成')
}

/**
 * 创建示例内容
 */
async function createSampleContent() {
  console.log('📝 创建示例内容...')

  const testUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (!testUser) return

  const contents = [
    {
      title: '春季新品营销文案',
      content: '春暖花开，新品上市！我们精心打造的春季系列，融合了时尚与舒适，为您带来全新的穿搭体验。',
      type: 'TEXT',
      status: 'PUBLISHED',
      userId: testUser.id
    },
    {
      title: '产品宣传图片',
      content: 'https://example.com/spring-collection.jpg',
      type: 'IMAGE',
      status: 'PUBLISHED',
      userId: testUser.id
    }
  ]

  for (const content of contents) {
    await prisma.content.create({
      data: content
    })
  }

  console.log('✅ 示例内容创建完成')
}

/**
 * 创建示例AI生成历史
 */
async function createSampleAIHistory() {
  console.log('🤖 创建示例AI生成历史...')

  const testUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (!testUser) return

  const histories = [
    {
      type: 'TEXT_GENERATION',
      prompt: '为智能手机写一段营销文案',
      result: '全新智能手机，科技与美学的完美结合。超强性能，持久续航，让您的每一天都充满可能。',
      metadata: {
        model: 'gpt-3.5-turbo',
        tokensUsed: 150,
        generationTime: 2500
      },
      status: 'COMPLETED',
      userId: testUser.id
    },
    {
      type: 'IMAGE_GENERATION',
      prompt: '生成一张科技感的产品宣传图',
      result: 'https://example.com/generated-tech-image.jpg',
      metadata: {
        model: 'dall-e-3',
        size: '1024x1024',
        generationTime: 15000
      },
      status: 'COMPLETED',
      userId: testUser.id
    }
  ]

  for (const history of histories) {
    await prisma.aIGenerationHistory.create({
      data: history
    })
  }

  console.log('✅ 示例AI生成历史创建完成')
}

// 执行种子填充
main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
