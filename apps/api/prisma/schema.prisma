// Prisma数据库模式文件
// 定义数据库表结构和关系

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id                String   @id @default(cuid())
  email             String   @unique
  emailVerified     Boolean  @default(false)
  emailVerifiedAt   DateTime?
  password          String
  firstName         String?
  lastName          String?
  avatar            String?
  phone             String?
  timezone          String   @default("UTC")
  language          String   @default("zh-CN")
  status            UserStatus @default(ACTIVE)
  lastLoginAt       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // 关联关系
  profile           UserProfile?
  subscriptions     Subscription[]
  campaigns         Campaign[]
  contents          Content[]
  personas          UserPersona[]
  reports           AnalyticsReport[]
  sessions          UserSession[]
  
  @@map("users")
}

// 用户状态枚举
enum UserStatus {
  ACTIVE    // 活跃
  INACTIVE  // 非活跃
  SUSPENDED // 暂停
  DELETED   // 已删除
}

// 用户配置表
model UserProfile {
  id            String   @id @default(cuid())
  userId        String   @unique
  companyName   String?
  industry      String?
  companySize   String?
  websiteUrl    String?
  description   String?
  preferences   Json?    // 用户偏好设置
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_profiles")
}

// 用户会话表
model UserSession {
  id            String   @id @default(cuid())
  userId        String
  token         String   @unique
  refreshToken  String   @unique
  expiresAt     DateTime
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_sessions")
}

// 订阅表
model Subscription {
  id                    String            @id @default(cuid())
  userId                String
  planId                String
  status                SubscriptionStatus @default(ACTIVE)
  currentPeriodStart    DateTime
  currentPeriodEnd      DateTime
  stripeSubscriptionId  String?           @unique
  stripeCustomerId      String?
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // 关联关系
  user                  User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("subscriptions")
}

// 订阅状态枚举
enum SubscriptionStatus {
  ACTIVE      // 活跃
  INACTIVE    // 非活跃
  CANCELLED   // 已取消
  EXPIRED     // 已过期
  TRIAL       // 试用期
}

// 营销活动表
model Campaign {
  id              String         @id @default(cuid())
  userId          String
  name            String
  description     String?
  type            CampaignType
  status          CampaignStatus @default(DRAFT)
  targetAudience  Json?          // 目标受众配置
  content         Json?          // 活动内容
  schedule        Json?          // 调度配置
  budget          Decimal?       @db.Decimal(10, 2)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // 关联关系
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  contents        Content[]
  executions      CampaignExecution[]
  reports         AnalyticsReport[]
  
  @@map("campaigns")
}

// 营销活动类型枚举
enum CampaignType {
  EMAIL     // 邮件营销
  SMS       // 短信营销
  SOCIAL    // 社交媒体
  PUSH      // 推送通知
  DISPLAY   // 展示广告
  SEARCH    // 搜索广告
}

// 营销活动状态枚举
enum CampaignStatus {
  DRAFT     // 草稿
  ACTIVE    // 活跃
  PAUSED    // 暂停
  COMPLETED // 已完成
  CANCELLED // 已取消
}

// 内容表
model Content {
  id            String        @id @default(cuid())
  userId        String
  campaignId    String?
  type          ContentType
  title         String?
  content       String?
  metadata      Json?         // 内容元数据
  aiGenerated   Boolean       @default(false)
  status        ContentStatus @default(DRAFT)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // 关联关系
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaign      Campaign?     @relation(fields: [campaignId], references: [id], onDelete: SetNull)
  
  @@map("contents")
}

// 内容类型枚举
enum ContentType {
  TEXT    // 文本
  IMAGE   // 图片
  VIDEO   // 视频
  AUDIO   // 音频
  HTML    // HTML
}

// 内容状态枚举
enum ContentStatus {
  DRAFT     // 草稿
  PUBLISHED // 已发布
  ARCHIVED  // 已归档
  DELETED   // 已删除
}

// 营销活动执行记录表
model CampaignExecution {
  id            String   @id @default(cuid())
  campaignId    String
  executionTime DateTime
  status        ExecutionStatus
  metrics       Json?    // 执行指标
  errorMessage  String?
  createdAt     DateTime @default(now())

  // 关联关系
  campaign      Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  
  @@map("campaign_executions")
}

// 执行状态枚举
enum ExecutionStatus {
  PENDING   // 待执行
  RUNNING   // 执行中
  SUCCESS   // 成功
  FAILED    // 失败
  CANCELLED // 已取消
}

// 用户画像表
model UserPersona {
  id            String   @id @default(cuid())
  userId        String
  name          String
  description   String?
  demographics  Json?    // 人口统计数据
  interests     Json?    // 兴趣偏好
  behaviors     Json?    // 行为数据
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_personas")
}

// 数据分析报告表
model AnalyticsReport {
  id          String     @id @default(cuid())
  userId      String
  campaignId  String?
  reportType  ReportType
  data        Json       // 报告数据
  generatedAt DateTime   @default(now())

  // 关联关系
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaign    Campaign?  @relation(fields: [campaignId], references: [id], onDelete: SetNull)
  
  @@map("analytics_reports")
}

// 报告类型枚举
enum ReportType {
  DAILY     // 日报
  WEEKLY    // 周报
  MONTHLY   // 月报
  CAMPAIGN  // 活动报告
  CUSTOM    // 自定义报告
}

// AI生成历史表
model AIGenerationHistory {
  id          String           @id @default(cuid())
  userId      String
  type        AIGenerationType
  prompt      String
  result      String?
  metadata    Json?            // 生成参数和元数据
  status      GenerationStatus @default(PENDING)
  errorMessage String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  
  @@map("ai_generation_history")
}

// AI生成类型枚举
enum AIGenerationType {
  TEXT_GENERATION   // 文本生成
  IMAGE_GENERATION  // 图片生成
  VIDEO_GENERATION  // 视频生成
  CONTENT_OPTIMIZATION // 内容优化
}

// 生成状态枚举
enum GenerationStatus {
  PENDING   // 待处理
  PROCESSING // 处理中
  COMPLETED // 已完成
  FAILED    // 失败
}

// 系统配置表
model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  category  String?
  description String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("system_configs")
}

// 操作日志表
model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  resource  String?
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  
  @@map("audit_logs")
}

// 文件上传表
model FileUpload {
  id        String   @id @default(cuid())
  userId    String?
  filename  String
  originalName String
  mimeType  String
  size      Int
  path      String
  url       String?
  createdAt DateTime @default(now())
  
  @@map("file_uploads")
}

// 通知表
model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  data      Json?
  read      Boolean          @default(false)
  readAt    DateTime?
  createdAt DateTime         @default(now())
  
  @@map("notifications")
}

// 通知类型枚举
enum NotificationType {
  SYSTEM    // 系统通知
  CAMPAIGN  // 活动通知
  PAYMENT   // 支付通知
  SECURITY  // 安全通知
  MARKETING // 营销通知
}

// 索引定义
// 用户表索引
@@index([User.email])
@@index([User.status])
@@index([User.createdAt])

// 营销活动表索引
@@index([Campaign.userId])
@@index([Campaign.status])
@@index([Campaign.type])
@@index([Campaign.createdAt])

// 内容表索引
@@index([Content.userId])
@@index([Content.campaignId])
@@index([Content.type])
@@index([Content.status])

// 分析报告表索引
@@index([AnalyticsReport.userId])
@@index([AnalyticsReport.campaignId])
@@index([AnalyticsReport.reportType])
@@index([AnalyticsReport.generatedAt])

// AI生成历史表索引
@@index([AIGenerationHistory.userId])
@@index([AIGenerationHistory.type])
@@index([AIGenerationHistory.status])
@@index([AIGenerationHistory.createdAt])
