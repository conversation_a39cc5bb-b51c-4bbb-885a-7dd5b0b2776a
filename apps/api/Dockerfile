# AI数字营销平台 API服务 Dockerfile
# 多阶段构建，优化镜像大小和安全性

# ===========================================
# 基础镜像阶段
# ===========================================
FROM node:20-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    libc6-compat \
    openssl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# 设置环境变量
ENV NODE_ENV=production
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# 安装pnpm
RUN corepack enable

# ===========================================
# 依赖安装阶段
# ===========================================
FROM base AS deps

# 复制包管理文件
COPY package.json package-lock.json* ./
COPY apps/api/package.json ./apps/api/

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# ===========================================
# 构建阶段
# ===========================================
FROM base AS builder

# 复制源代码和依赖
COPY . .
COPY --from=deps /app/node_modules ./node_modules

# 设置构建环境变量
ENV NODE_ENV=production

# 生成Prisma客户端
RUN cd apps/api && npx prisma generate

# 构建应用
RUN npm run build --workspace=apps/api

# ===========================================
# 生产运行阶段
# ===========================================
FROM base AS runner

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 apiuser

# 设置工作目录
WORKDIR /app

# 复制必要文件
COPY --from=builder /app/apps/api/dist ./dist
COPY --from=builder /app/apps/api/package.json ./package.json
COPY --from=builder /app/apps/api/prisma ./prisma
COPY --from=deps /app/node_modules ./node_modules

# 创建必要目录
RUN mkdir -p logs uploads && \
    chown -R apiuser:nodejs logs uploads

# 切换到非root用户
USER apiuser

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动命令
CMD ["node", "dist/index.js"]

# ===========================================
# 开发环境阶段
# ===========================================
FROM base AS development

# 安装开发依赖
RUN npm install -g nodemon tsx

# 复制源代码
COPY . .

# 安装所有依赖（包括开发依赖）
RUN npm ci

# 生成Prisma客户端
RUN cd apps/api && npx prisma generate

# 切换到API目录
WORKDIR /app/apps/api

# 暴露端口
EXPOSE 3001

# 开发环境启动命令
CMD ["npm", "run", "dev"]
