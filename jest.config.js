// Jest 配置文件 - 根目录
// 用于整个monorepo的测试配置

module.exports = {
  // 项目配置 - 为每个应用单独配置
  projects: [
    {
      displayName: 'API',
      testMatch: ['<rootDir>/apps/api/tests/**/*.test.{js,ts}'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/apps/api/tests/setup.ts'],
      collectCoverageFrom: [
        '<rootDir>/apps/api/src/**/*.{js,ts}',
        '!<rootDir>/apps/api/src/**/*.d.ts',
        '!<rootDir>/apps/api/src/index.ts',
      ],
      coverageDirectory: '<rootDir>/coverage/api',
      coverageReporters: ['text', 'lcov', 'html'],
      transform: {
        '^.+\\.ts$': 'ts-jest',
      },
      moduleFileExtensions: ['ts', 'js', 'json'],
      testTimeout: 30000,
    },
    {
      displayName: 'Web',
      testMatch: ['<rootDir>/apps/web/**/*.test.{js,ts,tsx}'],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/apps/web/tests/setup.ts'],
      collectCoverageFrom: [
        '<rootDir>/apps/web/**/*.{js,ts,tsx}',
        '!<rootDir>/apps/web/**/*.d.ts',
        '!<rootDir>/apps/web/.next/**',
        '!<rootDir>/apps/web/node_modules/**',
      ],
      coverageDirectory: '<rootDir>/coverage/web',
      coverageReporters: ['text', 'lcov', 'html'],
      transform: {
        '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
      },
      moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/apps/web/$1',
      },
      testTimeout: 30000,
    },
  ],

  // 全局配置
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },

  // 忽略的文件和目录
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
    '<rootDir>/dist/',
    '<rootDir>/build/',
  ],

  // 模块路径映射
  modulePathIgnorePatterns: [
    '<rootDir>/dist/',
    '<rootDir>/build/',
  ],

  // 全局设置文件
  globalSetup: '<rootDir>/tests/global-setup.ts',
  globalTeardown: '<rootDir>/tests/global-teardown.ts',

  // 测试结果处理器
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/coverage',
        outputName: 'junit.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true,
      },
    ],
  ],

  // 详细输出
  verbose: true,

  // 最大并发数
  maxWorkers: '50%',

  // 缓存配置
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',

  // 清除模拟
  clearMocks: true,
  restoreMocks: true,

  // 错误时停止
  bail: false,

  // 强制退出
  forceExit: true,

  // 检测打开的句柄
  detectOpenHandles: true,

  // 检测泄漏
  detectLeaks: false,
};
