# AI数字营销平台环境变量配置模板
# 复制此文件为 .env 并填入实际的配置值

# ===========================================
# 基础环境配置
# ===========================================
NODE_ENV=development
PORT=3001
HOST=0.0.0.0

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL 主数据库
DATABASE_URL="postgresql://postgres:postgres123@localhost:5432/ai_marketing"

# Redis 缓存数据库
REDIS_URL="redis://:redis123@localhost:6379"
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# MongoDB 文档数据库
MONGODB_URL="**********************************************************************"

# Elasticsearch 搜索引擎
ELASTICSEARCH_URL="http://localhost:9200"

# ===========================================
# JWT 认证配置
# ===========================================
# JWT 密钥（至少32位字符）
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_EXPIRES_IN="30d"

# ===========================================
# OpenAI API 配置
# ===========================================
# OpenAI API 密钥
OPENAI_API_KEY="sk-your-openai-api-key-here"
# OpenAI 组织ID（可选）
OPENAI_ORGANIZATION=""

# ===========================================
# Stripe 支付配置
# ===========================================
# Stripe 密钥
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key-here"
# Stripe Webhook 密钥
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret-here"
# Stripe 公钥
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key-here"

# ===========================================
# 邮件服务配置
# ===========================================
# SMTP 服务器配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
SMTP_FROM="AI数字营销平台 <<EMAIL>>"

# ===========================================
# 文件存储配置
# ===========================================
# 上传目录
UPLOAD_DIR="./uploads"
# 最大文件大小（字节）
MAX_FILE_SIZE=10485760

# MinIO 对象存储配置
MINIO_ENDPOINT="localhost"
MINIO_PORT=9000
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin123"
MINIO_BUCKET="ai-marketing"

# ===========================================
# 安全配置
# ===========================================
# bcrypt 加密轮数
BCRYPT_ROUNDS=12
# API 限流配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# ===========================================
# 日志配置
# ===========================================
# 日志级别
LOG_LEVEL="info"
# 日志文件路径
LOG_FILE="./logs/app.log"

# ===========================================
# 前端应用配置
# ===========================================
# 前端应用URL
FRONTEND_URL="http://localhost:3000"
# API 路径前缀
API_PREFIX="/api/v1"
# API 文档路径
API_DOCS_PATH="/docs"

# ===========================================
# 监控配置
# ===========================================
# 是否启用指标监控
ENABLE_METRICS=true
# 指标路径
METRICS_PATH="/metrics"

# ===========================================
# 第三方服务配置
# ===========================================
# 微信公众号配置
WECHAT_APP_ID=""
WECHAT_APP_SECRET=""

# 钉钉机器人配置
DINGTALK_WEBHOOK=""
DINGTALK_SECRET=""

# Slack 配置
SLACK_WEBHOOK=""
SLACK_TOKEN=""

# ===========================================
# 开发环境特定配置
# ===========================================
# 是否启用调试模式
DEBUG=true
# 是否启用热重载
HOT_RELOAD=true
# 是否启用 API 文档
ENABLE_DOCS=true

# ===========================================
# 生产环境特定配置
# ===========================================
# 域名配置
DOMAIN="ai-marketing.com"
# SSL 证书路径
SSL_CERT_PATH="/etc/ssl/certs/ai-marketing.crt"
SSL_KEY_PATH="/etc/ssl/private/ai-marketing.key"

# CDN 配置
CDN_URL="https://cdn.ai-marketing.com"

# ===========================================
# 数据库备份配置
# ===========================================
# 备份存储路径
BACKUP_PATH="./backups"
# 备份保留天数
BACKUP_RETENTION_DAYS=30
# 自动备份间隔（小时）
BACKUP_INTERVAL_HOURS=24

# ===========================================
# 缓存配置
# ===========================================
# 缓存过期时间（秒）
CACHE_TTL=3600
# 缓存键前缀
CACHE_PREFIX="ai-marketing:"

# ===========================================
# 队列配置
# ===========================================
# 队列名称前缀
QUEUE_PREFIX="ai-marketing-queue:"
# 队列并发数
QUEUE_CONCURRENCY=5
# 任务重试次数
QUEUE_MAX_RETRIES=3

# ===========================================
# AI 服务配置
# ===========================================
# AI 内容生成超时时间（毫秒）
AI_GENERATION_TIMEOUT=30000
# AI 服务最大并发数
AI_MAX_CONCURRENT_REQUESTS=10

# ===========================================
# 营销服务配置
# ===========================================
# 邮件发送批次大小
EMAIL_BATCH_SIZE=100
# 短信发送批次大小
SMS_BATCH_SIZE=50
# 营销活动最大并发数
CAMPAIGN_MAX_CONCURRENT=5

# ===========================================
# 分析服务配置
# ===========================================
# 数据分析批次大小
ANALYTICS_BATCH_SIZE=1000
# 报告生成超时时间（毫秒）
REPORT_GENERATION_TIMEOUT=60000

# ===========================================
# 外部API配置
# ===========================================
# 百度AI API
BAIDU_AI_API_KEY=""
BAIDU_AI_SECRET_KEY=""

# 腾讯云API
TENCENT_SECRET_ID=""
TENCENT_SECRET_KEY=""

# 阿里云API
ALIYUN_ACCESS_KEY_ID=""
ALIYUN_ACCESS_KEY_SECRET=""

# ===========================================
# 社交媒体API配置
# ===========================================
# 微博API
WEIBO_APP_KEY=""
WEIBO_APP_SECRET=""

# 抖音API
DOUYIN_CLIENT_KEY=""
DOUYIN_CLIENT_SECRET=""

# Facebook API
FACEBOOK_APP_ID=""
FACEBOOK_APP_SECRET=""

# Twitter API
TWITTER_API_KEY=""
TWITTER_API_SECRET=""
TWITTER_ACCESS_TOKEN=""
TWITTER_ACCESS_TOKEN_SECRET=""

# LinkedIn API
LINKEDIN_CLIENT_ID=""
LINKEDIN_CLIENT_SECRET=""

# ===========================================
# 短信服务配置
# ===========================================
# 阿里云短信
ALIYUN_SMS_ACCESS_KEY_ID=""
ALIYUN_SMS_ACCESS_KEY_SECRET=""
ALIYUN_SMS_SIGN_NAME=""

# 腾讯云短信
TENCENT_SMS_SDK_APP_ID=""
TENCENT_SMS_SECRET_ID=""
TENCENT_SMS_SECRET_KEY=""

# ===========================================
# 推送服务配置
# ===========================================
# 极光推送
JPUSH_APP_KEY=""
JPUSH_MASTER_SECRET=""

# 个推
GETUI_APP_ID=""
GETUI_APP_KEY=""
GETUI_MASTER_SECRET=""

# ===========================================
# 地图服务配置
# ===========================================
# 高德地图API
AMAP_API_KEY=""

# 百度地图API
BAIDU_MAP_API_KEY=""

# ===========================================
# 支付服务配置
# ===========================================
# 支付宝配置
ALIPAY_APP_ID=""
ALIPAY_PRIVATE_KEY=""
ALIPAY_PUBLIC_KEY=""

# 微信支付配置
WECHAT_PAY_MCH_ID=""
WECHAT_PAY_API_KEY=""
WECHAT_PAY_CERT_PATH=""
WECHAT_PAY_KEY_PATH=""
